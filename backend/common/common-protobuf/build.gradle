plugins {
    id 'java-library'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'com.google.protobuf' version '0.9.4'
}

dependencies {
    // 依赖核心模块
    api project(':common:common-core')
    
    // Protocol Buffers 核心依赖
    api 'com.google.protobuf:protobuf-java:4.31.1'
    api 'com.google.protobuf:protobuf-java-util:4.31.1'
    
    // gRPC 支持 (为未来的服务间通信预留)
    api 'io.grpc:grpc-protobuf:1.73.0'
    api 'io.grpc:grpc-stub:1.73.0'
    api 'io.grpc:grpc-netty-shaded:1.73.0'
    
    // JSON 转换工具
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // 对象池化支持 (性能优化)
    implementation 'org.apache.commons:commons-pool2:2.12.0'
    
    // 压缩支持
    implementation 'org.apache.commons:commons-compress:1.24.0'
    
    // Spring Boot 集成
    implementation 'org.springframework.boot:spring-boot-starter'

    // Actuator 支持 - 改为 api 依赖以确保传递给使用方
    api 'org.springframework.boot:spring-boot-starter-actuator'
    api 'org.springframework.boot:spring-boot-actuator'
    api 'org.springframework.boot:spring-boot-actuator-autoconfigure'

    // Micrometer 指标支持
    api 'io.micrometer:micrometer-core'
    
    // 测试依赖
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.assertj:assertj-core'
    testImplementation 'org.mockito:mockito-core'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2024.0.0"
    }
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.25.1'
    }
    
    plugins {
        grpc {
            artifact = 'io.grpc:protoc-gen-grpc-java:1.60.0'
        }
    }
    
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {
                    // 使用标准运行时以获得完整功能
                    // option 'lite' 可以在需要时启用以提高性能
                }
            }
            task.plugins {
                grpc {
                    // 生成 gRPC 服务代码
                }
            }
        }
    }
}

// 确保 proto 文件包含在 JAR 中
jar {
    enabled = true
    archiveClassifier = ''
    
    from('src/main/proto') {
        into 'proto'
    }
    
    // 包含生成的源代码
    from sourceSets.main.java.srcDirs
}

// 禁用 bootJar 任务，因为这是一个库模块
bootJar {
    enabled = false
}

// 确保在编译前生成 protobuf 代码
compileJava.dependsOn generateProto

// 将生成的代码添加到源集
sourceSets {
    main {
        java {
            srcDirs 'build/generated/source/proto/main/java'
            srcDirs 'build/generated/source/proto/main/grpc'
            // 重新启用所有功能，依赖问题已解决
        }
    }
}

// 清理任务
clean {
    delete 'build/generated'
}

// 测试配置
test {
    useJUnitPlatform()
    
    // 测试时的 JVM 参数
    jvmArgs = [
        '-Dfile.encoding=UTF-8',
        '-Djava.awt.headless=true'
    ]
    
    // 测试报告
    reports {
        html.required = true
        junitXml.required = true
    }
}

// 编译配置
tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    options.compilerArgs += ['-parameters']
    
    // 忽略生成代码的警告
    options.compilerArgs += ['-Xlint:-unchecked', '-Xlint:-rawtypes']
}

// 文档生成
javadoc {
    options.encoding = 'UTF-8'
    options.charSet = 'UTF-8'
    
    // 排除生成的代码
    exclude '**/proto/**'
}
