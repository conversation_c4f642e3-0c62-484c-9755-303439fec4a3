package com.implatform.realtime.repository;

import com.implatform.realtime.entity.MessageReactionStats;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 消息表情回应统计Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface MessageReactionStatsRepository extends R2dbcRepository<MessageReactionStats, Long> {

    /**
     * 根据消息ID查找统计信息
     */
    Optional<MessageReactionStats> findByMessageId(Long messageId);

    /**
     * 检查消息是否有表情回应统计
     */
    boolean existsByMessageId(Long messageId);

    /**
     * 查找有表情回应的消息统计
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.totalReactions > 0 " +
           "ORDER BY s.totalReactions DESC")
    Page<MessageReactionStats> findMessagesWithReactions(Pageable pageable);

    /**
     * 查找热门消息（基于表情回应数量）
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.totalReactions >= :minReactions " +
           "ORDER BY s.totalReactions DESC")
    List<MessageReactionStats> findPopularMessages(@Param("minReactions") Integer minReactions, Pageable pageable);

    /**
     * 查找最近有表情回应的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.lastReactionAt >= :since " +
           "ORDER BY s.lastReactionAt DESC")
    List<MessageReactionStats> findRecentlyReactedMessages(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 统计总表情回应数量
     */
    @Query("SELECT SUM(s.totalReactions) FROM MessageReactionStats s")
    Long getTotalReactionsCount();

    /**
     * 统计有表情回应的消息数量
     */
    @Query("SELECT COUNT(s) FROM MessageReactionStats s WHERE s.totalReactions > 0")
    Long getMessagesWithReactionsCount();

    /**
     * 获取平均表情回应数量
     */
    @Query("SELECT AVG(s.totalReactions) FROM MessageReactionStats s WHERE s.totalReactions > 0")
    Double getAverageReactionsPerMessage();

    /**
     * 查找参与度最高的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.uniqueUsers > 0 " +
           "ORDER BY (CAST(s.uniqueUsers AS double) / CAST(s.totalReactions AS double)) DESC")
    List<MessageReactionStats> findHighEngagementMessages(Pageable pageable);

    /**
     * 查找指定时间段内的表情回应统计
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.lastReactionAt BETWEEN :startTime AND :endTime " +
           "ORDER BY s.lastReactionAt DESC")
    List<MessageReactionStats> findStatsBetweenTime(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查找使用特定表情回应最多的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.mostPopularReaction = :reaction " +
           "ORDER BY s.totalReactions DESC")
    List<MessageReactionStats> findMessagesByPopularReaction(@Param("reaction") String reaction, Pageable pageable);

    /**
     * 统计表情回应趋势
     */
    @Query("SELECT DATE(s.lastReactionAt) as date, SUM(s.totalReactions) as totalReactions, " +
           "COUNT(s) as messagesCount, AVG(s.totalReactions) as avgReactions " +
           "FROM MessageReactionStats s WHERE s.lastReactionAt >= :since " +
           "GROUP BY DATE(s.lastReactionAt) ORDER BY date DESC")
    List<Object[]> getReactionTrends(@Param("since") LocalDateTime since);

    /**
     * 查找表情回应数量在指定范围内的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.totalReactions BETWEEN :minReactions AND :maxReactions " +
           "ORDER BY s.totalReactions DESC")
    List<MessageReactionStats> findMessagesByReactionRange(@Param("minReactions") Integer minReactions,
                                                          @Param("maxReactions") Integer maxReactions,
                                                          Pageable pageable);

    /**
     * 查找独立用户数量最多的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.uniqueUsers > 0 " +
           "ORDER BY s.uniqueUsers DESC")
    List<MessageReactionStats> findMessagesByUniqueUsers(Pageable pageable);

    /**
     * 统计最受欢迎的表情回应
     */
    @Query("SELECT s.mostPopularReaction, COUNT(s) as count " +
           "FROM MessageReactionStats s WHERE s.mostPopularReaction IS NOT NULL " +
           "GROUP BY s.mostPopularReaction ORDER BY count DESC")
    List<Object[]> getMostPopularReactions(Pageable pageable);

    /**
     * 查找长时间没有新表情回应的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.lastReactionAt < :cutoffTime " +
           "AND s.totalReactions > 0 ORDER BY s.lastReactionAt ASC")
    List<MessageReactionStats> findStaleReactionMessages(@Param("cutoffTime") LocalDateTime cutoffTime, Pageable pageable);

    /**
     * 统计表情回应活跃度
     */
    @Query("SELECT " +
           "COUNT(s) as totalMessages, " +
           "COUNT(CASE WHEN s.totalReactions > 0 THEN 1 END) as messagesWithReactions, " +
           "SUM(s.totalReactions) as totalReactions, " +
           "SUM(s.uniqueUsers) as totalUniqueUsers, " +
           "AVG(s.totalReactions) as avgReactions " +
           "FROM MessageReactionStats s WHERE s.lastReactionAt >= :since")
    Object[] getReactionActivity(@Param("since") LocalDateTime since);

    /**
     * 查找表情回应增长最快的消息
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.lastReactionAt >= :since " +
           "ORDER BY s.totalReactions DESC, s.lastReactionAt DESC")
    List<MessageReactionStats> findFastGrowingMessages(@Param("since") LocalDateTime since, Pageable pageable);

    /**
     * 删除没有表情回应的统计记录
     */
    @Query("DELETE FROM MessageReactionStats s WHERE s.totalReactions = 0")
    int deleteEmptyStats();

    /**
     * 查找需要更新的统计记录
     */
    @Query("SELECT s FROM MessageReactionStats s WHERE s.updatedAt < :cutoffTime")
    List<MessageReactionStats> findStatsNeedingUpdate(@Param("cutoffTime") LocalDateTime cutoffTime, Pageable pageable);
}
