package com.implatform.realtime.repository;

import com.implatform.realtime.entity.PollOption;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 投票选项Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface PollOptionRepository extends R2dbcRepository<PollOption, Long> {

    /**
     * 根据投票ID查找所有选项
     */
    List<PollOption> findByPollIdOrderByOptionOrderAsc(Long pollId);

    /**
     * 根据投票ID和选项顺序查找选项
     */
    Optional<PollOption> findByPollIdAndOptionOrder(Long pollId, Integer optionOrder);

    /**
     * 查找投票的正确答案选项
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.isCorrect = true")
    List<PollOption> findCorrectOptionsByPollId(@Param("pollId") Long pollId);

    /**
     * 查找投票中得票最多的选项
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId ORDER BY o.voterCount DESC LIMIT 1")
    Optional<PollOption> findMostPopularOption(@Param("pollId") Long pollId);

    /**
     * 查找投票中得票最少的选项
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.voterCount > 0 ORDER BY o.voterCount ASC LIMIT 1")
    Optional<PollOption> findLeastPopularOption(@Param("pollId") Long pollId);

    /**
     * 统计投票的选项数量
     */
    @Query("SELECT COUNT(o) FROM PollOption o WHERE o.pollId = :pollId")
    Long countByPollId(@Param("pollId") Long pollId);

    /**
     * 查找有投票的选项
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.voterCount > 0 ORDER BY o.voterCount DESC")
    List<PollOption> findOptionsWithVotes(@Param("pollId") Long pollId);

    /**
     * 查找没有投票的选项
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.voterCount = 0 ORDER BY o.optionOrder ASC")
    List<PollOption> findOptionsWithoutVotes(@Param("pollId") Long pollId);

    /**
     * 获取投票选项的统计信息
     */
    @Query("SELECT " +
           "COUNT(o) as totalOptions, " +
           "COUNT(CASE WHEN o.voterCount > 0 THEN 1 END) as optionsWithVotes, " +
           "MAX(o.voterCount) as maxVotes, " +
           "AVG(o.voterCount) as avgVotes " +
           "FROM PollOption o WHERE o.pollId = :pollId")
    Object[] getOptionStatistics(@Param("pollId") Long pollId);

    /**
     * 查找热门选项（得票率高）
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.percentage >= :minPercentage " +
           "ORDER BY o.percentage DESC")
    List<PollOption> findPopularOptions(@Param("pollId") Long pollId, @Param("minPercentage") Double minPercentage);

    /**
     * 查找冷门选项（得票率低但有投票）
     */
    @Query("SELECT o FROM PollOption o WHERE o.pollId = :pollId AND o.voterCount > 0 AND o.percentage <= :maxPercentage " +
           "ORDER BY o.percentage ASC")
    List<PollOption> findUnpopularOptions(@Param("pollId") Long pollId, @Param("maxPercentage") Double maxPercentage);
}
