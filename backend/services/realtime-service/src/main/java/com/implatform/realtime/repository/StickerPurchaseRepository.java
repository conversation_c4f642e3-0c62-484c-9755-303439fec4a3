package com.implatform.realtime.repository;

import com.implatform.realtime.entity.StickerPurchase;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 表情包购买记录Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface StickerPurchaseRepository extends R2dbcRepository<StickerPurchase, Long> {

    /**
     * 根据用户ID和表情包ID查找购买记录
     */
    Optional<StickerPurchase> findByUserIdAndPackId(Long userId, Long packId);

    /**
     * 检查用户是否已购买表情包
     */
    boolean existsByUserIdAndPackIdAndStatus(Long userId, Long packId, StickerPurchase.PurchaseStatus status);

    /**
     * 根据用户ID查找购买记录
     */
    Page<StickerPurchase> findByUserIdOrderByPurchasedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找购买记录
     */
    Page<StickerPurchase> findByUserIdAndStatusOrderByPurchasedAtDesc(Long userId, StickerPurchase.PurchaseStatus status, Pageable pageable);

    /**
     * 根据表情包ID查找购买记录
     */
    Page<StickerPurchase> findByPackIdOrderByPurchasedAtDesc(Long packId, Pageable pageable);

    /**
     * 根据商店商品ID查找购买记录
     */
    Page<StickerPurchase> findByStoreItemIdOrderByPurchasedAtDesc(Long storeItemId, Pageable pageable);

    /**
     * 根据购买类型查找记录
     */
    Page<StickerPurchase> findByPurchaseTypeOrderByPurchasedAtDesc(StickerPurchase.PurchaseType purchaseType, Pageable pageable);

    /**
     * 根据交易ID查找购买记录
     */
    Optional<StickerPurchase> findByTransactionId(String transactionId);

    /**
     * 统计用户购买次数
     */
    @Query("SELECT COUNT(p) FROM StickerPurchase p WHERE p.userId = :userId AND p.status = :status")
    Long countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 统计表情包购买次数
     */
    @Query("SELECT COUNT(p) FROM StickerPurchase p WHERE p.packId = :packId AND p.status = :status")
    Long countByPackIdAndStatus(@Param("packId") Long packId, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 计算用户总消费金额
     */
    @Query("SELECT SUM(p.price) FROM StickerPurchase p WHERE p.userId = :userId AND p.status = :status")
    BigDecimal sumPriceByUserIdAndStatus(@Param("userId") Long userId, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 计算表情包总收入
     */
    @Query("SELECT SUM(p.price) FROM StickerPurchase p WHERE p.packId = :packId AND p.status = :status")
    BigDecimal sumPriceByPackIdAndStatus(@Param("packId") Long packId, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 根据时间范围查找购买记录
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.purchasedAt BETWEEN :startDate AND :endDate ORDER BY p.purchasedAt DESC")
    Page<StickerPurchase> findByPurchasedAtBetween(@Param("startDate") Instant startDate, 
                                                   @Param("endDate") Instant endDate, 
                                                   Pageable pageable);

    /**
     * 统计时间范围内的购买数量
     */
    @Query("SELECT COUNT(p) FROM StickerPurchase p WHERE p.purchasedAt BETWEEN :startDate AND :endDate AND p.status = :status")
    Long countByPurchasedAtBetweenAndStatus(@Param("startDate") Instant startDate, 
                                           @Param("endDate") Instant endDate, 
                                           @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 统计时间范围内的收入
     */
    @Query("SELECT SUM(p.price) FROM StickerPurchase p WHERE p.purchasedAt BETWEEN :startDate AND :endDate AND p.status = :status")
    BigDecimal sumRevenueByDateRangeAndStatus(@Param("startDate") Instant startDate, 
                                             @Param("endDate") Instant endDate, 
                                             @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 查找可退款的购买记录
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.status = :status AND p.purchasedAt > :cutoffDate")
    List<StickerPurchase> findRefundableRecords(@Param("status") StickerPurchase.PurchaseStatus status, 
                                               @Param("cutoffDate") Instant cutoffDate);

    /**
     * 根据支付方式统计
     */
    @Query("SELECT p.paymentMethod, COUNT(p), SUM(p.price) FROM StickerPurchase p WHERE p.status = :status GROUP BY p.paymentMethod")
    List<Object[]> getStatisticsByPaymentMethod(@Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 根据购买类型统计
     */
    @Query("SELECT p.purchaseType, COUNT(p), SUM(p.price) FROM StickerPurchase p WHERE p.status = :status GROUP BY p.purchaseType")
    List<Object[]> getStatisticsByPurchaseType(@Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 获取热门表情包（按购买次数排序）
     */
    @Query("SELECT p.packId, COUNT(p) as purchaseCount FROM StickerPurchase p WHERE p.status = :status GROUP BY p.packId ORDER BY purchaseCount DESC")
    Page<Object[]> findPopularPacks(@Param("status") StickerPurchase.PurchaseStatus status, Pageable pageable);

    /**
     * 获取用户最近购买的表情包
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.userId = :userId AND p.status = :status ORDER BY p.purchasedAt DESC")
    Page<StickerPurchase> findRecentPurchasesByUser(@Param("userId") Long userId, 
                                                   @Param("status") StickerPurchase.PurchaseStatus status, 
                                                   Pageable pageable);

    /**
     * 查找需要处理退款的记录
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.status = :status AND p.refundedAt IS NULL")
    List<StickerPurchase> findPendingRefunds(@Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 批量更新状态
     */
    @Query("UPDATE StickerPurchase p SET p.status = :status WHERE p.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 删除过期的待支付记录
     */
    @Query("DELETE FROM StickerPurchase p WHERE p.status = :status AND p.createdAt < :cutoffDate")
    void deleteExpiredPendingRecords(@Param("status") StickerPurchase.PurchaseStatus status,
                                   @Param("cutoffDate") Instant cutoffDate);

    // ==================== 新增缺失的方法 ====================

    /**
     * 根据用户ID查找购买记录（不分页）
     */
    List<StickerPurchase> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据用户ID查找购买记录（分页）
     */
    Page<StickerPurchase> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据表情包ID查找购买记录
     */
    List<StickerPurchase> findByPackIdOrderByCreatedAtDesc(Long packId);

    /**
     * 查找成功购买记录
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.userId = :userId AND p.purchaseStatus = :status ORDER BY p.createdAt DESC")
    List<StickerPurchase> findSuccessfulPurchases(@Param("userId") Long userId, @Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 查找礼品购买记录
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.userId = :userId AND p.isGift = :isGift ORDER BY p.createdAt DESC")
    List<StickerPurchase> findGiftPurchases(@Param("userId") Long userId, @Param("isGift") boolean isGift);

    /**
     * 查找收到的礼品
     */
    @Query("SELECT p FROM StickerPurchase p WHERE p.giftRecipientId = :recipientId AND p.isGift = :isGift ORDER BY p.createdAt DESC")
    List<StickerPurchase> findReceivedGifts(@Param("recipientId") Long recipientId, @Param("isGift") boolean isGift);

    /**
     * 检查用户是否已购买表情包
     */
    boolean existsByUserIdAndPackIdAndPurchaseStatus(Long userId, Long packId, StickerPurchase.PurchaseStatus status);

    /**
     * 查找热门表情包
     */
    @Query("SELECT p.packId, COUNT(p) as purchaseCount FROM StickerPurchase p WHERE p.purchaseStatus = :status GROUP BY p.packId ORDER BY purchaseCount DESC")
    List<Object[]> findPopularStickerPacks(@Param("status") StickerPurchase.PurchaseStatus status);

    /**
     * 获取购买趋势
     */
    @Query("SELECT DATE(p.createdAt), COUNT(p) FROM StickerPurchase p WHERE p.createdAt >= :since GROUP BY DATE(p.createdAt) ORDER BY DATE(p.createdAt)")
    List<Object[]> getPurchaseTrends(@Param("since") Instant since);

    /**
     * 计算总收入
     */
    @Query("SELECT SUM(p.paidAmount) FROM StickerPurchase p WHERE p.purchaseStatus = :status AND p.createdAt >= :since")
    BigDecimal calculateTotalRevenue(@Param("status") StickerPurchase.PurchaseStatus status, @Param("since") Instant since);

    /**
     * 统计购买次数
     */
    @Query("SELECT COUNT(p) FROM StickerPurchase p WHERE p.purchaseStatus = :status AND p.createdAt >= :since")
    Long countPurchasesSince(@Param("status") StickerPurchase.PurchaseStatus status, @Param("since") Instant since);

    /**
     * 计算平均购买金额
     */
    @Query("SELECT AVG(p.paidAmount) FROM StickerPurchase p WHERE p.purchaseStatus = :status AND p.createdAt >= :since")
    BigDecimal calculateAveragePurchaseAmount(@Param("status") StickerPurchase.PurchaseStatus status, @Param("since") Instant since);

    /**
     * 按支付方式统计收入
     */
    @Query("SELECT p.paymentMethod, SUM(p.paidAmount) FROM StickerPurchase p WHERE p.purchaseStatus = :status AND p.createdAt >= :since GROUP BY p.paymentMethod")
    List<Object[]> getRevenueByPaymentMethod(@Param("status") StickerPurchase.PurchaseStatus status, @Param("since") Instant since);

    /**
     * 按购买状态统计
     */
    @Query("SELECT p.purchaseStatus, COUNT(p) FROM StickerPurchase p GROUP BY p.purchaseStatus")
    List<Object[]> countByPurchaseStatus();

    /**
     * 按支付方式统计
     */
    @Query("SELECT p.paymentMethod, COUNT(p) FROM StickerPurchase p GROUP BY p.paymentMethod")
    List<Object[]> countByPaymentMethod();

    /**
     * 按退款状态统计
     */
    @Query("SELECT p.refundStatus, COUNT(p) FROM StickerPurchase p GROUP BY p.refundStatus")
    List<Object[]> countByRefundStatus();

    /**
     * 获取礼品统计
     */
    @Query("SELECT COUNT(p), SUM(p.paidAmount) FROM StickerPurchase p WHERE p.isGift = true")
    Object[] getGiftStatistics();

    /**
     * 统计用户今日购买次数
     */
    @Query("SELECT COUNT(p) FROM StickerPurchase p WHERE p.userId = :userId AND p.createdAt >= :startOfDay")
    long countUserPurchasesToday(@Param("userId") Long userId, @Param("startOfDay") Instant startOfDay);
}
