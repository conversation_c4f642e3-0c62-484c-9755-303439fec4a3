package com.implatform.realtime.repository;

import com.implatform.realtime.entity.MessageSearchIndex;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 消息搜索Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface MessageSearchRepository extends R2dbcRepository<MessageSearchIndex, Long> {

    /**
     * 根据消息ID查找搜索索引
     */
    Optional<MessageSearchIndex> findByMessageId(Long messageId);

    /**
     * 全文搜索消息
     */
    @Query(value = "SELECT * FROM message_search_index msi " +
           "WHERE (:conversationId IS NULL OR msi.conversation_id = :conversationId) " +
           "AND (:senderId IS NULL OR msi.sender_id = :senderId) " +
           "AND (:messageType IS NULL OR msi.message_type = :messageType) " +
           "AND (:hasMedia IS NULL OR msi.has_attachments = :hasMedia) " +
           "AND (:startDate IS NULL OR msi.created_at >= :startDate) " +
           "AND (:endDate IS NULL OR msi.created_at <= :endDate) " +
           "AND (msi.content_vector @@ plainto_tsquery('simple', :query) " +
           "     OR msi.media_filename ILIKE CONCAT('%', :query, '%')) " +
           "ORDER BY ts_rank(msi.content_vector, plainto_tsquery('simple', :query)) DESC, msi.created_at DESC",
           nativeQuery = true)
    List<MessageSearchIndex> searchMessages(@Param("query") String query,
                                           @Param("conversationId") Long conversationId,
                                           @Param("senderId") Long senderId,
                                           @Param("messageType") String messageType,
                                           @Param("hasMedia") Boolean hasMedia,
                                           @Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate,
                                           Pageable pageable);

    /**
     * 简单内容搜索
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND (LOWER(msi.contentText) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
           "     OR LOWER(msi.mediaFilename) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY msi.createdAt DESC")
    Page<MessageSearchIndex> searchByKeyword(@Param("keyword") String keyword,
                                            @Param("conversationId") Long conversationId,
                                            Pageable pageable);

    /**
     * 按发送者搜索
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.senderId = :senderId " +
           "AND (:keyword IS NULL OR LOWER(msi.contentText) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY msi.createdAt DESC")
    Page<MessageSearchIndex> searchBySender(@Param("senderId") Long senderId,
                                           @Param("conversationId") Long conversationId,
                                           @Param("keyword") String keyword,
                                           Pageable pageable);

    /**
     * 按媒体类型搜索
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.hasAttachments = true " +
           "AND (:mediaType IS NULL OR msi.mediaType = :mediaType) " +
           "AND (:keyword IS NULL OR LOWER(msi.mediaFilename) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY msi.createdAt DESC")
    Page<MessageSearchIndex> searchByMedia(@Param("mediaType") String mediaType,
                                          @Param("conversationId") Long conversationId,
                                          @Param("keyword") String keyword,
                                          Pageable pageable);

    /**
     * 按时间范围搜索
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.createdAt BETWEEN :startDate AND :endDate " +
           "AND (:keyword IS NULL OR LOWER(msi.contentText) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY msi.createdAt DESC")
    Page<MessageSearchIndex> searchByDateRange(@Param("startDate") LocalDateTime startDate,
                                              @Param("endDate") LocalDateTime endDate,
                                              @Param("conversationId") Long conversationId,
                                              @Param("keyword") String keyword,
                                              Pageable pageable);

    /**
     * 查找会话中的所有消息
     */
    Page<MessageSearchIndex> findByConversationIdOrderByCreatedAtDesc(Long conversationId, Pageable pageable);

    /**
     * 查找用户发送的消息
     */
    Page<MessageSearchIndex> findBySenderIdOrderByCreatedAtDesc(Long senderId, Pageable pageable);

    /**
     * 查找包含媒体的消息
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.hasAttachments = true " +
           "ORDER BY msi.createdAt DESC")
    Page<MessageSearchIndex> findMessagesWithMedia(@Param("conversationId") Long conversationId, Pageable pageable);

    /**
     * 统计搜索索引数量
     */
    @Query("SELECT COUNT(msi) FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId)")
    Long countSearchableMessages(@Param("conversationId") Long conversationId);

    /**
     * 统计包含媒体的消息数量
     */
    @Query("SELECT COUNT(msi) FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.hasAttachments = true")
    Long countMessagesWithMedia(@Param("conversationId") Long conversationId);

    /**
     * 按消息类型统计
     */
    @Query("SELECT msi.messageType, COUNT(msi) FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "GROUP BY msi.messageType")
    List<Object[]> countByMessageType(@Param("conversationId") Long conversationId);

    /**
     * 按媒体类型统计
     */
    @Query("SELECT msi.mediaType, COUNT(msi) FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.hasAttachments = true " +
           "GROUP BY msi.mediaType")
    List<Object[]> countByMediaType(@Param("conversationId") Long conversationId);

    /**
     * 查找最近的消息
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.createdAt >= :since " +
           "ORDER BY msi.createdAt DESC")
    List<MessageSearchIndex> findRecentMessages(@Param("conversationId") Long conversationId,
                                               @Param("since") LocalDateTime since,
                                               Pageable pageable);

    /**
     * 查找活跃的发送者
     */
    @Query("SELECT msi.senderId, COUNT(msi) as messageCount FROM MessageSearchIndex msi WHERE " +
           "(:conversationId IS NULL OR msi.conversationId = :conversationId) " +
           "AND msi.createdAt >= :since " +
           "GROUP BY msi.senderId ORDER BY messageCount DESC")
    List<Object[]> findActiveSenders(@Param("conversationId") Long conversationId,
                                    @Param("since") LocalDateTime since,
                                    Pageable pageable);

    /**
     * 获取消息统计信息
     */
    @Query("SELECT " +
           "COUNT(msi) as totalMessages, " +
           "COUNT(CASE WHEN msi.hasAttachments = true THEN 1 END) as messagesWithMedia, " +
           "COUNT(DISTINCT msi.senderId) as uniqueSenders, " +
           "COUNT(DISTINCT msi.conversationId) as uniqueConversations " +
           "FROM MessageSearchIndex msi WHERE msi.createdAt >= :since")
    Object[] getMessageStatistics(@Param("since") LocalDateTime since);

    /**
     * 删除旧的搜索索引
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);

    /**
     * 查找需要重建索引的消息
     */
    @Query("SELECT msi FROM MessageSearchIndex msi WHERE " +
           "msi.updatedAt < :cutoffTime " +
           "ORDER BY msi.updatedAt ASC")
    List<MessageSearchIndex> findMessagesNeedingReindex(@Param("cutoffTime") LocalDateTime cutoffTime,
                                                        Pageable pageable);
}
