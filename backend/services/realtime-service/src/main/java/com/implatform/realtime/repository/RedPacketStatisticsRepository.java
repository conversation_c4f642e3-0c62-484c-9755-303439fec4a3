package com.implatform.realtime.repository;

import com.implatform.realtime.entity.RedPacketStatistics;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;

/**
 * 红包统计数据访问接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface RedPacketStatisticsRepository extends R2dbcRepository<RedPacketStatistics, Long> {

    /**
     * 根据用户ID和统计日期查找统计记录
     */
    Optional<RedPacketStatistics> findByUserIdAndStatDate(Long userId, LocalDate statDate);

    /**
     * 根据用户ID查找统计记录列表
     */
    List<RedPacketStatistics> findByUserIdOrderByStatDateDesc(Long userId);

    /**
     * 根据用户ID和日期范围查找统计记录
     */
    @Query("SELECT rps FROM RedPacketStatistics rps WHERE rps.userId = :userId " +
           "AND rps.statDate BETWEEN :startDate AND :endDate " +
           "ORDER BY rps.statDate DESC")
    List<RedPacketStatistics> findByUserIdAndDateRange(@Param("userId") Long userId, 
                                                       @Param("startDate") LocalDate startDate, 
                                                       @Param("endDate") LocalDate endDate);

    /**
     * 统计用户总的发送数据
     */
    @Query("SELECT new map(" +
           "SUM(rps.sentCount) as totalSentCount, " +
           "SUM(rps.sentAmount) as totalSentAmount" +
           ") FROM RedPacketStatistics rps WHERE rps.userId = :userId")
    List<Object> getUserTotalSentStatistics(@Param("userId") Long userId);

    /**
     * 统计用户总的领取数据
     */
    @Query("SELECT new map(" +
           "SUM(rps.receivedCount) as totalReceivedCount, " +
           "SUM(rps.receivedAmount) as totalReceivedAmount" +
           ") FROM RedPacketStatistics rps WHERE rps.userId = :userId")
    List<Object> getUserTotalReceivedStatistics(@Param("userId") Long userId);

    /**
     * 获取用户指定日期范围的汇总统计
     */
    @Query("SELECT new map(" +
           "SUM(rps.sentCount) as totalSentCount, " +
           "SUM(rps.sentAmount) as totalSentAmount, " +
           "SUM(rps.receivedCount) as totalReceivedCount, " +
           "SUM(rps.receivedAmount) as totalReceivedAmount" +
           ") FROM RedPacketStatistics rps WHERE rps.userId = :userId " +
           "AND rps.statDate BETWEEN :startDate AND :endDate")
    List<Object> getUserStatisticsSummary(@Param("userId") Long userId, 
                                         @Param("startDate") LocalDate startDate, 
                                         @Param("endDate") LocalDate endDate);

    /**
     * 获取系统总体统计
     */
    @Query("SELECT new map(" +
           "SUM(rps.sentCount) as totalSentCount, " +
           "SUM(rps.sentAmount) as totalSentAmount, " +
           "SUM(rps.receivedCount) as totalReceivedCount, " +
           "SUM(rps.receivedAmount) as totalReceivedAmount, " +
           "COUNT(DISTINCT rps.userId) as activeUsers" +
           ") FROM RedPacketStatistics rps WHERE rps.statDate BETWEEN :startDate AND :endDate")
    List<Object> getSystemStatistics(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 获取活跃用户排行榜（按发送金额）
     */
    @Query("SELECT rps.userId, SUM(rps.sentAmount) as totalSent FROM RedPacketStatistics rps " +
           "WHERE rps.statDate BETWEEN :startDate AND :endDate " +
           "GROUP BY rps.userId ORDER BY totalSent DESC")
    List<Object[]> getTopSendersByAmount(@Param("startDate") LocalDate startDate, 
                                        @Param("endDate") LocalDate endDate);

    /**
     * 获取活跃用户排行榜（按领取金额）
     */
    @Query("SELECT rps.userId, SUM(rps.receivedAmount) as totalReceived FROM RedPacketStatistics rps " +
           "WHERE rps.statDate BETWEEN :startDate AND :endDate " +
           "GROUP BY rps.userId ORDER BY totalReceived DESC")
    List<Object[]> getTopReceiversByAmount(@Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);

    /**
     * 获取每日统计趋势
     */
    @Query("SELECT rps.statDate, " +
           "SUM(rps.sentCount) as dailySentCount, " +
           "SUM(rps.sentAmount) as dailySentAmount, " +
           "SUM(rps.receivedCount) as dailyReceivedCount, " +
           "SUM(rps.receivedAmount) as dailyReceivedAmount " +
           "FROM RedPacketStatistics rps " +
           "WHERE rps.statDate BETWEEN :startDate AND :endDate " +
           "GROUP BY rps.statDate ORDER BY rps.statDate")
    List<Object[]> getDailyStatisticsTrend(@Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);

    /**
     * 检查用户是否有统计记录
     */
    @Query("SELECT COUNT(rps) > 0 FROM RedPacketStatistics rps WHERE rps.userId = :userId")
    boolean existsByUserId(@Param("userId") Long userId);

    /**
     * 删除指定日期之前的统计记录
     */
    @Query("DELETE FROM RedPacketStatistics rps WHERE rps.statDate < :beforeDate")
    int deleteStatisticsBeforeDate(@Param("beforeDate") LocalDate beforeDate);
}
