package com.implatform.realtime.repository;

import com.implatform.realtime.entity.VideoInteraction;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 视频互动Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface VideoInteractionRepository extends R2dbcRepository<VideoInteraction, Long> {

    // ==================== 基础查询 ====================

    /**
     * 查找用户对视频的特定互动
     */
    Optional<VideoInteraction> findByVideoIdAndUserIdAndInteractionType(Long videoId, Long userId, VideoInteraction.InteractionType interactionType);

    /**
     * 查找用户对视频的所有互动
     */
    List<VideoInteraction> findByVideoIdAndUserId(Long videoId, Long userId);

    /**
     * 查找视频的所有互动
     */
    List<VideoInteraction> findByVideoId(Long videoId);

    /**
     * 查找用户的所有互动
     */
    Page<VideoInteraction> findByUserId(Long userId, Pageable pageable);

    /**
     * 查找用户的特定类型互动
     */
    Page<VideoInteraction> findByUserIdAndInteractionType(Long userId, VideoInteraction.InteractionType interactionType, Pageable pageable);

    /**
     * 查找用户的活跃互动
     */
    Page<VideoInteraction> findByUserIdAndIsActiveTrue(Long userId, Pageable pageable);

    /**
     * 查找用户的特定类型活跃互动
     */
    Page<VideoInteraction> findByUserIdAndInteractionTypeAndIsActiveTrue(Long userId, VideoInteraction.InteractionType interactionType, Pageable pageable);

    // ==================== 点赞相关 ====================

    /**
     * 检查用户是否点赞了视频
     */
    boolean existsByVideoIdAndUserIdAndInteractionTypeAndIsActiveTrue(Long videoId, Long userId, VideoInteraction.InteractionType interactionType);

    /**
     * 获取用户点赞的视频列表
     */
    @Query("SELECT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = 'LIKE' AND vi.isActive = true")
    Page<Long> findLikedVideoIdsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户收藏的视频列表
     */
    @Query("SELECT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = 'FAVORITE' AND vi.isActive = true")
    Page<Long> findFavoritedVideoIdsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户分享的视频列表
     */
    @Query("SELECT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = 'SHARE'")
    Page<Long> findSharedVideoIdsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户观看的视频列表
     */
    @Query("SELECT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = 'VIEW' ORDER BY vi.createdAt DESC")
    Page<Long> findViewedVideoIdsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户下载的视频列表
     */
    @Query("SELECT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = 'DOWNLOAD' ORDER BY vi.createdAt DESC")
    Page<Long> findDownloadedVideoIdsByUserId(@Param("userId") Long userId, Pageable pageable);

    // ==================== 统计查询 ====================

    /**
     * 统计视频的特定类型互动数量
     */
    long countByVideoIdAndInteractionTypeAndIsActiveTrue(Long videoId, VideoInteraction.InteractionType interactionType);

    /**
     * 统计用户的特定类型互动数量
     */
    long countByUserIdAndInteractionTypeAndIsActiveTrue(Long userId, VideoInteraction.InteractionType interactionType);

    /**
     * 统计视频的总互动数量
     */
    long countByVideoIdAndIsActiveTrue(Long videoId);

    /**
     * 统计用户的总互动数量
     */
    long countByUserIdAndIsActiveTrue(Long userId);

    /**
     * 获取视频各类型互动统计
     */
    @Query("SELECT vi.interactionType, COUNT(vi) FROM VideoInteraction vi WHERE vi.videoId = :videoId AND vi.isActive = true GROUP BY vi.interactionType")
    List<Object[]> getVideoInteractionStatistics(@Param("videoId") Long videoId);

    /**
     * 获取用户各类型互动统计
     */
    @Query("SELECT vi.interactionType, COUNT(vi) FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.isActive = true GROUP BY vi.interactionType")
    List<Object[]> getUserInteractionStatistics(@Param("userId") Long userId);

    // ==================== 时间范围查询 ====================

    /**
     * 获取指定时间范围内的互动
     */
    Page<VideoInteraction> findByCreatedAtBetween(Instant startTime, Instant endTime, Pageable pageable);

    /**
     * 获取用户指定时间范围内的互动
     */
    Page<VideoInteraction> findByUserIdAndCreatedAtBetween(Long userId, Instant startTime, Instant endTime, Pageable pageable);

    /**
     * 获取视频指定时间范围内的互动
     */
    Page<VideoInteraction> findByVideoIdAndCreatedAtBetween(Long videoId, Instant startTime, Instant endTime, Pageable pageable);

    /**
     * 获取指定时间范围内的特定类型互动
     */
    Page<VideoInteraction> findByInteractionTypeAndCreatedAtBetween(VideoInteraction.InteractionType interactionType, Instant startTime, Instant endTime, Pageable pageable);

    // ==================== 趋势分析 ====================

    /**
     * 获取视频互动趋势数据
     */
    @Query("SELECT DATE(vi.createdAt), vi.interactionType, COUNT(vi) FROM VideoInteraction vi " +
           "WHERE vi.videoId = :videoId AND vi.createdAt >= :startTime " +
           "GROUP BY DATE(vi.createdAt), vi.interactionType ORDER BY DATE(vi.createdAt)")
    List<Object[]> getVideoInteractionTrend(@Param("videoId") Long videoId, @Param("startTime") Instant startTime);

    /**
     * 获取用户互动趋势数据
     */
    @Query("SELECT DATE(vi.createdAt), vi.interactionType, COUNT(vi) FROM VideoInteraction vi " +
           "WHERE vi.userId = :userId AND vi.createdAt >= :startTime " +
           "GROUP BY DATE(vi.createdAt), vi.interactionType ORDER BY DATE(vi.createdAt)")
    List<Object[]> getUserInteractionTrend(@Param("userId") Long userId, @Param("startTime") Instant startTime);

    /**
     * 获取全平台互动趋势数据
     */
    @Query("SELECT DATE(vi.createdAt), vi.interactionType, COUNT(vi) FROM VideoInteraction vi " +
           "WHERE vi.createdAt >= :startTime " +
           "GROUP BY DATE(vi.createdAt), vi.interactionType ORDER BY DATE(vi.createdAt)")
    List<Object[]> getPlatformInteractionTrend(@Param("startTime") Instant startTime);

    // ==================== 热门内容 ====================

    /**
     * 获取最受欢迎的视频（基于点赞）
     */
    @Query("SELECT vi.videoId, COUNT(vi) as likeCount FROM VideoInteraction vi " +
           "WHERE vi.interactionType = 'LIKE' AND vi.isActive = true AND vi.createdAt >= :startTime " +
           "GROUP BY vi.videoId ORDER BY likeCount DESC")
    List<Object[]> getMostLikedVideos(@Param("startTime") Instant startTime, Pageable pageable);

    /**
     * 获取最受收藏的视频
     */
    @Query("SELECT vi.videoId, COUNT(vi) as favoriteCount FROM VideoInteraction vi " +
           "WHERE vi.interactionType = 'FAVORITE' AND vi.isActive = true AND vi.createdAt >= :startTime " +
           "GROUP BY vi.videoId ORDER BY favoriteCount DESC")
    List<Object[]> getMostFavoritedVideos(@Param("startTime") Instant startTime, Pageable pageable);

    /**
     * 获取最多分享的视频
     */
    @Query("SELECT vi.videoId, COUNT(vi) as shareCount FROM VideoInteraction vi " +
           "WHERE vi.interactionType = 'SHARE' AND vi.createdAt >= :startTime " +
           "GROUP BY vi.videoId ORDER BY shareCount DESC")
    List<Object[]> getMostSharedVideos(@Param("startTime") Instant startTime, Pageable pageable);

    /**
     * 获取最多观看的视频
     */
    @Query("SELECT vi.videoId, COUNT(vi) as viewCount FROM VideoInteraction vi " +
           "WHERE vi.interactionType = 'VIEW' AND vi.createdAt >= :startTime " +
           "GROUP BY vi.videoId ORDER BY viewCount DESC")
    List<Object[]> getMostViewedVideos(@Param("startTime") Instant startTime, Pageable pageable);

    // ==================== 用户行为分析 ====================

    /**
     * 获取最活跃的用户
     */
    @Query("SELECT vi.userId, COUNT(vi) as interactionCount FROM VideoInteraction vi " +
           "WHERE vi.createdAt >= :startTime " +
           "GROUP BY vi.userId ORDER BY interactionCount DESC")
    List<Object[]> getMostActiveUsers(@Param("startTime") Instant startTime, Pageable pageable);

    /**
     * 获取用户最近的互动
     */
    List<VideoInteraction> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 获取用户最近的特定类型互动
     */
    List<VideoInteraction> findByUserIdAndInteractionTypeOrderByCreatedAtDesc(Long userId, VideoInteraction.InteractionType interactionType, Pageable pageable);

    // ==================== 更新操作 ====================

    /**
     * 切换互动状态
     */
    @Modifying
    @Query("UPDATE VideoInteraction vi SET vi.isActive = :isActive, vi.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE vi.videoId = :videoId AND vi.userId = :userId AND vi.interactionType = :interactionType")
    int toggleInteractionStatus(@Param("videoId") Long videoId, 
                               @Param("userId") Long userId, 
                               @Param("interactionType") VideoInteraction.InteractionType interactionType,
                               @Param("isActive") boolean isActive);

    /**
     * 批量删除用户的互动记录
     */
    @Modifying
    @Query("DELETE FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.videoId IN :videoIds")
    int batchDeleteUserInteractions(@Param("userId") Long userId, @Param("videoIds") List<Long> videoIds);

    /**
     * 批量删除视频的互动记录
     */
    @Modifying
    @Query("DELETE FROM VideoInteraction vi WHERE vi.videoId IN :videoIds")
    int batchDeleteVideoInteractions(@Param("videoIds") List<Long> videoIds);

    // ==================== 清理操作 ====================

    /**
     * 清理过期的观看记录
     */
    @Modifying
    @Query("DELETE FROM VideoInteraction vi WHERE vi.interactionType = 'VIEW' AND vi.createdAt < :expireTime")
    int cleanupExpiredViewRecords(@Param("expireTime") Instant expireTime);

    /**
     * 清理非活跃的互动记录
     */
    @Modifying
    @Query("DELETE FROM VideoInteraction vi WHERE vi.isActive = false AND vi.updatedAt < :expireTime")
    int cleanupInactiveInteractions(@Param("expireTime") Instant expireTime);

    // ==================== 推荐算法相关 ====================

    /**
     * 获取用户喜欢的视频创作者
     */
    @Query("SELECT v.creatorId, COUNT(vi) as interactionCount FROM VideoInteraction vi " +
           "JOIN ShortVideo v ON vi.videoId = v.id " +
           "WHERE vi.userId = :userId AND vi.interactionType IN ('LIKE', 'FAVORITE') AND vi.isActive = true " +
           "GROUP BY v.creatorId ORDER BY interactionCount DESC")
    List<Object[]> getUserPreferredCreators(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取与用户有相似喜好的用户
     */
    @Query("SELECT vi2.userId, COUNT(vi2) as commonLikes FROM VideoInteraction vi1 " +
           "JOIN VideoInteraction vi2 ON vi1.videoId = vi2.videoId " +
           "WHERE vi1.userId = :userId AND vi2.userId != :userId " +
           "AND vi1.interactionType = 'LIKE' AND vi2.interactionType = 'LIKE' " +
           "AND vi1.isActive = true AND vi2.isActive = true " +
           "GROUP BY vi2.userId ORDER BY commonLikes DESC")
    List<Object[]> findSimilarUsers(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户可能感兴趣的视频（基于相似用户的喜好）
     */
    @Query("SELECT vi.videoId, COUNT(vi) as score FROM VideoInteraction vi " +
           "WHERE vi.userId IN :similarUserIds AND vi.interactionType = 'LIKE' AND vi.isActive = true " +
           "AND vi.videoId NOT IN (SELECT vi2.videoId FROM VideoInteraction vi2 WHERE vi2.userId = :userId AND vi2.interactionType = 'LIKE' AND vi2.isActive = true) " +
           "GROUP BY vi.videoId ORDER BY score DESC")
    List<Object[]> getRecommendedVideosBySimilarUsers(@Param("userId") Long userId, @Param("similarUserIds") List<Long> similarUserIds, Pageable pageable);

    // ==================== 特殊查询 ====================

    /**
     * 检查用户是否对视频有任何互动
     */
    boolean existsByVideoIdAndUserId(Long videoId, Long userId);

    /**
     * 获取用户对视频的最后互动时间
     */
    @Query("SELECT MAX(vi.createdAt) FROM VideoInteraction vi WHERE vi.videoId = :videoId AND vi.userId = :userId")
    Optional<Instant> getLastInteractionTime(@Param("videoId") Long videoId, @Param("userId") Long userId);

    /**
     * 获取视频的互动用户列表
     */
    @Query("SELECT DISTINCT vi.userId FROM VideoInteraction vi WHERE vi.videoId = :videoId AND vi.interactionType = :interactionType AND vi.isActive = true")
    List<Long> getVideoInteractionUsers(@Param("videoId") Long videoId, @Param("interactionType") VideoInteraction.InteractionType interactionType);

    /**
     * 获取用户的互动视频列表
     */
    @Query("SELECT DISTINCT vi.videoId FROM VideoInteraction vi WHERE vi.userId = :userId AND vi.interactionType = :interactionType AND vi.isActive = true")
    List<Long> getUserInteractionVideos(@Param("userId") Long userId, @Param("interactionType") VideoInteraction.InteractionType interactionType);

    /**
     * 检查两个用户是否对同一视频有相同类型的互动
     */
    @Query("SELECT COUNT(vi1) > 0 FROM VideoInteraction vi1, VideoInteraction vi2 " +
           "WHERE vi1.userId = :userId1 AND vi2.userId = :userId2 " +
           "AND vi1.videoId = vi2.videoId AND vi1.interactionType = vi2.interactionType " +
           "AND vi1.interactionType = :interactionType AND vi1.isActive = true AND vi2.isActive = true")
    boolean hasCommonInteraction(@Param("userId1") Long userId1, @Param("userId2") Long userId2, @Param("interactionType") VideoInteraction.InteractionType interactionType);
}
