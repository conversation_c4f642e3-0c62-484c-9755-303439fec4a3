package com.implatform.realtime.repository;

import com.implatform.realtime.entity.AccountFollower;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 公众号关注者数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface AccountFollowerRepository extends R2dbcRepository<AccountFollower, String> {

    /**
     * 查找用户对公众号的关注记录
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId AND user_id = :userId")
    Mono<AccountFollower> findByAccountIdAndUserId(@Param("accountId") String accountId, @Param("userId") String userId);

    /**
     * 获取公众号的关注者列表
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId AND status = :status ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findByAccountIdAndStatusOrderByFollowedAtDesc(
            @Param("accountId") String accountId,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 获取用户关注的公众号列表
     */
    @Query("SELECT * FROM account_followers WHERE user_id = :userId AND status = :status ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findByUserIdAndStatusOrderByFollowedAtDesc(
            @Param("userId") String userId,
            @Param("status") String status,
            @Param("limit") int limit,
            @Param("offset") long offset);

    /**
     * 统计公众号关注者数量
     */
    @Query("SELECT COUNT(*) FROM account_followers WHERE account_id = :accountId AND status = :status")
    Mono<Long> countByAccountIdAndStatus(@Param("accountId") String accountId, @Param("status") String status);

    /**
     * 统计用户关注的公众号数量
     */
    @Query("SELECT COUNT(*) FROM account_followers WHERE user_id = :userId AND status = :status")
    Mono<Long> countByUserIdAndStatus(@Param("userId") String userId, @Param("status") String status);

    /**
     * 获取活跃关注者
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId " +
           "AND status = :status AND last_interaction_at >= :sinceTime " +
           "ORDER BY last_interaction_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findActiveFollowers(@Param("accountId") String accountId,
                                            @Param("status") String status,
                                            @Param("sinceTime") Instant sinceTime,
                                            @Param("limit") int limit,
                                            @Param("offset") long offset);

    /**
     * 获取新关注者
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId " +
           "AND status = :status AND followed_at >= :sinceTime " +
           "ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findNewFollowers(@Param("accountId") String accountId,
                                         @Param("status") String status,
                                         @Param("sinceTime") Instant sinceTime,
                                         @Param("limit") int limit,
                                         @Param("offset") long offset);

    /**
     * 根据标签查找关注者
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId " +
           "AND status = :status AND user_tags LIKE CONCAT('%', :tag, '%') " +
           "ORDER BY followed_at DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findFollowersByTag(@Param("accountId") String accountId,
                                           @Param("status") String status,
                                           @Param("tag") String tag,
                                           @Param("limit") int limit,
                                           @Param("offset") long offset);

    /**
     * 更新关注状态
     */
    @Modifying
    @Query("UPDATE account_followers SET status = :status, unfollowed_at = :unfollowedAt, updated_at = :updatedAt " +
           "WHERE account_id = :accountId AND user_id = :userId")
    Mono<Integer> updateFollowStatus(@Param("accountId") String accountId,
                          @Param("userId") String userId,
                          @Param("status") String status,
                          @Param("unfollowedAt") Instant unfollowedAt,
                          @Param("updatedAt") Instant updatedAt);

    /**
     * 更新互动信息
     */
    @Modifying
    @Query("UPDATE account_followers SET interaction_count = interaction_count + 1, " +
           "last_interaction_at = :interactionTime, updated_at = :updatedAt " +
           "WHERE account_id = :accountId AND user_id = :userId")
    Mono<Integer> updateInteraction(@Param("accountId") String accountId,
                         @Param("userId") String userId,
                         @Param("interactionTime") Instant interactionTime,
                         @Param("updatedAt") Instant updatedAt);

    /**
     * 批量更新用户标签
     */
    @Modifying
    @Query("UPDATE account_followers SET user_tags = :tags, updated_at = :updatedAt " +
           "WHERE account_id = :accountId AND user_id = ANY(:userIds)")
    Mono<Integer> batchUpdateTags(@Param("accountId") String accountId,
                       @Param("userIds") String[] userIds,
                       @Param("tags") String tags,
                       @Param("updatedAt") Instant updatedAt);

    /**
     * 获取关注者统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as totalFollowers, " +
           "COUNT(CASE WHEN followed_at >= :todayStart THEN 1 END) as todayNew, " +
           "COUNT(CASE WHEN followed_at >= :weekStart THEN 1 END) as weekNew, " +
           "COUNT(CASE WHEN followed_at >= :monthStart THEN 1 END) as monthNew, " +
           "COUNT(CASE WHEN last_interaction_at >= :activeThreshold THEN 1 END) as activeFollowers " +
           "FROM account_followers WHERE account_id = :accountId AND status = :status")
    Mono<Object[]> getFollowerStatistics(@Param("accountId") String accountId,
                                        @Param("status") String status,
                                        @Param("todayStart") Instant todayStart,
                                        @Param("weekStart") Instant weekStart,
                                        @Param("monthStart") Instant monthStart,
                                        @Param("activeThreshold") Instant activeThreshold);

    /**
     * 获取关注趋势数据
     */
    @Query("SELECT DATE(followed_at) as followDate, COUNT(*) as followCount " +
           "FROM account_followers WHERE account_id = :accountId " +
           "AND status = :status AND followed_at >= :startTime " +
           "GROUP BY DATE(followed_at) ORDER BY followDate")
    Flux<Object[]> getFollowTrend(@Param("accountId") String accountId,
                                 @Param("status") String status,
                                 @Param("startTime") Instant startTime);

    /**
     * 检查用户是否关注了公众号
     */
    @Query("SELECT COUNT(*) > 0 FROM account_followers WHERE account_id = :accountId AND user_id = :userId AND status = :status")
    Mono<Boolean> existsByAccountIdAndUserIdAndStatus(@Param("accountId") String accountId, @Param("userId") String userId, @Param("status") String status);

    /**
     * 获取互动最多的关注者
     */
    @Query("SELECT * FROM account_followers WHERE account_id = :accountId " +
           "AND status = :status ORDER BY interaction_count DESC LIMIT :limit OFFSET :offset")
    Flux<AccountFollower> findTopInteractiveFollowers(@Param("accountId") String accountId,
                                                     @Param("status") String status,
                                                     @Param("limit") int limit,
                                                     @Param("offset") long offset);

    /**
     * 删除指定时间之前的取消关注记录
     */
    @Modifying
    @Query("DELETE FROM account_followers WHERE status = :status AND unfollowed_at < :beforeTime")
    Mono<Integer> deleteOldUnfollowedRecords(@Param("status") String status,
                                  @Param("beforeTime") Instant beforeTime);
}
