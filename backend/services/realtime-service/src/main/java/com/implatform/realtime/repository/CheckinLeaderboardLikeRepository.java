package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CheckinLeaderboard;
import com.implatform.realtime.entity.CheckinLeaderboardLike;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 签到排行榜点赞Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CheckinLeaderboardLikeRepository extends R2dbcRepository<CheckinLeaderboardLike, Long> {

    /**
     * 根据排行榜ID和用户ID查找点赞记录
     */
    Optional<CheckinLeaderboardLike> findByLeaderboardIdAndUserId(Long leaderboardId, Long userId);

    /**
     * 检查用户是否已点赞指定排行榜记录
     */
    boolean existsByLeaderboardIdAndUserIdAndStatus(Long leaderboardId, Long userId, 
                                                     CheckinLeaderboardLike.LikeStatus status);

    /**
     * 根据用户ID和目标用户ID查找点赞记录
     */
    List<CheckinLeaderboardLike> findByUserIdAndTargetUserIdAndStatus(Long userId, Long targetUserId, 
                                                                       CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取排行榜记录的所有点赞
     */
    List<CheckinLeaderboardLike> findByLeaderboardIdAndStatusOrderByCreatedAtDesc(Long leaderboardId, 
                                                                                   CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取排行榜记录的点赞数量
     */
    long countByLeaderboardIdAndStatus(Long leaderboardId, CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取用户的所有点赞记录
     */
    Page<CheckinLeaderboardLike> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, 
                                                                            CheckinLeaderboardLike.LikeStatus status, 
                                                                            Pageable pageable);

    /**
     * 获取用户收到的所有点赞
     */
    Page<CheckinLeaderboardLike> findByTargetUserIdAndStatusOrderByCreatedAtDesc(Long targetUserId, 
                                                                                  CheckinLeaderboardLike.LikeStatus status, 
                                                                                  Pageable pageable);

    /**
     * 获取指定排行榜类型和周期的点赞记录
     */
    List<CheckinLeaderboardLike> findByRankingTypeAndRankingPeriodAndStatusOrderByCreatedAtDesc(
            CheckinLeaderboard.RankingType rankingType, String rankingPeriod, 
            CheckinLeaderboardLike.LikeStatus status);

    /**
     * 统计用户在指定周期内的点赞数
     */
    @Query("SELECT COUNT(l) FROM CheckinLeaderboardLike l WHERE l.userId = :userId " +
           "AND l.rankingPeriod = :rankingPeriod AND l.status = :status")
    long countUserLikesInPeriod(@Param("userId") Long userId, 
                                @Param("rankingPeriod") String rankingPeriod,
                                @Param("status") CheckinLeaderboardLike.LikeStatus status);

    /**
     * 统计用户收到的点赞数
     */
    @Query("SELECT COUNT(l) FROM CheckinLeaderboardLike l WHERE l.targetUserId = :targetUserId " +
           "AND l.rankingPeriod = :rankingPeriod AND l.status = :status")
    long countUserReceivedLikesInPeriod(@Param("targetUserId") Long targetUserId, 
                                        @Param("rankingPeriod") String rankingPeriod,
                                        @Param("status") CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取最活跃的点赞用户
     */
    @Query("SELECT l.userId, COUNT(l) as likeCount FROM CheckinLeaderboardLike l " +
           "WHERE l.rankingPeriod = :rankingPeriod AND l.status = :status " +
           "GROUP BY l.userId ORDER BY likeCount DESC")
    List<Object[]> getMostActiveLikers(@Param("rankingPeriod") String rankingPeriod,
                                       @Param("status") CheckinLeaderboardLike.LikeStatus status,
                                       Pageable pageable);

    /**
     * 获取最受欢迎的用户
     */
    @Query("SELECT l.targetUserId, COUNT(l) as receivedLikes FROM CheckinLeaderboardLike l " +
           "WHERE l.rankingPeriod = :rankingPeriod AND l.status = :status " +
           "GROUP BY l.targetUserId ORDER BY receivedLikes DESC")
    List<Object[]> getMostPopularUsers(@Param("rankingPeriod") String rankingPeriod,
                                       @Param("status") CheckinLeaderboardLike.LikeStatus status,
                                       Pageable pageable);

    /**
     * 获取指定时间范围内的点赞记录
     */
    List<CheckinLeaderboardLike> findByCreatedAtBetweenAndStatusOrderByCreatedAtDesc(
            Instant startTime, Instant endTime, CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取用户的点赞历史
     */
    @Query("SELECT l FROM CheckinLeaderboardLike l WHERE l.userId = :userId " +
           "AND l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status " +
           "ORDER BY l.createdAt DESC")
    List<CheckinLeaderboardLike> getUserLikeHistory(@Param("userId") Long userId,
                                                     @Param("startTime") Instant startTime,
                                                     @Param("endTime") Instant endTime,
                                                     @Param("status") CheckinLeaderboardLike.LikeStatus status);

    /**
     * 批量更新点赞状态
     */
    @Modifying
    @Query("UPDATE CheckinLeaderboardLike l SET l.status = :newStatus WHERE l.id IN :ids")
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("newStatus") CheckinLeaderboardLike.LikeStatus newStatus);

    /**
     * 取消用户对指定排行榜记录的点赞
     */
    @Modifying
    @Query("UPDATE CheckinLeaderboardLike l SET l.status = 'CANCELLED' " +
           "WHERE l.leaderboardId = :leaderboardId AND l.userId = :userId AND l.status = 'ACTIVE'")
    int cancelLike(@Param("leaderboardId") Long leaderboardId, @Param("userId") Long userId);

    /**
     * 删除指定时间之前的点赞记录
     */
    void deleteByCreatedAtBefore(Instant cutoffTime);

    /**
     * 删除指定排行榜的所有点赞记录
     */
    void deleteByLeaderboardId(Long leaderboardId);

    /**
     * 获取点赞统计信息
     */
    @Query("SELECT l.rankingType, l.rankingPeriod, COUNT(l), COUNT(DISTINCT l.userId), COUNT(DISTINCT l.targetUserId) " +
           "FROM CheckinLeaderboardLike l WHERE l.status = :status " +
           "GROUP BY l.rankingType, l.rankingPeriod ORDER BY l.rankingType, l.rankingPeriod DESC")
    List<Object[]> getLikeStatistics(@Param("status") CheckinLeaderboardLike.LikeStatus status);

    /**
     * 获取每日点赞统计
     */
    @Query("SELECT DATE(l.createdAt), COUNT(l) FROM CheckinLeaderboardLike l " +
           "WHERE l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status " +
           "GROUP BY DATE(l.createdAt) ORDER BY DATE(l.createdAt)")
    List<Object[]> getDailyLikeStatistics(@Param("startTime") Instant startTime,
                                          @Param("endTime") Instant endTime,
                                          @Param("status") CheckinLeaderboardLike.LikeStatus status);

    /**
     * 检查用户是否对目标用户有互相点赞
     */
    @Query("SELECT COUNT(l1) > 0 AND COUNT(l2) > 0 FROM CheckinLeaderboardLike l1, CheckinLeaderboardLike l2 " +
           "WHERE l1.userId = :userId AND l1.targetUserId = :targetUserId AND l1.status = 'ACTIVE' " +
           "AND l2.userId = :targetUserId AND l2.targetUserId = :userId AND l2.status = 'ACTIVE'")
    boolean hasMutualLikes(@Param("userId") Long userId, @Param("targetUserId") Long targetUserId);

    /**
     * 获取用户的点赞关系网络
     */
    @Query("SELECT l.targetUserId, COUNT(l) FROM CheckinLeaderboardLike l " +
           "WHERE l.userId = :userId AND l.status = 'ACTIVE' " +
           "GROUP BY l.targetUserId ORDER BY COUNT(l) DESC")
    List<Object[]> getUserLikeNetwork(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找过期的点赞记录（用于清理）
     */
    @Query("SELECT l FROM CheckinLeaderboardLike l WHERE l.status = 'ACTIVE' " +
           "AND l.createdAt < :expiryTime")
    List<CheckinLeaderboardLike> findExpiredLikes(@Param("expiryTime") Instant expiryTime);

    /**
     * 获取排行榜记录的点赞用户列表
     */
    @Query("SELECT l.userId FROM CheckinLeaderboardLike l WHERE l.leaderboardId = :leaderboardId " +
           "AND l.status = 'ACTIVE' ORDER BY l.createdAt DESC")
    List<Long> getLeaderboardLikerIds(@Param("leaderboardId") Long leaderboardId, Pageable pageable);
}
