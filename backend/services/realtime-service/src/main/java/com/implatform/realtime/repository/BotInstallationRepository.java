package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BotInstallation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 机器人安装Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BotInstallationRepository extends R2dbcRepository<BotInstallation, String> {

    /**
     * 查找用户安装的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :installedBy AND status = :status ORDER BY last_used_at DESC")
    Flux<BotInstallation> findByInstalledByAndStatusOrderByLastUsedAtDesc(
            @Param("installedBy") String installedBy, @Param("status") String status);

    /**
     * 分页查找用户安装的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :installedBy AND status = :status ORDER BY last_used_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findByInstalledByAndStatusOrderByLastUsedAtDesc(
            @Param("installedBy") String installedBy, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找机器人的所有安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = :status ORDER BY installed_at DESC")
    Flux<BotInstallation> findByBotIdAndStatusOrderByInstalledAtDesc(
            @Param("botId") Long botId, @Param("status") String status);

    /**
     * 分页查找机器人的安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = :status ORDER BY installed_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findByBotIdAndStatusOrderByInstalledAtDesc(
            @Param("botId") Long botId, @Param("status") String status, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找特定安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND installed_by = :installedBy AND installation_type = :installationType AND target_id = :targetId")
    Mono<BotInstallation> findByBotIdAndInstalledByAndInstallationTypeAndTargetId(
            @Param("botId") Long botId, @Param("installedBy") String installedBy, @Param("installationType") String installationType, @Param("targetId") String targetId);

    /**
     * 查找私聊安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND installed_by = :installedBy AND installation_type = :installationType")
    Mono<BotInstallation> findByBotIdAndInstalledByAndInstallationType(
            @Param("botId") Long botId, @Param("installedBy") String installedBy, @Param("installationType") String installationType);

    /**
     * 根据机器人ID和安装类型查找安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND installation_type = :installationType")
    Flux<BotInstallation> findByBotIdAndInstallationType(
            @Param("botId") Long botId, @Param("installationType") String installationType);

    /**
     * 根据机器人ID、聊天ID和安装类型查找安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND chat_id = :chatId AND installation_type = :installationType")
    Mono<BotInstallation> findByBotIdAndChatIdAndInstallationType(
            @Param("botId") Long botId, @Param("chatId") String chatId, @Param("installationType") String installationType);

    /**
     * 检查机器人是否已在指定聊天中安装（带状态）
     */
    @Query("SELECT COUNT(*) > 0 FROM bot_installations WHERE bot_id = :botId AND chat_id = :chatId AND installation_type = :installationType AND status = :status")
    Mono<Boolean> existsByBotIdAndChatIdAndInstallationTypeAndStatus(
            @Param("botId") Long botId, @Param("chatId") String chatId, @Param("installationType") String installationType,
            @Param("status") String status);

    /**
     * 查找群组/频道的机器人安装
     */
    @Query("SELECT * FROM bot_installations WHERE installation_type = :installationType AND target_id = :targetId AND status = :status ORDER BY installed_at DESC")
    Flux<BotInstallation> findByInstallationTypeAndTargetIdAndStatusOrderByInstalledAtDesc(
            @Param("installationType") String installationType, @Param("targetId") String targetId,
            @Param("status") String status);

    /**
     * 查找用户在特定群组/频道安装的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :installedBy AND installation_type = :installationType AND target_id = :targetId AND status = :status")
    Flux<BotInstallation> findByInstalledByAndInstallationTypeAndTargetIdAndStatus(
            @Param("installedBy") String installedBy, @Param("installationType") String installationType, @Param("targetId") String targetId,
            @Param("status") String status);

    /**
     * 检查机器人是否已安装
     */
    @Query("SELECT COUNT(*) > 0 FROM bot_installations WHERE bot_id = :botId AND " +
           "installed_by = :installedBy AND installation_type = :installationType AND " +
           "(:targetId IS NULL OR target_id = :targetId) AND status = 'ACTIVE'")
    Mono<Boolean> isInstalled(@Param("botId") Long botId, @Param("installedBy") String installedBy,
                       @Param("installationType") String installationType,
                       @Param("targetId") String targetId);

    /**
     * 统计机器人的安装数量
     */
    @Query("SELECT COUNT(*) FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE'")
    Mono<Long> countActiveInstallationsByBotId(@Param("botId") Long botId);

    /**
     * 统计用户安装的机器人数量
     */
    @Query("SELECT COUNT(*) FROM bot_installations WHERE installed_by = :userId AND status = 'ACTIVE'")
    Mono<Long> countActiveInstallationsByUserId(@Param("userId") Long userId);

    /**
     * 统计各安装类型的数量
     */
    @Query("SELECT installation_type, COUNT(*) FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE' GROUP BY installation_type")
    Flux<Object[]> countInstallationsByType(@Param("botId") Long botId);

    /**
     * 查找活跃安装（最近使用）
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE' AND " +
           "last_used_at >= :since ORDER BY last_used_at DESC")
    Flux<BotInstallation> findActiveInstallationsSince(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找非活跃安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE' AND " +
           "(last_used_at IS NULL OR last_used_at < :threshold)")
    Flux<BotInstallation> findInactiveInstallations(@Param("botId") Long botId, @Param("threshold") LocalDateTime threshold);

    /**
     * 查找最近安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE' AND " +
           "installed_at >= :since ORDER BY installed_at DESC")
    Flux<BotInstallation> findRecentInstallations(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找需要更新活跃时间的安装
     */
    @Query("SELECT * FROM bot_installations WHERE status = 'ACTIVE' AND " +
           "(last_used_at IS NULL OR last_used_at < :threshold)")
    Flux<BotInstallation> findInstallationsNeedingActivityUpdate(@Param("threshold") LocalDateTime threshold);

    /**
     * 批量更新最后使用时间
     */
    @Modifying
    @Query("UPDATE bot_installations SET last_used_at = :lastUsedAt, usage_count = usage_count + 1 " +
           "WHERE id = ANY(:installationIds)")
    Mono<Integer> updateLastUsedTime(@Param("installationIds") String[] installationIds, @Param("lastUsedAt") LocalDateTime lastUsedAt);

    /**
     * 批量更新安装状态
     */
    @Modifying
    @Query("UPDATE bot_installations SET status = :status WHERE id = ANY(:installationIds)")
    Mono<Integer> updateInstallationStatus(@Param("installationIds") String[] installationIds,
                                @Param("status") String status);

    /**
     * 移除机器人的所有安装
     */
    @Modifying
    @Query("UPDATE bot_installations SET status = 'REMOVED' WHERE bot_id = :botId")
    Mono<Integer> removeAllInstallationsByBotId(@Param("botId") Long botId);

    /**
     * 移除用户的所有安装
     */
    @Modifying
    @Query("UPDATE bot_installations SET status = 'REMOVED' WHERE installed_by = :userId")
    Mono<Integer> removeAllInstallationsByUserId(@Param("userId") Long userId);

    /**
     * 获取安装统计信息
     */
    @Query("SELECT " +
           "COUNT(*) as totalInstallations, " +
           "COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as activeInstallations, " +
           "COUNT(CASE WHEN installation_type = 'PRIVATE' THEN 1 END) as privateInstallations, " +
           "COUNT(CASE WHEN installation_type = 'GROUP' THEN 1 END) as groupInstallations, " +
           "COUNT(CASE WHEN installation_type = 'CHANNEL' THEN 1 END) as channelInstallations, " +
           "AVG(usage_count) as averageUsage " +
           "FROM bot_installations WHERE bot_id = :botId")
    Mono<Object[]> getInstallationStatistics(@Param("botId") Long botId);

    /**
     * 查找安装趋势
     */
    @Query("SELECT DATE(installed_at) as date, COUNT(*) as count FROM bot_installations " +
           "WHERE bot_id = :botId AND installed_at >= :since " +
           "GROUP BY DATE(installed_at) ORDER BY date")
    Flux<Object[]> getInstallationTrend(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找使用趋势
     */
    @Query("SELECT DATE(last_used_at) as date, COUNT(*) as count FROM bot_installations " +
           "WHERE bot_id = :botId AND last_used_at >= :since " +
           "GROUP BY DATE(last_used_at) ORDER BY date")
    Flux<Object[]> getUsageTrend(@Param("botId") Long botId, @Param("since") LocalDateTime since);

    /**
     * 查找热门安装目标
     */
    @Query("SELECT target_id, installation_type, COUNT(*) as count FROM bot_installations " +
           "WHERE status = 'ACTIVE' AND target_id IS NOT NULL " +
           "GROUP BY target_id, installation_type ORDER BY count DESC LIMIT :limit OFFSET :offset")
    Flux<Object[]> findPopularInstallationTargets(@Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找用户最常用的机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :userId AND status = 'ACTIVE' " +
           "ORDER BY usage_count DESC, last_used_at DESC NULLS LAST LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findMostUsedBotsByUser(@Param("userId") Long userId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找机器人的重度用户
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND status = 'ACTIVE' " +
           "ORDER BY usage_count DESC, last_used_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotInstallation> findHeavyUsersByBot(@Param("botId") Long botId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找需要清理的安装
     */
    @Query("SELECT * FROM bot_installations WHERE status = 'REMOVED' AND installed_at < :cutoffTime")
    Flux<BotInstallation> findInstallationsForCleanup(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 删除旧的已移除安装
     */
    @Modifying
    @Query("DELETE FROM bot_installations WHERE status = 'REMOVED' AND installed_at < :cutoffTime")
    Mono<Integer> deleteOldRemovedInstallations(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 查找重复安装
     */
    @Query("SELECT bot_id, installed_by, installation_type, target_id, COUNT(*) as count " +
           "FROM bot_installations WHERE status = 'ACTIVE' " +
           "GROUP BY bot_id, installed_by, installation_type, target_id HAVING COUNT(*) > 1")
    Flux<Object[]> findDuplicateInstallations();

    /**
     * 查找安装冲突
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND installed_by = :installedBy AND " +
           "installation_type = :installationType AND " +
           "(:targetId IS NULL OR target_id = :targetId) AND status = 'ACTIVE'")
    Flux<BotInstallation> findInstallationConflicts(@Param("botId") Long botId, @Param("installedBy") Long installedBy,
                                                   @Param("installationType") String installationType,
                                                   @Param("targetId") Long targetId);

    /**
     * 查找用户在群组中安装的所有机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :userId AND " +
           "installation_type IN ('GROUP', 'CHANNEL') AND status = 'ACTIVE' " +
           "ORDER BY last_used_at DESC NULLS LAST")
    Flux<BotInstallation> findUserGroupInstallations(@Param("userId") Long userId);

    /**
     * 查找用户的私聊机器人
     */
    @Query("SELECT * FROM bot_installations WHERE installed_by = :userId AND " +
           "installation_type = 'PRIVATE' AND status = 'ACTIVE' " +
           "ORDER BY last_used_at DESC NULLS LAST")
    Flux<BotInstallation> findUserPrivateInstallations(@Param("userId") Long userId);

    /**
     * 统计用户各类型安装数量
     */
    @Query("SELECT installation_type, COUNT(*) FROM bot_installations " +
           "WHERE installed_by = :userId AND status = 'ACTIVE' GROUP BY installation_type")
    Flux<Object[]> countUserInstallationsByType(@Param("userId") Long userId);

    // ============================================================================
    // Missing Methods Required by BotInstallationService
    // ============================================================================

    /**
     * 根据机器人ID、聊天ID、安装类型查找活跃安装
     */
    @Query("SELECT * FROM bot_installations WHERE bot_id = :botId AND chat_id = :chatId AND installation_type = :installationType AND is_active = true")
    Mono<BotInstallation> findByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(
            @Param("botId") Long botId, @Param("chatId") String chatId, @Param("installationType") String installationType);

    /**
     * 检查机器人是否已在指定聊天中安装
     */
    @Query("SELECT COUNT(*) > 0 FROM bot_installations WHERE bot_id = :botId AND chat_id = :chatId AND installation_type = :installationType AND is_active = true")
    Mono<Boolean> existsByBotIdAndChatIdAndInstallationTypeAndIsActiveTrue(
            @Param("botId") Long botId, @Param("chatId") String chatId, @Param("installationType") String installationType);

    /**
     * 根据用户ID查找活跃安装（按安装时间排序）
     */
    @Query("SELECT * FROM bot_installations WHERE user_id = :userId AND is_active = true ORDER BY installed_at DESC")
    Flux<BotInstallation> findByUserIdAndIsActiveTrueOrderByInstalledAtDesc(@Param("userId") String userId);

    /**
     * 根据聊天ID查找活跃安装（按安装时间排序）
     */
    @Query("SELECT * FROM bot_installations WHERE chat_id = :chatId AND is_active = true ORDER BY installed_at DESC")
    Flux<BotInstallation> findByChatIdAndIsActiveTrueOrderByInstalledAtDesc(@Param("chatId") String chatId);

    /**
     * 根据机器人ID查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByBotIdAndIsActiveTrueOrderByInstalledAtDesc(Long botId);

    /**
     * 根据安装类型查找活跃安装（按安装时间排序）
     */
    List<BotInstallation> findByInstallationTypeAndIsActiveTrueOrderByInstalledAtDesc(
            BotInstallation.InstallationType installationType);

    /**
     * 统计各安装类型数量
     */
    @Query("SELECT bi.installationType, COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.installationType")
    List<Object[]> countByInstallationType();

    /**
     * 统计各机器人的安装数量
     */
    @Query("SELECT bi.botId, COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.botId ORDER BY COUNT(bi) DESC")
    List<Object[]> countByBot();

    /**
     * 获取安装活动统计
     */
    @Query("SELECT " +
           "COUNT(bi) as totalInstallations, " +
           "COUNT(CASE WHEN bi.isActive = true THEN 1 END) as activeInstallations, " +
           "COUNT(CASE WHEN bi.lastUsedAt >= :recentThreshold THEN 1 END) as recentlyUsed " +
           "FROM BotInstallation bi")
    Object[] getInstallationActivityStats(@Param("recentThreshold") Instant recentThreshold);

    /**
     * 获取安装趋势
     */
    @Query("SELECT DATE(bi.installedAt), COUNT(bi) FROM BotInstallation bi " +
           "WHERE bi.installedAt >= :since " +
           "GROUP BY DATE(bi.installedAt) ORDER BY DATE(bi.installedAt)")
    List<Object[]> getInstallationTrends(@Param("since") Instant since);

    /**
     * 统计活跃安装数量
     */
    long countByIsActiveTrue();

    /**
     * 查找最多安装的机器人
     */
    @Query("SELECT bi.botId, COUNT(bi) as installCount FROM BotInstallation bi " +
           "WHERE bi.isActive = true GROUP BY bi.botId ORDER BY installCount DESC")
    List<Object[]> findMostInstalledBots();

    /**
     * 查找非活跃安装（重载方法）
     */
    @Query("SELECT bi FROM BotInstallation bi WHERE bi.isActive = :isActive AND " +
           "(bi.lastUsedAt IS NULL OR bi.lastUsedAt < :threshold)")
    List<BotInstallation> findInactiveInstallations(@Param("threshold") Long thresholdDays, @Param("isActive") boolean isActive);

    /**
     * 根据用户ID分页查找安装（按安装时间排序）
     */
    Page<BotInstallation> findByUserIdOrderByInstalledAtDesc(String userId, Pageable pageable);

    /**
     * 根据聊天ID分页查找安装（按安装时间排序）
     */
    Page<BotInstallation> findByChatIdOrderByInstalledAtDesc(String chatId, Pageable pageable);

    /**
     * 统计用户活跃安装数量
     */
    long countByUserIdAndIsActiveTrue(String userId);

    /**
     * 统计聊天活跃安装数量
     */
    long countByChatIdAndIsActiveTrue(String chatId);

    /**
     * 根据机器人ID统计安装数量
     */
    long countByBotId(Long botId);

    /**
     * 统计指定时间以来的活跃安装数量
     */
    @Query("SELECT COUNT(bi) FROM BotInstallation bi WHERE bi.botId = :botId AND " +
           "bi.status = 'ACTIVE' AND bi.lastUsedAt >= :since")
    long countActiveInstallationsSince(@Param("botId") Long botId, @Param("since") Instant since);

    /**
     * 根据机器人ID查找所有安装
     */
    List<BotInstallation> findByBotId(Long botId);

    /**
     * 根据用户ID、机器人ID和状态查找安装
     */
    List<BotInstallation> findByUserIdAndBotIdAndStatus(String userId, Long botId,
                                                       BotInstallation.InstallationStatus status);
}
