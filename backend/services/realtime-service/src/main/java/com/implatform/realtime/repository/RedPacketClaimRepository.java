package com.implatform.realtime.repository;

import com.implatform.realtime.entity.RedPacketClaim;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 红包领取记录数据访问接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface RedPacketClaimRepository extends R2dbcRepository<RedPacketClaim, Long> {

    /**
     * 根据红包ID查找领取记录
     */
    List<RedPacketClaim> findByRedPacketIdOrderByClaimTimeAsc(Long redPacketId);

    /**
     * 根据用户ID查找领取记录
     */
    Page<RedPacketClaim> findByUserIdOrderByClaimTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据红包ID和用户ID查找领取记录
     */
    Optional<RedPacketClaim> findByRedPacketIdAndUserId(Long redPacketId, Long userId);

    /**
     * 检查用户是否已经领取过指定红包
     */
    boolean existsByRedPacketIdAndUserId(Long redPacketId, Long userId);

    /**
     * 统计红包的领取次数
     */
    @Query("SELECT COUNT(rpc) FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId")
    Long countByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计红包的已领取金额
     */
    @Query("SELECT COALESCE(SUM(rpc.amount), 0) FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId AND rpc.status = 'SUCCESS'")
    BigDecimal sumAmountByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 获取红包的下一个领取顺序号
     */
    @Query("SELECT COALESCE(MAX(rpc.claimOrder), 0) + 1 FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId")
    Integer getNextClaimOrder(@Param("redPacketId") Long redPacketId);

    /**
     * 查找用户在指定时间范围内的领取记录
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.userId = :userId " +
           "AND rpc.claimTime BETWEEN :startTime AND :endTime " +
           "ORDER BY rpc.claimTime DESC")
    List<RedPacketClaim> findByUserIdAndClaimTimeBetween(@Param("userId") Long userId, 
                                                        @Param("startTime") Instant startTime, 
                                                        @Param("endTime") Instant endTime);

    /**
     * 统计用户领取的红包数量
     */
    @Query("SELECT COUNT(rpc) FROM RedPacketClaim rpc WHERE rpc.userId = :userId AND rpc.status = 'SUCCESS'")
    Long countSuccessfulClaimsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户领取的红包总金额
     */
    @Query("SELECT COALESCE(SUM(rpc.amount), 0) FROM RedPacketClaim rpc WHERE rpc.userId = :userId AND rpc.status = 'SUCCESS'")
    BigDecimal sumAmountByUserId(@Param("userId") Long userId);

    /**
     * 查找红包的最大领取金额记录
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId " +
           "AND rpc.amount = (SELECT MAX(rpc2.amount) FROM RedPacketClaim rpc2 WHERE rpc2.redPacket.id = :redPacketId)")
    List<RedPacketClaim> findMaxAmountClaimsByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 查找红包的手气王（金额最大的领取者）
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId " +
           "ORDER BY rpc.amount DESC, rpc.claimTime ASC")
    List<RedPacketClaim> findLuckyKingByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计指定时间范围内的领取统计
     */
    @Query("SELECT new map(" +
           "COUNT(rpc) as totalClaims, " +
           "SUM(rpc.amount) as totalAmount, " +
           "AVG(rpc.amount) as avgAmount, " +
           "COUNT(DISTINCT rpc.userId) as uniqueUsers" +
           ") FROM RedPacketClaim rpc WHERE rpc.claimTime BETWEEN :startTime AND :endTime")
    List<Object> getClaimStatistics(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 查找用户最近的领取记录
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.userId = :userId " +
           "ORDER BY rpc.claimTime DESC")
    Page<RedPacketClaim> findRecentClaimsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找红包的领取排行榜
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId " +
           "ORDER BY rpc.amount DESC, rpc.claimTime ASC")
    List<RedPacketClaim> findClaimRankingByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计用户今日领取情况
     */
    @Query("SELECT new map(" +
           "COUNT(rpc) as todayClaims, " +
           "COALESCE(SUM(rpc.amount), 0) as todayAmount" +
           ") FROM RedPacketClaim rpc WHERE rpc.userId = :userId " +
           "AND rpc.claimTime >= :todayStart AND rpc.claimTime < :tomorrowStart")
    List<Object> getTodayClaimStatistics(@Param("userId") Long userId, 
                                        @Param("todayStart") Instant todayStart, 
                                        @Param("tomorrowStart") Instant tomorrowStart);

    /**
     * 查找指定红包的前N个领取者
     */
    @Query("SELECT rpc FROM RedPacketClaim rpc WHERE rpc.redPacket.id = :redPacketId " +
           "ORDER BY rpc.claimOrder ASC")
    List<RedPacketClaim> findTopClaimersByRedPacketId(@Param("redPacketId") Long redPacketId, Pageable pageable);
}
