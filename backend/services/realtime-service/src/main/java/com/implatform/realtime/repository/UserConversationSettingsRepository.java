package com.implatform.realtime.repository;

import java.time.Instant;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.realtime.dto.UserConversationStatistics;
import com.implatform.realtime.entity.UserConversationSettings;

/**
 * 用户对话设置Repository - R2DBC响应式版本
 *
 * <p><strong>设计特点</strong>：
 * 针对用户个性化设置的查询场景进行优化，包括对话列表查询、统计查询、
 * 批量操作等。通过合理的索引设计和查询优化提升性能。
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface UserConversationSettingsRepository extends R2dbcRepository<UserConversationSettings, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据用户ID和对话ID查找设置
     */
    Optional<UserConversationSettings> findByUserIdAndConversationId(Long userId, Long conversationId);

    /**
     * 检查用户对话设置是否存在
     */
    boolean existsByUserIdAndConversationId(Long userId, Long conversationId);

    // ==================== 用户对话列表查询 ====================

    /**
     * 查询用户的活跃对话列表（按更新时间排序）
     */
    Page<UserConversationSettings> findByUserIdAndStatusOrderByUpdatedAtDesc(
        Long userId, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    /**
     * 查询用户的置顶对话列表
     */
    Page<UserConversationSettings> findByUserIdAndIsPinnedTrueAndStatusOrderByPinnedAtDesc(
        Long userId, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    /**
     * 查询用户的免打扰对话列表
     */
    Page<UserConversationSettings> findByUserIdAndIsMutedTrueAndStatus(
        Long userId, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    /**
     * 查询用户的未读对话列表
     */
    Page<UserConversationSettings> findByUserIdAndUnreadCountGreaterThanAndStatus(
        Long userId, Integer unreadCount, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    /**
     * 查询用户的隐藏对话列表
     */
    Page<UserConversationSettings> findByUserIdAndIsHiddenTrueAndStatus(
        Long userId, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    // ==================== 搜索查询 ====================

    /**
     * 根据自定义名称搜索用户对话
     */
    Page<UserConversationSettings> findByUserIdAndCustomNameContainingIgnoreCaseAndStatus(
        Long userId, String keyword, UserConversationSettings.UserConversationStatus status, Pageable pageable);

    /**
     * 根据对话ID列表查询用户设置
     */
    List<UserConversationSettings> findByUserIdAndConversationIdInAndStatus(
        Long userId, List<Long> conversationIds, UserConversationSettings.UserConversationStatus status);

    // ==================== 统计查询 ====================

    /**
     * 统计用户总未读消息数
     */
    @Query("SELECT COALESCE(SUM(s.unreadCount), 0) FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.status = 'ACTIVE'")
    Integer getTotalUnreadCountByUserId(@Param("userId") Long userId);

    /**
     * 统计用户各状态对话数量
     */
    @Query("SELECT s.status, COUNT(s) FROM UserConversationSettings s " +
           "WHERE s.userId = :userId GROUP BY s.status")
    List<Object[]> getConversationCountByStatus(@Param("userId") Long userId);

    /**
     * 统计用户置顶对话数量
     */
    @Query("SELECT COUNT(s) FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.isPinned = true AND s.status = 'ACTIVE'")
    Long getPinnedConversationCount(@Param("userId") Long userId);

    /**
     * 统计用户免打扰对话数量
     */
    @Query("SELECT COUNT(s) FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.isMuted = true AND s.status = 'ACTIVE'")
    Long getMutedConversationCount(@Param("userId") Long userId);

    /**
     * 获取用户对话统计信息
     */
    @Query("SELECT new com.implatform.realtime.dto.UserConversationStatistics(" +
           "COUNT(s), " +
           "SUM(CASE WHEN s.isPinned = true THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN s.isMuted = true THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN s.unreadCount > 0 THEN 1 ELSE 0 END), " +
           "COALESCE(SUM(s.unreadCount), 0), " +
           "SUM(s.accessCount)" +
           ") FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.status = 'ACTIVE'")
    UserConversationStatistics getUserConversationStatistics(@Param("userId") Long userId);

    // ==================== 批量操作 ====================

    /**
     * 批量标记用户对话为已读
     */
    @Modifying
    @Query("UPDATE UserConversationSettings s SET s.unreadCount = 0, s.lastReadAt = :readTime, s.updatedAt = :updateTime " +
           "WHERE s.userId = :userId AND s.conversationId IN :conversationIds")
    int batchMarkAsRead(@Param("userId") Long userId, 
                       @Param("conversationIds") List<Long> conversationIds,
                       @Param("readTime") Instant readTime,
                       @Param("updateTime") Instant updateTime);

    /**
     * 批量设置对话免打扰
     */
    @Modifying
    @Query("UPDATE UserConversationSettings s SET s.isMuted = :isMuted, s.mutedUntil = :mutedUntil, s.updatedAt = :updateTime " +
           "WHERE s.userId = :userId AND s.conversationId IN :conversationIds")
    int batchMuteConversations(@Param("userId") Long userId,
                              @Param("conversationIds") List<Long> conversationIds,
                              @Param("isMuted") Boolean isMuted,
                              @Param("mutedUntil") Instant mutedUntil,
                              @Param("updateTime") Instant updateTime);

    /**
     * 批量隐藏对话
     */
    @Modifying
    @Query("UPDATE UserConversationSettings s SET s.isHidden = true, s.hiddenAt = :hiddenTime, " +
           "s.status = 'HIDDEN', s.updatedAt = :updateTime " +
           "WHERE s.userId = :userId AND s.conversationId IN :conversationIds")
    int batchHideConversations(@Param("userId") Long userId,
                              @Param("conversationIds") List<Long> conversationIds,
                              @Param("hiddenTime") Instant hiddenTime,
                              @Param("updateTime") Instant updateTime);

    // ==================== 清理操作 ====================

    /**
     * 删除指定对话的所有用户设置（对话删除时调用）
     */
    @Modifying
    @Query("DELETE FROM UserConversationSettings s WHERE s.conversationId = :conversationId")
    int deleteByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 删除用户的所有对话设置（用户注销时调用）
     */
    @Modifying
    @Query("DELETE FROM UserConversationSettings s WHERE s.userId = :userId")
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 清理过期的免打扰设置
     */
    @Modifying
    @Query("UPDATE UserConversationSettings s SET s.isMuted = false, s.mutedUntil = null, s.updatedAt = :updateTime " +
           "WHERE s.isMuted = true AND s.mutedUntil IS NOT NULL AND s.mutedUntil < :currentTime")
    int cleanupExpiredMuteSettings(@Param("currentTime") Instant currentTime,
                                  @Param("updateTime") Instant updateTime);

    // ==================== 高级查询 ====================

    /**
     * 查询用户最近访问的对话
     */
    @Query("SELECT s FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.status = 'ACTIVE' AND s.lastAccessedAt IS NOT NULL " +
           "ORDER BY s.lastAccessedAt DESC")
    List<UserConversationSettings> findRecentlyAccessedConversations(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查询用户最活跃的对话（按访问次数排序）
     */
    @Query("SELECT s FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.status = 'ACTIVE' AND s.accessCount > 0 " +
           "ORDER BY s.accessCount DESC")
    List<UserConversationSettings> findMostActiveConversations(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查询有草稿的对话
     */
    @Query("SELECT s FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.status = 'ACTIVE' AND s.draftContent IS NOT NULL " +
           "ORDER BY s.draftAt DESC")
    List<UserConversationSettings> findConversationsWithDraft(@Param("userId") Long userId);

    /**
     * 查询需要同步的设置（用于增量同步）
     */
    @Query("SELECT s FROM UserConversationSettings s " +
           "WHERE s.userId = :userId AND s.updatedAt > :lastSyncTime " +
           "ORDER BY s.updatedAt ASC")
    List<UserConversationSettings> findSettingsToSync(@Param("userId") Long userId,
                                                      @Param("lastSyncTime") Instant lastSyncTime);

    // ==================== 性能优化查询 ====================

    /**
     * 批量查询多个用户的未读消息统计（用于推送服务）
     */
    @Query("SELECT s.userId, COALESCE(SUM(s.unreadCount), 0) FROM UserConversationSettings s " +
           "WHERE s.userId IN :userIds AND s.status = 'ACTIVE' " +
           "GROUP BY s.userId")
    List<Object[]> getBatchUnreadCounts(@Param("userIds") List<Long> userIds);

    /**
     * 查询对话的所有用户设置（用于广播通知）
     */
    @Query("SELECT s FROM UserConversationSettings s " +
           "WHERE s.conversationId = :conversationId AND s.status = 'ACTIVE'")
    List<UserConversationSettings> findAllUserSettingsByConversationId(@Param("conversationId") Long conversationId);

    /**
     * 查询需要通知的用户（排除免打扰用户）
     */
    @Query("SELECT s.userId FROM UserConversationSettings s " +
           "WHERE s.conversationId = :conversationId AND s.status = 'ACTIVE' " +
           "AND (s.isMuted = false OR (s.mutedUntil IS NOT NULL AND s.mutedUntil < :currentTime))")
    List<Long> findNotifiableUserIds(@Param("conversationId") Long conversationId,
                                    @Param("currentTime") Instant currentTime);

    // ==================== 数据维护查询 ====================

    /**
     * 查找孤儿设置记录（对应的对话已删除）
     */
    @Query(value = "SELECT s.* FROM user_conversation_settings s " +
                   "LEFT JOIN conversations c ON s.conversation_id = c.id " +
                   "WHERE c.id IS NULL", nativeQuery = true)
    List<UserConversationSettings> findOrphanedSettings();

    /**
     * 统计数据库中的设置记录数量
     */
    @Query("SELECT COUNT(s) FROM UserConversationSettings s WHERE s.status = :status")
    Long countByStatus(@Param("status") UserConversationSettings.UserConversationStatus status);
}
