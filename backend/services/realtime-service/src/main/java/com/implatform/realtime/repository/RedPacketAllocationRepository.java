package com.implatform.realtime.repository;

import com.implatform.realtime.entity.RedPacketAllocation;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 红包分配明细数据访问接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface RedPacketAllocationRepository extends R2dbcRepository<RedPacketAllocation, Long> {

    /**
     * 根据红包ID查找所有分配记录
     */
    List<RedPacketAllocation> findByRedPacketIdOrderByAllocationOrderAsc(Long redPacketId);

    /**
     * 根据红包ID查找未领取的分配记录
     */
    List<RedPacketAllocation> findByRedPacketIdAndIsClaimedFalseOrderByAllocationOrderAsc(Long redPacketId);

    /**
     * 根据红包ID查找已领取的分配记录
     */
    List<RedPacketAllocation> findByRedPacketIdAndIsClaimedTrueOrderByAllocationOrderAsc(Long redPacketId);

    /**
     * 查找下一个可领取的分配记录（加悲观锁）
     */
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Query("SELECT rpa FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId " +
           "AND rpa.isClaimed = false ORDER BY rpa.allocationOrder ASC")
    List<RedPacketAllocation> findNextAvailableAllocationWithLock(@Param("redPacketId") Long redPacketId);

    /**
     * 根据红包ID和分配顺序查找分配记录
     */
    Optional<RedPacketAllocation> findByRedPacketIdAndAllocationOrder(Long redPacketId, Integer allocationOrder);

    /**
     * 统计红包的未领取分配数量
     */
    @Query("SELECT COUNT(rpa) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId AND rpa.isClaimed = false")
    Long countUnclaimedByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计红包的已领取分配数量
     */
    @Query("SELECT COUNT(rpa) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId AND rpa.isClaimed = true")
    Long countClaimedByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 批量标记分配记录为已领取
     */
    @Modifying
    @Query("UPDATE RedPacketAllocation rpa SET rpa.isClaimed = true, rpa.claimedBy = :userId, rpa.claimedAt = CURRENT_TIMESTAMP " +
           "WHERE rpa.id = :allocationId AND rpa.isClaimed = false")
    int markAllocationAsClaimed(@Param("allocationId") Long allocationId, @Param("userId") Long userId);

    /**
     * 查找用户领取的分配记录
     */
    List<RedPacketAllocation> findByClaimedByOrderByClaimedAtDesc(Long userId);

    /**
     * 检查红包是否还有未分配的记录
     */
    @Query("SELECT COUNT(rpa) > 0 FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId AND rpa.isClaimed = false")
    boolean hasUnclaimedAllocations(@Param("redPacketId") Long redPacketId);

    /**
     * 删除红包的所有分配记录
     */
    @Modifying
    @Query("DELETE FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId")
    int deleteByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 查找红包分配的最大金额
     */
    @Query("SELECT MAX(rpa.amount) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId")
    Optional<java.math.BigDecimal> findMaxAmountByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 查找红包分配的最小金额
     */
    @Query("SELECT MIN(rpa.amount) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId")
    Optional<java.math.BigDecimal> findMinAmountByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计红包分配的总金额
     */
    @Query("SELECT COALESCE(SUM(rpa.amount), 0) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId")
    java.math.BigDecimal sumAmountByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计红包已领取分配的总金额
     */
    @Query("SELECT COALESCE(SUM(rpa.amount), 0) FROM RedPacketAllocation rpa WHERE rpa.redPacket.id = :redPacketId AND rpa.isClaimed = true")
    java.math.BigDecimal sumClaimedAmountByRedPacketId(@Param("redPacketId") Long redPacketId);
}
