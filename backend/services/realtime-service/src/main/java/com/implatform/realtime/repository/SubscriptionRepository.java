package com.implatform.realtime.repository;

import com.implatform.realtime.entity.Subscription;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 订阅Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SubscriptionRepository extends R2dbcRepository<Subscription, Long> {

    /**
     * 根据订阅号查找订阅记录
     */
    Optional<Subscription> findBySubscriptionNo(String subscriptionNo);

    /**
     * 根据用户ID查找订阅记录（不分页）
     */
    List<Subscription> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据用户ID查找订阅记录（分页）
     */
    Page<Subscription> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找订阅记录
     */
    List<Subscription> findByUserIdAndStatus(Long userId, Subscription.SubscriptionStatus status);

    /**
     * 根据用户ID查找活跃订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.userId = :userId AND s.status = 'ACTIVE' AND s.endDate > :now")
    List<Subscription> findActiveSubscriptionsByUserId(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 根据计划ID查找订阅记录
     */
    Page<Subscription> findByPlanIdOrderByCreatedAtDesc(Long planId, Pageable pageable);

    /**
     * 根据订阅类型查找订阅记录
     */
    Page<Subscription> findBySubscriptionTypeOrderByCreatedAtDesc(Subscription.SubscriptionType subscriptionType, Pageable pageable);

    /**
     * 根据状态查找订阅记录
     */
    Page<Subscription> findByStatusOrderByCreatedAtDesc(Subscription.SubscriptionStatus status, Pageable pageable);

    /**
     * 查找即将到期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND s.endDate BETWEEN :now AND :threshold")
    List<Subscription> findSubscriptionsExpiringBetween(@Param("now") LocalDateTime now, @Param("threshold") LocalDateTime threshold);

    /**
     * 查找需要续费的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.autoRenew = true AND s.status = 'ACTIVE' AND s.nextBillingDate <= :now")
    List<Subscription> findSubscriptionsNeedingRenewal(@Param("now") LocalDateTime now);

    /**
     * 查找已过期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'ACTIVE' AND s.endDate < :now")
    List<Subscription> findExpiredSubscriptions(@Param("now") LocalDateTime now);

    /**
     * 查找已过期的订阅（按状态和时间）
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = :status AND s.endTime < :now")
    List<Subscription> findExpiredSubscriptions(@Param("status") Subscription.SubscriptionStatus status, @Param("now") Instant now);

    /**
     * 查找试用期即将结束的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'TRIAL' AND s.trialEndDate BETWEEN :now AND :threshold")
    List<Subscription> findTrialSubscriptionsExpiringSoon(@Param("now") LocalDateTime now, @Param("threshold") LocalDateTime threshold);

    /**
     * 统计用户订阅总金额
     */
    @Query("SELECT SUM(s.price) FROM Subscription s WHERE s.userId = :userId AND s.status IN ('ACTIVE', 'TRIAL')")
    BigDecimal sumActiveSubscriptionsByUserId(@Param("userId") Long userId);

    /**
     * 统计指定时间范围内的订阅收入
     */
    @Query("SELECT SUM(s.price) FROM Subscription s WHERE s.status = 'ACTIVE' AND s.startDate BETWEEN :startDate AND :endDate")
    BigDecimal sumSubscriptionRevenueByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计指定时间范围内的新订阅数量
     */
    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.startDate BETWEEN :startDate AND :endDate")
    Long countNewSubscriptionsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 统计各订阅类型的使用情况
     */
    @Query("SELECT s.subscriptionType, COUNT(s), SUM(s.price) FROM Subscription s WHERE s.status IN ('ACTIVE', 'TRIAL') GROUP BY s.subscriptionType")
    List<Object[]> getSubscriptionTypeStatistics();

    /**
     * 统计各计费周期的使用情况
     */
    @Query("SELECT s.billingCycle, COUNT(s), SUM(s.price) FROM Subscription s WHERE s.status IN ('ACTIVE', 'TRIAL') GROUP BY s.billingCycle")
    List<Object[]> getBillingCycleStatistics();

    /**
     * 获取每日订阅统计
     */
    @Query("SELECT DATE(s.startDate) as subscriptionDate, COUNT(s) as subscriptionCount, SUM(s.price) as totalRevenue " +
           "FROM Subscription s WHERE s.startDate >= :startDate " +
           "GROUP BY DATE(s.startDate) ORDER BY subscriptionDate DESC")
    List<Object[]> getDailySubscriptionStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 获取月度订阅统计
     */
    @Query("SELECT YEAR(s.startDate) as year, MONTH(s.startDate) as month, COUNT(s) as subscriptionCount, SUM(s.price) as totalRevenue " +
           "FROM Subscription s WHERE s.startDate >= :startDate " +
           "GROUP BY YEAR(s.startDate), MONTH(s.startDate) ORDER BY year DESC, month DESC")
    List<Object[]> getMonthlySubscriptionStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找用户的当前活跃订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.userId = :userId AND s.status = 'ACTIVE' AND s.endDate > :now ORDER BY s.endDate DESC")
    Optional<Subscription> findCurrentActiveSubscription(@Param("userId") Long userId, @Param("now") LocalDateTime now);

    /**
     * 检查用户是否有指定类型的活跃订阅
     */
    @Query("SELECT COUNT(s) > 0 FROM Subscription s WHERE s.userId = :userId AND s.subscriptionType = :type AND s.status = 'ACTIVE' AND s.endDate > :now")
    boolean hasActiveSubscriptionOfType(@Param("userId") Long userId, @Param("type") Subscription.SubscriptionType type, @Param("now") LocalDateTime now);

    /**
     * 查找暂停的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = 'PAUSED' AND s.pausedAt < :threshold")
    List<Subscription> findLongPausedSubscriptions(@Param("threshold") LocalDateTime threshold);

    /**
     * 统计订阅流失率
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN s.status = 'CANCELLED' THEN 1 END) as cancelledCount, " +
           "COUNT(CASE WHEN s.status = 'ACTIVE' THEN 1 END) as activeCount, " +
           "COUNT(s) as totalCount " +
           "FROM Subscription s WHERE s.createdAt >= :startDate")
    Object[] getSubscriptionChurnStatistics(@Param("startDate") LocalDateTime startDate);

    /**
     * 查找高价值订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.price >= :threshold AND s.status = 'ACTIVE' ORDER BY s.price DESC")
    Page<Subscription> findHighValueSubscriptions(@Param("threshold") BigDecimal threshold, Pageable pageable);

    /**
     * 查找自动续费关闭的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.autoRenew = false AND s.status = 'ACTIVE' AND s.endDate BETWEEN :now AND :threshold")
    List<Subscription> findNonAutoRenewSubscriptionsExpiringSoon(@Param("now") LocalDateTime now, @Param("threshold") LocalDateTime threshold);

    /**
     * 统计用户订阅历史
     */
    @Query("SELECT COUNT(s), MIN(s.startDate), MAX(s.endDate), SUM(s.price) FROM Subscription s WHERE s.userId = :userId")
    Object[] getUserSubscriptionHistory(@Param("userId") Long userId);

    /**
     * 查找重复订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.userId = :userId AND s.planId = :planId AND s.status IN ('ACTIVE', 'TRIAL') AND s.endDate > :now")
    List<Subscription> findDuplicateActiveSubscriptions(@Param("userId") Long userId, @Param("planId") Long planId, @Param("now") LocalDateTime now);

    /**
     * 获取订阅留存率统计
     */
    @Query("SELECT s.billingCycle, " +
           "COUNT(CASE WHEN s.status = 'ACTIVE' AND s.autoRenew = true THEN 1 END) as retainedCount, " +
           "COUNT(s) as totalCount " +
           "FROM Subscription s WHERE s.startDate >= :startDate " +
           "GROUP BY s.billingCycle")
    List<Object[]> getSubscriptionRetentionStatistics(@Param("startDate") LocalDateTime startDate);

    // ==================== 新增缺失的方法 ====================

    /**
     * 查找活跃订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.userId = :userId AND s.status = :status")
    List<Subscription> findActiveSubscriptions(@Param("userId") Long userId, @Param("status") Subscription.SubscriptionStatus status);

    /**
     * 根据订阅号查找
     */
    @Query("SELECT s FROM Subscription s WHERE s.subscriptionNo = :subscriptionNumber")
    Optional<Subscription> findBySubscriptionNumber(@Param("subscriptionNumber") String subscriptionNumber);

    /**
     * 检查是否存在活跃订阅
     */
    @Query("SELECT COUNT(s) > 0 FROM Subscription s WHERE s.userId = :userId AND s.planId = :planId AND s.status = :status")
    boolean existsActiveSubscription(@Param("userId") Long userId, @Param("planId") Long planId, @Param("status") Subscription.SubscriptionStatus status);

    /**
     * 查找即将到期的订阅
     */
    @Query("SELECT s FROM Subscription s WHERE s.status = :status AND s.endTime <= :threshold")
    List<Subscription> findExpiringSubscriptions(@Param("status") Subscription.SubscriptionStatus status, @Param("threshold") Instant threshold);

    /**
     * 按状态统计
     */
    @Query("SELECT s.status, COUNT(s) FROM Subscription s GROUP BY s.status")
    List<Object[]> countByStatus();

    /**
     * 按订阅计划统计
     */
    @Query("SELECT s.planId, COUNT(s) FROM Subscription s GROUP BY s.planId")
    List<Object[]> countBySubscriptionPlan();

    /**
     * 获取收入统计
     */
    @Query("SELECT SUM(s.price), AVG(s.price), COUNT(s) FROM Subscription s WHERE s.status = 'ACTIVE'")
    Object[] getRevenueStatistics();

    /**
     * 获取续费统计
     */
    @Query("SELECT COUNT(CASE WHEN s.autoRenew = true THEN 1 END), COUNT(s) FROM Subscription s WHERE s.status = 'ACTIVE'")
    Object[] getRenewalStatistics();

    /**
     * 获取流失统计
     */
    @Query("SELECT COUNT(CASE WHEN s.status = 'CANCELLED' THEN 1 END), COUNT(s) FROM Subscription s")
    Object[] getChurnStatistics();

    /**
     * 获取订阅趋势
     */
    @Query("SELECT DATE(s.createdAt), COUNT(s) FROM Subscription s WHERE s.createdAt >= :since GROUP BY DATE(s.createdAt) ORDER BY DATE(s.createdAt)")
    List<Object[]> getSubscriptionTrends(@Param("since") Instant since);

    /**
     * 统计活跃订阅数量
     */
    @Query("SELECT COUNT(s) FROM Subscription s WHERE s.userId = :userId AND s.status = :status")
    long countActiveSubscriptions(@Param("userId") Long userId, @Param("status") Subscription.SubscriptionStatus status);
}
