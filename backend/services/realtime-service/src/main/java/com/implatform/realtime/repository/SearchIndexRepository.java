package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchIndex;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;

/**
 * 搜索索引Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchIndexRepository extends R2dbcRepository<SearchIndex, Long> {

    /**
     * 根据实体类型和ID查找索引
     */
    @Query("SELECT * FROM search_index WHERE entity_type = :entityType AND entity_id = :entityId")
    Mono<SearchIndex> findByEntityTypeAndEntityId(@Param("entityType") String entityType, @Param("entityId") Long entityId);

    /**
     * 检查索引是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM search_index WHERE entity_type = :entityType AND entity_id = :entityId")
    Mono<Boolean> existsByEntityTypeAndEntityId(@Param("entityType") String entityType, @Param("entityId") Long entityId);

    /**
     * 全文搜索（使用PostgreSQL的tsvector）
     */
    @Query("SELECT * FROM search_index " +
           "WHERE is_active = true AND search_vector @@ plainto_tsquery('english', :query) " +
           "ORDER BY ts_rank(search_vector, plainto_tsquery('english', :query)) DESC " +
           "LIMIT :limit OFFSET :offset")
    Flux<SearchIndex> fullTextSearch(@Param("query") String query, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 按实体类型全文搜索
     */
    @Query("SELECT * FROM search_index " +
           "WHERE is_active = true AND entity_type = :entityType " +
           "AND search_vector @@ plainto_tsquery('english', :query) " +
           "ORDER BY ts_rank(search_vector, plainto_tsquery('english', :query)) DESC " +
           "LIMIT :limit OFFSET :offset")
    Flux<SearchIndex> fullTextSearchByType(@Param("query") String query,
                                          @Param("entityType") String entityType,
                                          @Param("limit") int limit,
                                          @Param("offset") long offset);

    /**
     * 简单关键词搜索
     */
    @Query("SELECT * FROM search_index WHERE is_active = true AND " +
           "(LOWER(title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(content) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY " +
           "CASE WHEN LOWER(title) LIKE LOWER(CONCAT('%', :keyword, '%')) THEN 1 ELSE 2 END, " +
           "created_at DESC " +
           "LIMIT :limit OFFSET :offset")
    Flux<SearchIndex> searchByKeyword(@Param("keyword") String keyword, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 按实体类型搜索
     */
    @Query("SELECT * FROM search_index WHERE is_active = true AND entity_type = :entityType AND " +
           "(LOWER(title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(content) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY created_at DESC " +
           "LIMIT :limit OFFSET :offset")
    Flux<SearchIndex> searchByTypeAndKeyword(@Param("entityType") String entityType,
                                           @Param("keyword") String keyword,
                                           @Param("limit") int limit,
                                           @Param("offset") long offset);

    /**
     * 按创建者搜索
     */
    @Query("SELECT s FROM SearchIndex s WHERE s.isActive = true AND s.creatorId = :creatorId AND " +
           "(LOWER(s.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(s.content) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY s.createdAt DESC")
    Page<SearchIndex> searchByCreatorAndKeyword(@Param("creatorId") Long creatorId,
                                              @Param("keyword") String keyword, 
                                              Pageable pageable);

    /**
     * 按标签搜索
     */
    @Query(value = "SELECT * FROM search_index s WHERE s.is_active = true AND " +
           ":tag = ANY(s.tags) " +
           "ORDER BY s.created_at DESC",
           nativeQuery = true)
    Page<SearchIndex> searchByTag(@Param("tag") String tag, Pageable pageable);

    /**
     * 按关键词数组搜索
     */
    @Query(value = "SELECT * FROM search_index s WHERE s.is_active = true AND " +
           "EXISTS (SELECT 1 FROM unnest(s.keywords) k WHERE LOWER(k) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY s.created_at DESC",
           nativeQuery = true)
    Page<SearchIndex> searchByKeywords(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 获取最近创建的索引
     */
    @Query("SELECT s FROM SearchIndex s WHERE s.isActive = true " +
           "ORDER BY s.createdAt DESC")
    List<SearchIndex> findRecentIndexes(Pageable pageable);

    /**
     * 获取指定时间范围内的索引
     */
    @Query("SELECT s FROM SearchIndex s WHERE s.isActive = true AND " +
           "s.createdAt BETWEEN :startTime AND :endTime " +
           "ORDER BY s.createdAt DESC")
    List<SearchIndex> findByTimeRange(@Param("startTime") Instant startTime,
                                     @Param("endTime") Instant endTime);

    /**
     * 按实体类型统计数量
     */
    @Query("SELECT s.entityType, COUNT(s) FROM SearchIndex s WHERE s.isActive = true " +
           "GROUP BY s.entityType")
    List<Object[]> countByEntityType();

    /**
     * 获取热门搜索内容
     */
    @Query(value = "SELECT title, COUNT(*) as search_count FROM search_index s " +
                   "JOIN search_history h ON s.entity_type = CAST(h.clicked_entity_type AS VARCHAR) " +
                   "AND s.entity_id = h.clicked_entity_id " +
                   "WHERE s.is_active = true AND h.search_time >= :since " +
                   "GROUP BY title " +
                   "ORDER BY search_count DESC",
           nativeQuery = true)
    List<Object[]> findPopularContent(@Param("since") Instant since, Pageable pageable);

    /**
     * 搜索建议
     */
    @Query("SELECT DISTINCT s.title FROM SearchIndex s WHERE s.isActive = true AND " +
           "LOWER(s.title) LIKE LOWER(CONCAT(:prefix, '%')) " +
           "ORDER BY s.title")
    List<String> findSuggestions(@Param("prefix") String prefix, Pageable pageable);

    /**
     * 高级搜索
     */
    @Query("SELECT s FROM SearchIndex s WHERE s.isActive = true " +
           "AND (:entityTypes IS NULL OR s.entityType IN :entityTypes) " +
           "AND (:creatorId IS NULL OR s.creatorId = :creatorId) " +
           "AND (:startTime IS NULL OR s.createdAt >= :startTime) " +
           "AND (:endTime IS NULL OR s.createdAt <= :endTime) " +
           "AND (:keyword IS NULL OR " +
           "     LOWER(s.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "     LOWER(s.content) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY s.createdAt DESC")
    Page<SearchIndex> advancedSearch(@Param("keyword") String keyword,
                                   @Param("entityTypes") List<SearchIndex.EntityType> entityTypes,
                                   @Param("creatorId") Long creatorId,
                                   @Param("startTime") Instant startTime,
                                   @Param("endTime") Instant endTime,
                                   Pageable pageable);

    /**
     * 删除指定实体的索引
     */
    void deleteByEntityTypeAndEntityId(SearchIndex.EntityType entityType, Long entityId);

    /**
     * 批量删除指定创建者的索引
     */
    void deleteByCreatorId(Long creatorId);

    /**
     * 清理过期的索引
     */
    @Query("DELETE FROM SearchIndex s WHERE s.isActive = false AND s.updatedAt < :cutoffTime")
    void cleanupInactiveIndexes(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 获取索引统计信息
     */
    @Query("SELECT " +
           "COUNT(s) as totalCount, " +
           "COUNT(CASE WHEN s.isActive = true THEN 1 END) as activeCount, " +
           "COUNT(DISTINCT s.creatorId) as uniqueCreators, " +
           "AVG(LENGTH(s.content)) as avgContentLength " +
           "FROM SearchIndex s")
    Object[] getIndexStatistics();
}
