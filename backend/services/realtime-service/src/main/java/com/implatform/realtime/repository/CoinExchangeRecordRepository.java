package com.implatform.realtime.repository;

import com.implatform.realtime.entity.CoinExchangeRecord;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 金币兑换记录Repository接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface CoinExchangeRecordRepository extends R2dbcRepository<CoinExchangeRecord, Long> {

    /**
     * 根据兑换订单号查找记录
     */
    @Query("SELECT * FROM coin_exchange_records WHERE exchange_order_no = :exchangeOrderNo")
    Mono<CoinExchangeRecord> findByExchangeOrderNo(@Param("exchangeOrderNo") String exchangeOrderNo);

    /**
     * 根据兑换订单号查找记录（加锁）
     */
    @Query("SELECT * FROM coin_exchange_records WHERE exchange_order_no = :exchangeOrderNo FOR UPDATE")
    Mono<CoinExchangeRecord> findByExchangeOrderNoWithLock(@Param("exchangeOrderNo") String exchangeOrderNo);

    /**
     * 检查兑换订单号是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM coin_exchange_records WHERE exchange_order_no = :exchangeOrderNo")
    Mono<Boolean> existsByExchangeOrderNo(@Param("exchangeOrderNo") String exchangeOrderNo);

    /**
     * 根据钱包交易流水号查找记录
     */
    @Query("SELECT * FROM coin_exchange_records WHERE wallet_transaction_no = :walletTransactionNo")
    Mono<CoinExchangeRecord> findByWalletTransactionNo(@Param("walletTransactionNo") String walletTransactionNo);

    /**
     * 获取用户的兑换记录（分页）
     */
    Page<CoinExchangeRecord> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 获取用户指定状态的兑换记录
     */
    Page<CoinExchangeRecord> findByUserIdAndStatusOrderByCreatedAtDesc(
            Long userId, CoinExchangeRecord.ExchangeStatus status, Pageable pageable);

    /**
     * 获取用户指定时间范围内的兑换记录
     */
    @Query("SELECT * FROM coin_exchange_records WHERE user_id = :userId AND created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<CoinExchangeRecord> findByUserIdAndCreatedAtBetweenOrderByCreatedAtDesc(
            @Param("userId") Long userId, @Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 统计用户兑换总次数
     */
    @Query("SELECT COUNT(*) FROM coin_exchange_records WHERE user_id = :userId")
    Mono<Long> countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户成功兑换次数
     */
    @Query("SELECT COUNT(*) FROM coin_exchange_records WHERE user_id = :userId AND status = :status")
    Mono<Long> countByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 统计用户兑换总金币数
     */
    @Query("SELECT COALESCE(SUM(coin_amount), 0) FROM coin_exchange_records " +
           "WHERE user_id = :userId AND status = 'SUCCESS'")
    Mono<Integer> sumExchangedCoinsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户兑换总金额
     */
    @Query("SELECT COALESCE(SUM(wallet_amount), 0) FROM coin_exchange_records " +
           "WHERE user_id = :userId AND status = 'SUCCESS'")
    Mono<BigDecimal> sumExchangedAmountByUserId(@Param("userId") Long userId);

    /**
     * 统计用户今日兑换金币数
     */
    @Query("SELECT COALESCE(SUM(coin_amount), 0) FROM coin_exchange_records " +
           "WHERE user_id = :userId AND status = 'SUCCESS' " +
           "AND created_at >= :startOfDay AND created_at < :endOfDay")
    Mono<Integer> sumTodayExchangedCoinsByUserId(@Param("userId") Long userId,
                                       @Param("startOfDay") Instant startOfDay,
                                       @Param("endOfDay") Instant endOfDay);

    /**
     * 获取指定状态的兑换记录
     */
    List<CoinExchangeRecord> findByStatusOrderByCreatedAtAsc(CoinExchangeRecord.ExchangeStatus status);

    /**
     * 获取待处理的兑换记录
     */
    List<CoinExchangeRecord> findByStatusInOrderByCreatedAtAsc(List<CoinExchangeRecord.ExchangeStatus> statuses);

    /**
     * 获取过期的兑换记录
     */
    @Query("SELECT e FROM CoinExchangeRecord e WHERE e.status IN ('PENDING', 'PROCESSING') " +
           "AND e.expiresAt IS NOT NULL AND e.expiresAt < :currentTime")
    List<CoinExchangeRecord> findExpiredRecords(@Param("currentTime") Instant currentTime);

    /**
     * 获取可重试的失败记录
     */
    @Query("SELECT e FROM CoinExchangeRecord e WHERE e.status = 'FAILED' " +
           "AND e.retryCount < e.maxRetryCount")
    List<CoinExchangeRecord> findRetryableRecords();

    /**
     * 获取系统兑换统计
     */
    @Query("SELECT e.status, COUNT(e), SUM(e.coinAmount), SUM(e.walletAmount) " +
           "FROM CoinExchangeRecord e GROUP BY e.status")
    List<Object[]> getExchangeStatistics();

    /**
     * 获取每日兑换统计
     */
    @Query(value = "SELECT DATE(created_at), COUNT(*), SUM(coin_amount), SUM(wallet_amount) " +
           "FROM user_coin_exchange_record WHERE status = 'SUCCESS' " +
           "AND created_at BETWEEN :startTime AND :endTime " +
           "GROUP BY DATE(created_at) ORDER BY DATE(created_at)", nativeQuery = true)
    List<Object[]> getDailyExchangeStatistics(@Param("startTime") Instant startTime,
                                              @Param("endTime") Instant endTime);

    /**
     * 获取用户兑换排行榜
     */
    @Query("SELECT e.userId, SUM(e.coinAmount), SUM(e.walletAmount), COUNT(e) " +
           "FROM CoinExchangeRecord e WHERE e.status = 'SUCCESS' " +
           "AND e.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY e.userId ORDER BY SUM(e.coinAmount) DESC")
    List<Object[]> getUserExchangeRanking(@Param("startTime") Instant startTime,
                                          @Param("endTime") Instant endTime,
                                          Pageable pageable);

    /**
     * 获取平均兑换金额
     */
    @Query("SELECT AVG(e.walletAmount) FROM CoinExchangeRecord e WHERE e.status = 'SUCCESS'")
    BigDecimal getAverageExchangeAmount();

    /**
     * 获取平均兑换金币数
     */
    @Query("SELECT AVG(e.coinAmount) FROM CoinExchangeRecord e WHERE e.status = 'SUCCESS'")
    Double getAverageExchangeCoins();

    /**
     * 获取最大单次兑换记录
     */
    Optional<CoinExchangeRecord> findTopByStatusOrderByCoinAmountDesc(CoinExchangeRecord.ExchangeStatus status);

    /**
     * 获取最小单次兑换记录
     */
    Optional<CoinExchangeRecord> findTopByStatusOrderByCoinAmountAsc(CoinExchangeRecord.ExchangeStatus status);

    /**
     * 统计活跃兑换用户数
     */
    @Query("SELECT COUNT(DISTINCT e.userId) FROM CoinExchangeRecord e " +
           "WHERE e.status = 'SUCCESS' AND e.createdAt BETWEEN :startTime AND :endTime")
    long countActiveExchangeUsers(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 获取兑换成功率
     */
    @Query("SELECT " +
           "CAST(SUM(CASE WHEN e.status = 'SUCCESS' THEN 1 ELSE 0 END) AS DOUBLE) / COUNT(e) * 100 " +
           "FROM CoinExchangeRecord e WHERE e.createdAt BETWEEN :startTime AND :endTime")
    Double getExchangeSuccessRate(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 获取平均处理时间（秒）
     */
    @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) " +
           "FROM user_coin_exchange_record WHERE status = 'SUCCESS' AND completed_at IS NOT NULL", nativeQuery = true)
    Double getAverageProcessingTimeSeconds();

    /**
     * 查找异常兑换记录（金额计算错误）
     */
    @Query("SELECT e FROM CoinExchangeRecord e WHERE e.status = 'SUCCESS' " +
           "AND ABS(e.walletAmount - (e.coinAmount / e.exchangeRate)) > 0.01")
    List<CoinExchangeRecord> findAnomalousExchangeRecords();

    /**
     * 查找长时间未完成的记录
     */
    @Query("SELECT e FROM CoinExchangeRecord e WHERE e.status IN ('PENDING', 'PROCESSING') " +
           "AND e.createdAt < :cutoffTime")
    List<CoinExchangeRecord> findLongPendingRecords(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 删除指定时间之前的记录（数据清理）
     */
    @Modifying
    @Transactional
    void deleteByCreatedAtBeforeAndStatusIn(Instant cutoffTime, List<CoinExchangeRecord.ExchangeStatus> statuses);

    /**
     * 获取用户最近的兑换记录
     */
    Optional<CoinExchangeRecord> findTopByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 检查用户是否有进行中的兑换
     */
    boolean existsByUserIdAndStatusIn(Long userId, List<CoinExchangeRecord.ExchangeStatus> statuses);

    /**
     * 获取指定兑换比例的记录数量
     */
    long countByExchangeRate(BigDecimal exchangeRate);

    /**
     * 获取兑换比例统计
     */
    @Query("SELECT e.exchangeRate, COUNT(e), SUM(e.coinAmount) FROM CoinExchangeRecord e " +
           "WHERE e.status = 'SUCCESS' GROUP BY e.exchangeRate ORDER BY e.exchangeRate")
    List<Object[]> getExchangeRateStatistics();
}
