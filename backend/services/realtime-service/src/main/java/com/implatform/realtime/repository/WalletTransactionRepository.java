package com.implatform.realtime.repository;

import com.implatform.realtime.entity.WalletTransaction;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 钱包交易记录Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface WalletTransactionRepository extends R2dbcRepository<WalletTransaction, Long> {

    /**
     * 根据用户ID查找交易记录
     */
    Page<WalletTransaction> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * 根据钱包ID查找交易记录
     */
    Page<WalletTransaction> findByWalletIdOrderByCreatedAtDesc(Long walletId, Pageable pageable);

    /**
     * 根据用户ID和交易类型查找交易记录
     */
    Page<WalletTransaction> findByUserIdAndTransactionTypeOrderByCreatedAtDesc(
            Long userId, WalletTransaction.TransactionType transactionType, Pageable pageable);

    /**
     * 根据用户ID和状态查找交易记录
     */
    Page<WalletTransaction> findByUserIdAndStatusOrderByCreatedAtDesc(
            Long userId, WalletTransaction.TransactionStatus status, Pageable pageable);

    /**
     * 根据业务订单号查找交易记录
     */
    List<WalletTransaction> findByBusinessOrderNo(String businessOrderNo);

    /**
     * 统计用户指定时间范围内的交易金额
     */
    @Query("SELECT SUM(wt.amount) FROM WalletTransaction wt WHERE wt.userId = :userId " +
           "AND wt.transactionType = :transactionType AND wt.status = 'SUCCESS' " +
           "AND wt.createdAt BETWEEN :startDate AND :endDate")
    BigDecimal sumAmountByUserIdAndTypeAndDateRange(@Param("userId") Long userId,
                                                   @Param("transactionType") WalletTransaction.TransactionType transactionType,
                                                   @Param("startDate") Instant startDate,
                                                   @Param("endDate") Instant endDate);

    /**
     * 统计用户指定时间范围内的交易次数
     */
    @Query("SELECT COUNT(wt) FROM WalletTransaction wt WHERE wt.userId = :userId " +
           "AND wt.transactionType = :transactionType AND wt.status = 'SUCCESS' " +
           "AND wt.createdAt BETWEEN :startDate AND :endDate")
    Long countByUserIdAndTypeAndDateRange(@Param("userId") Long userId,
                                         @Param("transactionType") WalletTransaction.TransactionType transactionType,
                                         @Param("startDate") Instant startDate,
                                         @Param("endDate") Instant endDate);

    /**
     * 查找用户最近的交易记录
     */
    @Query("SELECT wt FROM WalletTransaction wt WHERE wt.userId = :userId " +
           "ORDER BY wt.createdAt DESC")
    List<WalletTransaction> findRecentTransactionsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 删除指定时间之前的交易记录
     */
    @Query("DELETE FROM WalletTransaction wt WHERE wt.createdAt < :cutoffDate")
    int deleteTransactionsBeforeDate(@Param("cutoffDate") Instant cutoffDate);
}
