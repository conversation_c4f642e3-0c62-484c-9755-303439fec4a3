package com.implatform.realtime.repository;

import com.implatform.realtime.entity.PinnedMessage;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 消息置顶Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface PinnedMessageRepository extends R2dbcRepository<PinnedMessage, Long> {

    /**
     * 检查消息是否已置顶
     */
    boolean existsByConversationIdAndMessageIdAndIsActiveTrue(Long conversationId, Long messageId);

    /**
     * 根据会话ID和消息ID查找置顶记录
     */
    Optional<PinnedMessage> findByConversationIdAndMessageIdAndIsActiveTrue(Long conversationId, Long messageId);

    /**
     * 获取会话中的所有置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true ORDER BY pm.priority DESC, pm.pinnedAt DESC")
    List<PinnedMessage> findByConversationIdAndIsActiveTrueOrderByPriorityDescPinnedAtDesc(Long conversationId);

    /**
     * 分页获取会话中的置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true ORDER BY pm.priority DESC, pm.pinnedAt DESC")
    Page<PinnedMessage> findByConversationIdAndIsActiveTrueOrderByPriorityDescPinnedAtDesc(Long conversationId, Pageable pageable);

    /**
     * 根据置顶类型获取置顶消息
     */
    List<PinnedMessage> findByConversationIdAndPinTypeAndIsActiveTrueOrderByPriorityDescPinnedAtDesc(
            Long conversationId, PinnedMessage.PinType pinType);

    /**
     * 统计会话中的置顶消息数量
     */
    long countByConversationIdAndIsActiveTrue(Long conversationId);

    /**
     * 统计指定类型的置顶消息数量
     */
    long countByConversationIdAndPinTypeAndIsActiveTrue(Long conversationId, PinnedMessage.PinType pinType);

    /**
     * 获取用户置顶的消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.pinnedBy = :userId AND pm.isActive = true ORDER BY pm.pinnedAt DESC")
    List<PinnedMessage> findByPinnedByAndIsActiveTrueOrderByPinnedAtDesc(@Param("userId") Long userId);

    /**
     * 分页获取用户置顶的消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.pinnedBy = :userId AND pm.isActive = true ORDER BY pm.pinnedAt DESC")
    Page<PinnedMessage> findByPinnedByAndIsActiveTrueOrderByPinnedAtDesc(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取即将过期的置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.isActive = true AND pm.expiresAt IS NOT NULL AND pm.expiresAt <= :cutoffTime")
    List<PinnedMessage> findExpiringSoon(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 获取已过期的置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.isActive = true AND pm.expiresAt IS NOT NULL AND pm.expiresAt < :currentTime")
    List<PinnedMessage> findExpiredPins(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 取消置顶消息
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.isActive = false WHERE pm.conversationId = :conversationId AND pm.messageId = :messageId")
    void unpinMessage(@Param("conversationId") Long conversationId, @Param("messageId") Long messageId);

    /**
     * 批量取消置顶
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.isActive = false WHERE pm.conversationId = :conversationId AND pm.messageId IN :messageIds")
    void batchUnpinMessages(@Param("conversationId") Long conversationId, @Param("messageIds") List<Long> messageIds);

    /**
     * 取消会话中所有置顶消息
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.isActive = false WHERE pm.conversationId = :conversationId")
    void unpinAllMessages(@Param("conversationId") Long conversationId);

    /**
     * 自动取消过期的置顶消息
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.isActive = false WHERE pm.isActive = true AND pm.expiresAt IS NOT NULL AND pm.expiresAt < :currentTime")
    int autoUnpinExpiredMessages(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新置顶优先级
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.priority = :priority WHERE pm.id = :pinId")
    void updatePriority(@Param("pinId") Long pinId, @Param("priority") Integer priority);

    /**
     * 更新置顶类型
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.pinType = :pinType WHERE pm.id = :pinId")
    void updatePinType(@Param("pinId") Long pinId, @Param("pinType") PinnedMessage.PinType pinType);

    /**
     * 更新过期时间
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.expiresAt = :expiresAt WHERE pm.id = :pinId")
    void updateExpiresAt(@Param("pinId") Long pinId, @Param("expiresAt") LocalDateTime expiresAt);

    /**
     * 根据时间范围查找置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.pinnedAt BETWEEN :startTime AND :endTime ORDER BY pm.pinnedAt DESC")
    List<PinnedMessage> findByConversationIdAndTimeRange(@Param("conversationId") Long conversationId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 获取最近置顶的消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true ORDER BY pm.pinnedAt DESC")
    List<PinnedMessage> findRecentPinnedMessages(@Param("conversationId") Long conversationId, Pageable pageable);

    /**
     * 获取高优先级置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true AND pm.priority >= :minPriority ORDER BY pm.priority DESC, pm.pinnedAt DESC")
    List<PinnedMessage> findHighPriorityPins(@Param("conversationId") Long conversationId, @Param("minPriority") Integer minPriority);

    /**
     * 搜索置顶消息（根据置顶原因）
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true AND pm.pinReason LIKE %:keyword% ORDER BY pm.pinnedAt DESC")
    List<PinnedMessage> searchByReason(@Param("conversationId") Long conversationId, @Param("keyword") String keyword);

    /**
     * 获取置顶统计信息
     */
    @Query("SELECT pm.pinType, COUNT(pm) FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true GROUP BY pm.pinType")
    List<Object[]> getPinStatistics(@Param("conversationId") Long conversationId);

    /**
     * 获取每日置顶统计
     */
    @Query("SELECT DATE(pm.pinnedAt), COUNT(pm) FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.pinnedAt >= :startDate GROUP BY DATE(pm.pinnedAt) ORDER BY DATE(pm.pinnedAt)")
    List<Object[]> getDailyPinStatistics(@Param("conversationId") Long conversationId, @Param("startDate") LocalDateTime startDate);

    /**
     * 获取用户置顶统计
     */
    @Query("SELECT pm.pinnedBy, COUNT(pm) FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true GROUP BY pm.pinnedBy ORDER BY COUNT(pm) DESC")
    List<Object[]> getUserPinStatistics(@Param("conversationId") Long conversationId);

    /**
     * 检查用户是否有置顶权限（基于历史记录）
     */
    boolean existsByConversationIdAndPinnedBy(Long conversationId, Long userId);

    /**
     * 获取会话中最大优先级
     */
    @Query("SELECT COALESCE(MAX(pm.priority), 0) FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true")
    Integer getMaxPriority(@Param("conversationId") Long conversationId);

    /**
     * 获取指定优先级范围的置顶消息
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true AND pm.priority BETWEEN :minPriority AND :maxPriority ORDER BY pm.priority DESC, pm.pinnedAt DESC")
    List<PinnedMessage> findByPriorityRange(@Param("conversationId") Long conversationId,
                                           @Param("minPriority") Integer minPriority,
                                           @Param("maxPriority") Integer maxPriority);

    /**
     * 获取永久置顶消息（无过期时间）
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true AND pm.expiresAt IS NULL ORDER BY pm.priority DESC, pm.pinnedAt DESC")
    List<PinnedMessage> findPermanentPins(@Param("conversationId") Long conversationId);

    /**
     * 获取临时置顶消息（有过期时间）
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true AND pm.expiresAt IS NOT NULL ORDER BY pm.expiresAt ASC")
    List<PinnedMessage> findTemporaryPins(@Param("conversationId") Long conversationId);

    /**
     * 检查是否达到置顶数量限制
     */
    @Query("SELECT COUNT(pm) >= :maxPins FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true")
    boolean hasReachedPinLimit(@Param("conversationId") Long conversationId, @Param("maxPins") Integer maxPins);

    /**
     * 获取最旧的置顶消息（用于达到限制时替换）
     */
    @Query("SELECT pm FROM PinnedMessage pm WHERE pm.conversationId = :conversationId AND pm.isActive = true ORDER BY pm.priority ASC, pm.pinnedAt ASC")
    List<PinnedMessage> findOldestPins(@Param("conversationId") Long conversationId, Pageable pageable);

    /**
     * 批量更新置顶优先级
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.priority = pm.priority + :increment WHERE pm.conversationId = :conversationId AND pm.isActive = true")
    void batchUpdatePriority(@Param("conversationId") Long conversationId, @Param("increment") Integer increment);

    /**
     * 重置会话中所有置顶消息的优先级
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.priority = 0 WHERE pm.conversationId = :conversationId AND pm.isActive = true")
    void resetAllPriorities(@Param("conversationId") Long conversationId);

    /**
     * 获取置顶消息的平均存活时间（秒）
     */
    @Query(value = "SELECT AVG(EXTRACT(EPOCH FROM COALESCE(expires_at, CURRENT_TIMESTAMP)) - EXTRACT(EPOCH FROM pinned_at)) FROM pinned_messages WHERE conversation_id = :conversationId", nativeQuery = true)
    Double getAveragePinDuration(@Param("conversationId") Long conversationId);

    /**
     * 获取最活跃的置顶用户
     */
    @Query("SELECT pm.pinnedBy, COUNT(pm) as pinCount FROM PinnedMessage pm WHERE pm.conversationId = :conversationId GROUP BY pm.pinnedBy ORDER BY pinCount DESC")
    List<Object[]> getMostActivePinners(@Param("conversationId") Long conversationId, Pageable pageable);

    /**
     * 删除会话的所有置顶记录
     */
    void deleteByConversationId(Long conversationId);

    /**
     * 软删除用户的所有置顶记录
     */
    @Modifying
    @Query("UPDATE PinnedMessage pm SET pm.isActive = false WHERE pm.pinnedBy = :userId")
    void softDeleteUserPins(@Param("userId") Long userId);

    /**
     * 统计所有活跃的置顶消息数量
     */
    long countByIsActiveTrue();

    /**
     * 统计已过期的置顶消息数量
     */
    @Query("SELECT COUNT(pm) FROM PinnedMessage pm WHERE pm.isActive = true AND pm.expiresAt IS NOT NULL AND pm.expiresAt < :currentTime")
    Long countExpiredPins(@Param("currentTime") LocalDateTime currentTime);
}
