package com.implatform.realtime.repository;

import java.time.Instant;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.realtime.entity.SensitiveWordDetection;

/**
 * 敏感词检测记录Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SensitiveWordDetectionRepository extends R2dbcRepository<SensitiveWordDetection, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据用户ID查询检测记录
     */
    List<SensitiveWordDetection> findByUserId(Long userId);

    /**
     * 根据会话ID查询检测记录
     */
    List<SensitiveWordDetection> findByConversationId(Long conversationId);

    /**
     * 根据消息ID查询检测记录
     */
    List<SensitiveWordDetection> findByMessageId(Long messageId);

    /**
     * 根据检测结果查询
     */
    List<SensitiveWordDetection> findByDetectionResult(SensitiveWordDetection.DetectionResult detectionResult);

    /**
     * 根据采取行动查询
     */
    List<SensitiveWordDetection> findByActionTaken(SensitiveWordDetection.ActionTaken actionTaken);

    // ==================== 分页查询 ====================

    /**
     * 根据用户ID分页查询
     */
    Page<SensitiveWordDetection> findByUserId(Long userId, Pageable pageable);

    /**
     * 根据会话ID分页查询
     */
    Page<SensitiveWordDetection> findByConversationId(Long conversationId, Pageable pageable);

    /**
     * 根据检测结果分页查询
     */
    Page<SensitiveWordDetection> findByDetectionResult(SensitiveWordDetection.DetectionResult detectionResult, Pageable pageable);

    /**
     * 根据采取行动分页查询
     */
    Page<SensitiveWordDetection> findByActionTaken(SensitiveWordDetection.ActionTaken actionTaken, Pageable pageable);

    // ==================== 时间范围查询 ====================

    /**
     * 查询指定时间范围内的检测记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.createdAt BETWEEN :startTime AND :endTime")
    List<SensitiveWordDetection> findByTimeRange(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 查询指定时间范围内的检测记录（分页）
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.createdAt BETWEEN :startTime AND :endTime")
    Page<SensitiveWordDetection> findByTimeRange(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime, Pageable pageable);

    /**
     * 查询用户在指定时间范围内的检测记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.userId = :userId AND d.createdAt BETWEEN :startTime AND :endTime")
    List<SensitiveWordDetection> findByUserIdAndTimeRange(@Param("userId") Long userId, @Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    // ==================== 统计查询 ====================

    /**
     * 统计用户的检测记录数量
     */
    Long countByUserId(Long userId);

    /**
     * 统计指定检测结果的记录数量
     */
    Long countByDetectionResult(SensitiveWordDetection.DetectionResult detectionResult);

    /**
     * 统计指定行动的记录数量
     */
    Long countByActionTaken(SensitiveWordDetection.ActionTaken actionTaken);

    /**
     * 统计时间范围内的检测记录数量
     */
    @Query("SELECT COUNT(d) FROM SensitiveWordDetection d WHERE d.createdAt BETWEEN :startTime AND :endTime")
    Long countByTimeRange(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 统计各检测结果的数量
     */
    @Query("SELECT d.detectionResult, COUNT(d) FROM SensitiveWordDetection d GROUP BY d.detectionResult")
    List<Object[]> countByDetectionResult();

    /**
     * 统计各行动类型的数量
     */
    @Query("SELECT d.actionTaken, COUNT(d) FROM SensitiveWordDetection d GROUP BY d.actionTaken")
    List<Object[]> countByActionTaken();

    // ==================== 性能统计查询 ====================

    /**
     * 查询平均检测时间
     */
    @Query("SELECT AVG(d.detectionTimeMs) FROM SensitiveWordDetection d WHERE d.detectionTimeMs IS NOT NULL")
    Double getAverageDetectionTime();

    /**
     * 查询最大检测时间
     */
    @Query("SELECT MAX(d.detectionTimeMs) FROM SensitiveWordDetection d WHERE d.detectionTimeMs IS NOT NULL")
    Long getMaxDetectionTime();

    /**
     * 查询检测时间超过阈值的记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.detectionTimeMs > :threshold")
    List<SensitiveWordDetection> findSlowDetections(@Param("threshold") Long threshold);

    // ==================== 审核相关查询 ====================

    /**
     * 查询需要审核的记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.actionTaken = 'MARKED_FOR_REVIEW' ORDER BY d.createdAt DESC")
    List<SensitiveWordDetection> findPendingReview();

    /**
     * 分页查询需要审核的记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.actionTaken = 'MARKED_FOR_REVIEW' ORDER BY d.createdAt DESC")
    Page<SensitiveWordDetection> findPendingReview(Pageable pageable);

    /**
     * 统计待审核记录数量
     */
    @Query("SELECT COUNT(d) FROM SensitiveWordDetection d WHERE d.actionTaken = 'MARKED_FOR_REVIEW'")
    Long countPendingReview();

    // ==================== 用户行为分析查询 ====================

    /**
     * 查询用户最近的违规记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE d.userId = :userId AND d.detectionResult != 'CLEAN' ORDER BY d.createdAt DESC")
    List<SensitiveWordDetection> findUserViolations(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计用户违规次数
     */
    @Query("SELECT COUNT(d) FROM SensitiveWordDetection d WHERE d.userId = :userId AND d.detectionResult != 'CLEAN'")
    Long countUserViolations(@Param("userId") Long userId);

    /**
     * 查询频繁违规用户
     */
    @Query("SELECT d.userId, COUNT(d) as violationCount FROM SensitiveWordDetection d " +
           "WHERE d.detectionResult != 'CLEAN' AND d.createdAt >= :since " +
           "GROUP BY d.userId HAVING COUNT(d) >= :threshold ORDER BY violationCount DESC")
    List<Object[]> findFrequentViolators(@Param("since") Instant since, @Param("threshold") Long threshold);

    // ==================== 清理操作 ====================

    /**
     * 删除指定时间之前的检测记录
     */
    @Modifying
    @Query("DELETE FROM SensitiveWordDetection d WHERE d.createdAt < :threshold")
    int deleteOldRecords(@Param("threshold") Instant threshold);

    /**
     * 删除用户的检测记录
     */
    @Modifying
    @Query("DELETE FROM SensitiveWordDetection d WHERE d.userId = :userId")
    int deleteByUserId(@Param("userId") Long userId);

    // ==================== 复合条件查询 ====================

    /**
     * 根据多个条件查询检测记录
     */
    @Query("SELECT d FROM SensitiveWordDetection d WHERE " +
           "(:userId IS NULL OR d.userId = :userId) AND " +
           "(:conversationId IS NULL OR d.conversationId = :conversationId) AND " +
           "(:detectionResult IS NULL OR d.detectionResult = :detectionResult) AND " +
           "(:actionTaken IS NULL OR d.actionTaken = :actionTaken) AND " +
           "(:startTime IS NULL OR d.createdAt >= :startTime) AND " +
           "(:endTime IS NULL OR d.createdAt <= :endTime)")
    Page<SensitiveWordDetection> findByMultipleConditions(
            @Param("userId") Long userId,
            @Param("conversationId") Long conversationId,
            @Param("detectionResult") SensitiveWordDetection.DetectionResult detectionResult,
            @Param("actionTaken") SensitiveWordDetection.ActionTaken actionTaken,
            @Param("startTime") Instant startTime,
            @Param("endTime") Instant endTime,
            Pageable pageable);
}
