package com.implatform.realtime.repository;

import com.implatform.realtime.entity.BotCommand;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 机器人命令Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface BotCommandRepository extends R2dbcRepository<BotCommand, String> {

    /**
     * 根据机器人ID查找命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true ORDER BY command")
    Flux<BotCommand> findByBotIdAndIsActiveTrueOrderByCommand(@Param("botId") Long botId);

    /**
     * 根据机器人ID查找命令（按命令名排序）
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true ORDER BY command ASC")
    Flux<BotCommand> findByBotIdAndIsActiveTrueOrderByCommandAsc(@Param("botId") Long botId);

    /**
     * 统计机器人活跃命令数量
     */
    @Query("SELECT COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = true")
    Mono<Long> countByBotIdAndIsActiveTrue(@Param("botId") Long botId);

    /**
     * 检查命令是否存在
     */
    @Query("SELECT COUNT(*) > 0 FROM bot_commands WHERE bot_id = :botId AND command = :command AND is_active = true")
    Mono<Boolean> existsByBotIdAndCommandAndIsActiveTrue(@Param("botId") Long botId, @Param("command") String command);

    /**
     * 分页查找机器人命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotCommand> findByBotIdOrderByCreatedAtDesc(@Param("botId") Long botId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 根据机器人ID和命令名查找
     */
    Optional<BotCommand> findByBotIdAndCommandAndIsActiveTrue(Long botId, String command);

    /**
     * 根据机器人ID、命令名和作用域查找
     */
    Optional<BotCommand> findByBotIdAndCommandAndScopeAndIsActiveTrue(
            Long botId, String command, BotCommand.CommandScope scope);

    /**
     * 根据机器人ID、命令名、作用域和语言查找
     */
    Optional<BotCommand> findByBotIdAndCommandAndScopeAndLanguageCodeAndIsActiveTrue(
            Long botId, String command, BotCommand.CommandScope scope, String languageCode);

    /**
     * 查找机器人的所有命令（包括非活跃）
     */
    List<BotCommand> findByBotIdOrderByCommandAsc(Long botId);

    /**
     * 根据作用域查找命令
     */
    List<BotCommand> findByBotIdAndScopeAndIsActiveTrueOrderByCommand(
            Long botId, BotCommand.CommandScope scope);

    /**
     * 查找系统命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true AND " +
           "command IN ('start', 'help', 'settings', 'cancel') ORDER BY command")
    Flux<BotCommand> findSystemCommands(@Param("botId") Long botId);

    /**
     * 查找自定义命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true AND " +
           "command NOT IN ('start', 'help', 'settings', 'cancel') ORDER BY command")
    Flux<BotCommand> findCustomCommands(@Param("botId") Long botId);

    /**
     * 搜索命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true AND " +
           "(LOWER(command) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY command")
    Flux<BotCommand> searchCommands(@Param("botId") Long botId, @Param("keyword") String keyword);

    /**
     * 统计机器人的命令数量
     */
    @Query("SELECT COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = true")
    Mono<Long> countActiveCommandsByBotId(@Param("botId") Long botId);

    /**
     * 统计各作用域的命令数量
     */
    @Query("SELECT scope, COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = true GROUP BY scope")
    Flux<Object[]> countCommandsByScope(@Param("botId") Long botId);

    /**
     * 查找最常用的命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true " +
           "ORDER BY usage_count DESC, command ASC LIMIT :limit OFFSET :offset")
    Flux<BotCommand> findMostUsedCommands(@Param("botId") Long botId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找最近创建的命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<BotCommand> findRecentCommands(@Param("botId") Long botId, @Param("limit") int limit, @Param("offset") long offset);

    /**
     * 查找未使用的命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true AND " +
           "(usage_count IS NULL OR usage_count = 0) ORDER BY created_at DESC")
    Flux<BotCommand> findUnusedCommands(@Param("botId") Long botId);

    /**
     * 批量更新命令状态
     */
    @Modifying
    @Query("UPDATE BotCommand bc SET bc.isActive = :isActive WHERE bc.id IN :commandIds")
    int updateCommandStatus(@Param("commandIds") List<String> commandIds, @Param("isActive") Boolean isActive);

    /**
     * 批量增加使用次数
     */
    @Modifying
    @Query("UPDATE BotCommand bc SET bc.usageCount = bc.usageCount + 1 WHERE bc.id = :commandId")
    int incrementUsageCount(@Param("commandId") String commandId);

    /**
     * 检查命令是否存在
     */
    @Query("SELECT COUNT(bc) > 0 FROM BotCommand bc WHERE bc.botId = :botId AND " +
           "LOWER(bc.command) = LOWER(:command) AND bc.scope = :scope AND " +
           "(:languageCode IS NULL OR bc.languageCode = :languageCode) AND bc.id != :excludeId")
    boolean existsByBotIdAndCommandAndScopeAndLanguageCodeAndIdNot(
            @Param("botId") Long botId, @Param("command") String command,
            @Param("scope") BotCommand.CommandScope scope, @Param("languageCode") String languageCode,
            @Param("excludeId") String excludeId);

    /**
     * 检查命令是否存在（新建时）
     */
    @Query("SELECT COUNT(bc) > 0 FROM BotCommand bc WHERE bc.botId = :botId AND " +
           "LOWER(bc.command) = LOWER(:command) AND bc.scope = :scope AND " +
           "(:languageCode IS NULL OR bc.languageCode = :languageCode)")
    boolean existsByBotIdAndCommandAndScopeAndLanguageCode(
            @Param("botId") Long botId, @Param("command") String command,
            @Param("scope") BotCommand.CommandScope scope, @Param("languageCode") String languageCode);

    /**
     * 删除机器人的所有命令
     */
    @Modifying
    @Query("DELETE FROM BotCommand bc WHERE bc.botId = :botId")
    int deleteByBotId(@Param("botId") Long botId);

    /**
     * 获取命令使用统计
     */
    @Query("SELECT " +
           "COUNT(bc) as totalCommands, " +
           "COUNT(CASE WHEN bc.isActive = true THEN 1 END) as activeCommands, " +
           "SUM(bc.usageCount) as totalUsage, " +
           "AVG(bc.usageCount) as averageUsage " +
           "FROM BotCommand bc WHERE bc.botId = :botId")
    Object[] getCommandStatistics(@Param("botId") Long botId);

    /**
     * 查找热门命令
     */
    @Query("SELECT bc.command, SUM(bc.usageCount) as totalUsage FROM BotCommand bc " +
           "WHERE bc.isActive = true AND bc.usageCount > 0 " +
           "GROUP BY bc.command ORDER BY totalUsage DESC")
    List<Object[]> findPopularCommands(Pageable pageable);

    /**
     * 查找机器人的热门命令
     */
    @Query("SELECT bc FROM BotCommand bc WHERE bc.botId = :botId AND bc.isActive = true " +
           "AND bc.usageCount >= :threshold ORDER BY bc.usageCount DESC")
    List<BotCommand> findPopularCommands(@Param("botId") Long botId, @Param("threshold") long threshold,
                                       @Param("isActive") boolean isActive);

    /**
     * 查找命令使用趋势
     */
    @Query("SELECT DATE(bc.updatedAt) as date, SUM(bc.usageCount) as usage FROM BotCommand bc " +
           "WHERE bc.botId = :botId AND bc.updatedAt >= :since " +
           "GROUP BY DATE(bc.updatedAt) ORDER BY date")
    List<Object[]> getCommandUsageTrend(@Param("botId") Long botId, @Param("since") Instant since);

    /**
     * 查找需要清理的命令
     */
    @Query("SELECT * FROM bot_commands WHERE is_active = false AND updated_at < :cutoffTime")
    Flux<BotCommand> findCommandsForCleanup(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 删除旧的非活跃命令
     */
    @Modifying
    @Query("DELETE FROM bot_commands WHERE is_active = false AND updated_at < :cutoffTime")
    Mono<Integer> deleteOldInactiveCommands(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 查找重复的命令
     */
    @Query("SELECT bot_id, command, scope, COUNT(*) as count FROM bot_commands " +
           "WHERE is_active = true GROUP BY bot_id, command, scope HAVING COUNT(*) > 1")
    Flux<Object[]> findDuplicateCommands();

    /**
     * 查找命令冲突
     */
    @Query("SELECT bc FROM BotCommand bc WHERE bc.botId = :botId AND bc.isActive = true AND " +
           "bc.command = :command AND bc.scope IN :scopes")
    List<BotCommand> findCommandConflicts(@Param("botId") Long botId, @Param("command") String command,
                                         @Param("scopes") List<BotCommand.CommandScope> scopes);

    /**
     * 查找所有命令名称
     */
    @Query("SELECT DISTINCT command FROM bot_commands WHERE is_active = true ORDER BY command")
    Flux<String> findAllCommandNames();

    /**
     * 查找机器人的命令名称
     */
    @Query("SELECT command FROM bot_commands WHERE bot_id = :botId AND is_active = true ORDER BY command")
    Flux<String> findCommandNamesByBotId(@Param("botId") Long botId);

    /**
     * 查找支持特定语言的命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = true AND " +
           "(language_code IS NULL OR language_code = :languageCode) ORDER BY command")
    Flux<BotCommand> findCommandsByLanguage(@Param("botId") Long botId, @Param("languageCode") String languageCode);

    /**
     * 查找命令的所有语言版本
     */
    @Query("SELECT bc FROM BotCommand bc WHERE bc.botId = :botId AND bc.command = :command AND bc.scope = :scope " +
           "ORDER BY bc.languageCode NULLS FIRST")
    List<BotCommand> findCommandLanguageVersions(@Param("botId") Long botId, @Param("command") String command,
                                                @Param("scope") BotCommand.CommandScope scope);

    // ============================================================================
    // Missing Methods Required by BotCommandService
    // ============================================================================

    /**
     * 查找公开命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND (is_private_only = false OR is_private_only IS NULL) " +
           "ORDER BY command")
    Flux<BotCommand> findPublicCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive);

    /**
     * 查找管理员命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_admin_only = :isAdminOnly ORDER BY command")
    Flux<BotCommand> findAdminCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                                     @Param("isAdminOnly") boolean isAdminOnly);

    /**
     * 查找群组命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_group_only = :isGroupOnly ORDER BY command")
    Flux<BotCommand> findGroupCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                                     @Param("isGroupOnly") boolean isGroupOnly);

    /**
     * 查找私聊命令
     */
    @Query("SELECT * FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_private_only = :isPrivateOnly ORDER BY command")
    Flux<BotCommand> findPrivateCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                                       @Param("isPrivateOnly") boolean isPrivateOnly);

    /**
     * 统计管理员命令数量
     */
    @Query("SELECT COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_admin_only = :isAdminOnly")
    Mono<Long> countAdminCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                          @Param("isAdminOnly") boolean isAdminOnly);

    /**
     * 统计群组命令数量
     */
    @Query("SELECT COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_group_only = :isGroupOnly")
    Mono<Long> countGroupCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                          @Param("isGroupOnly") boolean isGroupOnly);

    /**
     * 统计私聊命令数量
     */
    @Query("SELECT COUNT(*) FROM bot_commands WHERE bot_id = :botId AND is_active = :isActive " +
           "AND is_private_only = :isPrivateOnly")
    Mono<Long> countPrivateCommands(@Param("botId") Long botId, @Param("isActive") boolean isActive,
                            @Param("isPrivateOnly") boolean isPrivateOnly);

    /**
     * 获取命令使用统计
     */
    @Query("SELECT command, usage_count, created_at, updated_at FROM bot_commands " +
           "WHERE bot_id = :botId AND is_active = true ORDER BY usage_count DESC")
    Flux<Object[]> getCommandUsageStats(@Param("botId") Long botId);

    /**
     * 获取按机器人分组的命令统计
     */
    @Query("SELECT bot_id, COUNT(*), SUM(usage_count) FROM bot_commands " +
           "WHERE is_active = true GROUP BY bot_id ORDER BY COUNT(*) DESC")
    Flux<Object[]> getCommandStatsByBot();

    /**
     * 获取命令使用趋势
     */
    @Query("SELECT DATE(updated_at), SUM(usage_count) FROM bot_commands " +
           "WHERE is_active = true AND updated_at >= :since " +
           "GROUP BY DATE(updated_at) ORDER BY DATE(updated_at)")
    Flux<Object[]> getCommandUsageTrends(@Param("since") Instant since);

    /**
     * 激活命令
     */
    @Modifying
    @Query("UPDATE bot_commands SET is_active = :isActive WHERE id = :commandId")
    Mono<Integer> activateCommand(@Param("commandId") String commandId, @Param("isActive") boolean isActive);

    /**
     * 停用命令
     */
    @Modifying
    @Query("UPDATE bot_commands SET is_active = :isActive WHERE id = :commandId")
    Mono<Integer> deactivateCommand(@Param("commandId") String commandId, @Param("isActive") boolean isActive);
}
