package com.implatform.realtime.repository;

import com.implatform.realtime.entity.PollVote;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 投票记录Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface PollVoteRepository extends R2dbcRepository<PollVote, Long> {

    /**
     * 根据投票ID查找所有投票记录
     */
    List<PollVote> findByPollIdOrderByVoteTimeDesc(Long pollId);

    /**
     * 根据投票ID查找有效投票记录
     */
    @Query("SELECT v FROM PollVote v WHERE v.pollId = :pollId AND v.isRetracted = false ORDER BY v.voteTime DESC")
    List<PollVote> findValidVotesByPollId(@Param("pollId") Long pollId);

    /**
     * 根据用户ID查找投票记录
     */
    Page<PollVote> findByVoterIdOrderByVoteTimeDesc(Long voterId, Pageable pageable);

    /**
     * 查找用户对特定投票的投票记录
     */
    @Query("SELECT v FROM PollVote v WHERE v.pollId = :pollId AND v.voterId = :voterId AND v.isRetracted = false")
    List<PollVote> findUserVotesForPoll(@Param("pollId") Long pollId, @Param("voterId") Long voterId);

    /**
     * 查找用户对特定选项的投票记录
     */
    Optional<PollVote> findByPollIdAndOptionIdAndVoterId(Long pollId, Long optionId, Long voterId);

    /**
     * 检查用户是否已投票
     */
    @Query("SELECT COUNT(v) > 0 FROM PollVote v WHERE v.pollId = :pollId AND v.voterId = :voterId AND v.isRetracted = false")
    boolean hasUserVoted(@Param("pollId") Long pollId, @Param("voterId") Long voterId);

    /**
     * 统计投票的总投票数
     */
    @Query("SELECT COUNT(v) FROM PollVote v WHERE v.pollId = :pollId AND v.isRetracted = false")
    Long countValidVotesByPollId(@Param("pollId") Long pollId);

    /**
     * 统计投票的总投票人数
     */
    @Query("SELECT COUNT(DISTINCT v.voterId) FROM PollVote v WHERE v.pollId = :pollId AND v.isRetracted = false")
    Long countDistinctVotersByPollId(@Param("pollId") Long pollId);

    /**
     * 统计选项的投票数
     */
    @Query("SELECT COUNT(v) FROM PollVote v WHERE v.optionId = :optionId AND v.isRetracted = false")
    Long countValidVotesByOptionId(@Param("optionId") Long optionId);

    /**
     * 查找选项的所有投票记录
     */
    @Query("SELECT v FROM PollVote v WHERE v.optionId = :optionId AND v.isRetracted = false ORDER BY v.voteTime DESC")
    List<PollVote> findValidVotesByOptionId(@Param("optionId") Long optionId);

    /**
     * 查找已撤回的投票记录
     */
    @Query("SELECT v FROM PollVote v WHERE v.pollId = :pollId AND v.isRetracted = true ORDER BY v.retractedAt DESC")
    List<PollVote> findRetractedVotesByPollId(@Param("pollId") Long pollId);

    /**
     * 查找指定时间段内的投票记录
     */
    @Query("SELECT v FROM PollVote v WHERE v.voteTime BETWEEN :startTime AND :endTime ORDER BY v.voteTime DESC")
    List<PollVote> findVotesBetweenTime(@Param("startTime") Instant startTime,
                                       @Param("endTime") Instant endTime);

    /**
     * 查找快速撤回的投票（投票后很快撤回）
     */
    @Query("SELECT v FROM PollVote v WHERE v.isRetracted = true AND v.retractedAt IS NOT NULL " +
           "AND (EXTRACT(EPOCH FROM v.retractedAt) - EXTRACT(EPOCH FROM v.voteTime)) < :maxSeconds")
    List<PollVote> findQuickRetractedVotes(@Param("maxSeconds") Long maxSeconds, Pageable pageable);

    /**
     * 查找活跃投票者
     */
    @Query("SELECT v.voterId, COUNT(v) as voteCount FROM PollVote v WHERE v.voteTime >= :since AND v.isRetracted = false " +
           "GROUP BY v.voterId ORDER BY voteCount DESC")
    List<Object[]> findActiveVoters(@Param("since") Instant since, Pageable pageable);

    /**
     * 查找投票行为统计
     */
    @Query("SELECT " +
           "COUNT(v) as totalVotes, " +
           "COUNT(CASE WHEN v.isRetracted = false THEN 1 END) as validVotes, " +
           "COUNT(CASE WHEN v.isRetracted = true THEN 1 END) as retractedVotes, " +
           "COUNT(DISTINCT v.voterId) as uniqueVoters " +
           "FROM PollVote v WHERE v.voteTime >= :since")
    Object[] getVoteStatistics(@Param("since") Instant since);

    /**
     * 查找用户的投票历史
     */
    @Query("SELECT v FROM PollVote v WHERE v.voterId = :userId ORDER BY v.voteTime DESC")
    Page<PollVote> findUserVoteHistory(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找用户最近的投票
     */
    @Query("SELECT v FROM PollVote v WHERE v.voterId = :userId AND v.voteTime >= :since " +
           "ORDER BY v.voteTime DESC")
    List<PollVote> findUserRecentVotes(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 撤回用户对投票的所有投票
     */
    @Modifying
    @Query("UPDATE PollVote v SET v.isRetracted = true, v.retractedAt = CURRENT_TIMESTAMP " +
           "WHERE v.pollId = :pollId AND v.voterId = :voterId AND v.isRetracted = false")
    int retractUserVotesForPoll(@Param("pollId") Long pollId, @Param("voterId") Long voterId);

    /**
     * 删除旧的投票记录
     */
    @Modifying
    @Query("DELETE FROM PollVote v WHERE v.voteTime < :cutoffTime")
    int deleteOldVotes(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 查找来自特定IP的投票
     */
    List<PollVote> findByIpAddressOrderByVoteTimeDesc(String ipAddress);

    /**
     * 查找来自特定客户端的投票
     */
    @Query("SELECT v FROM PollVote v WHERE v.clientInfo LIKE %:clientInfo% ORDER BY v.voteTime DESC")
    List<PollVote> findByClientInfo(@Param("clientInfo") String clientInfo, Pageable pageable);

    /**
     * 统计投票时间分布
     */
    @Query("SELECT DATE(v.voteTime) as voteDate, COUNT(v) as voteCount " +
           "FROM PollVote v WHERE v.voteTime >= :since AND v.isRetracted = false " +
           "GROUP BY DATE(v.voteTime) ORDER BY voteDate DESC")
    List<Object[]> getVoteTimeDistribution(@Param("since") Instant since);

    /**
     * 查找投票模式分析
     */
    @Query("SELECT v.voterId, COUNT(DISTINCT v.pollId) as pollsVoted, COUNT(v) as totalVotes " +
           "FROM PollVote v WHERE v.voteTime >= :since AND v.isRetracted = false " +
           "GROUP BY v.voterId HAVING COUNT(DISTINCT v.pollId) >= :minPolls " +
           "ORDER BY pollsVoted DESC")
    List<Object[]> findVotingPatterns(@Param("since") Instant since,
                                     @Param("minPolls") Integer minPolls,
                                     Pageable pageable);
}
