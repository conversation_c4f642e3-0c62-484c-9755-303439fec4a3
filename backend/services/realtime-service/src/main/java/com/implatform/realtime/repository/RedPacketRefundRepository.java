package com.implatform.realtime.repository;

import com.implatform.realtime.entity.RedPacketRefund;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 红包退款记录数据访问接口 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface RedPacketRefundRepository extends R2dbcRepository<RedPacketRefund, Long> {

    /**
     * 根据红包ID查找退款记录
     */
    List<RedPacketRefund> findByRedPacketIdOrderByRefundTimeDesc(Long redPacketId);

    /**
     * 根据红包ID和状态查找退款记录
     */
    List<RedPacketRefund> findByRedPacketIdAndStatusOrderByRefundTimeDesc(Long redPacketId, RedPacketRefund.RefundStatus status);

    /**
     * 根据状态查找退款记录
     */
    Page<RedPacketRefund> findByStatusOrderByRefundTimeDesc(RedPacketRefund.RefundStatus status, Pageable pageable);

    /**
     * 根据时间范围查找退款记录
     */
    @Query("SELECT rpr FROM RedPacketRefund rpr WHERE rpr.refundTime BETWEEN :startTime AND :endTime " +
           "ORDER BY rpr.refundTime DESC")
    List<RedPacketRefund> findByRefundTimeBetween(@Param("startTime") Instant startTime, 
                                                 @Param("endTime") Instant endTime);

    /**
     * 统计红包的总退款金额
     */
    @Query("SELECT COALESCE(SUM(rpr.refundAmount), 0) FROM RedPacketRefund rpr " +
           "WHERE rpr.redPacket.id = :redPacketId AND rpr.status = 'SUCCESS'")
    BigDecimal sumRefundAmountByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 统计指定时间范围内的退款统计
     */
    @Query("SELECT new map(" +
           "COUNT(rpr) as totalRefunds, " +
           "SUM(rpr.refundAmount) as totalRefundAmount, " +
           "AVG(rpr.refundAmount) as avgRefundAmount" +
           ") FROM RedPacketRefund rpr WHERE rpr.refundTime BETWEEN :startTime AND :endTime " +
           "AND rpr.status = 'SUCCESS'")
    List<Object> getRefundStatistics(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 查找失败的退款记录
     */
    @Query("SELECT rpr FROM RedPacketRefund rpr WHERE rpr.status = 'FAILED' " +
           "ORDER BY rpr.refundTime DESC")
    List<RedPacketRefund> findFailedRefunds(Pageable pageable);

    /**
     * 查找处理中的退款记录
     */
    @Query("SELECT rpr FROM RedPacketRefund rpr WHERE rpr.status = 'PROCESSING' " +
           "ORDER BY rpr.refundTime ASC")
    List<RedPacketRefund> findProcessingRefunds();

    /**
     * 统计退款成功率
     */
    @Query("SELECT new map(" +
           "COUNT(CASE WHEN rpr.status = 'SUCCESS' THEN 1 END) as successCount, " +
           "COUNT(rpr) as totalCount" +
           ") FROM RedPacketRefund rpr WHERE rpr.refundTime BETWEEN :startTime AND :endTime")
    List<Object> getRefundSuccessRate(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 根据退款原因统计
     */
    @Query("SELECT rpr.refundReason, COUNT(rpr), SUM(rpr.refundAmount) " +
           "FROM RedPacketRefund rpr WHERE rpr.refundTime BETWEEN :startTime AND :endTime " +
           "GROUP BY rpr.refundReason ORDER BY COUNT(rpr) DESC")
    List<Object[]> getRefundReasonStatistics(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 检查红包是否有退款记录
     */
    @Query("SELECT COUNT(rpr) > 0 FROM RedPacketRefund rpr WHERE rpr.redPacket.id = :redPacketId")
    boolean existsByRedPacketId(@Param("redPacketId") Long redPacketId);

    /**
     * 查找最近的退款记录
     */
    @Query("SELECT rpr FROM RedPacketRefund rpr ORDER BY rpr.refundTime DESC")
    List<RedPacketRefund> findRecentRefunds(Pageable pageable);
}
