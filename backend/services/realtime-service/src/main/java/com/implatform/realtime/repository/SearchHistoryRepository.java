package com.implatform.realtime.repository;

import com.implatform.realtime.entity.SearchHistory;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 搜索历史Repository - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 2.0.0
 */
@Repository
public interface SearchHistoryRepository extends R2dbcRepository<SearchHistory, Long> {

    /**
     * 获取用户搜索历史
     */
    Page<SearchHistory> findByUserIdOrderBySearchTimeDesc(Long userId, Pageable pageable);



    /**
     * 根据查询内容模糊搜索历史记录
     */
    List<SearchHistory> findByQueryContainingIgnoreCase(String query, Pageable pageable);

    /**
     * 获取用户指定类型的搜索历史
     */
    Page<SearchHistory> findByUserIdAndSearchTypeOrderBySearchTimeDesc(Long userId, 
                                                                       SearchHistory.SearchType searchType, 
                                                                       Pageable pageable);

    /**
     * 获取用户最近的搜索历史
     */
    @Query("SELECT h FROM SearchHistory h WHERE h.userId = :userId AND h.searchTime >= :since " +
           "ORDER BY h.searchTime DESC")
    List<SearchHistory> findRecentSearchHistory(@Param("userId") Long userId,
                                               @Param("since") Instant since);

    /**
     * 获取用户热门搜索词
     */
    @Query("SELECT h.query, COUNT(h) as searchCount FROM SearchHistory h " +
           "WHERE h.userId = :userId AND h.searchTime >= :since " +
           "GROUP BY h.query " +
           "ORDER BY searchCount DESC")
    List<Object[]> findUserPopularQueries(@Param("userId") Long userId,
                                         @Param("since") Instant since,
                                         Pageable pageable);

    /**
     * 获取全局热门搜索词
     */
    @Query("SELECT h.query, COUNT(h) as searchCount FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY h.query " +
           "ORDER BY searchCount DESC")
    List<Object[]> findGlobalPopularQueries(@Param("since") Instant since, Pageable pageable);

    /**
     * 获取搜索成功率统计
     */
    @Query("SELECT h.searchType, " +
           "COUNT(h) as totalSearches, " +
           "COUNT(CASE WHEN h.resultCount > 0 THEN 1 END) as successfulSearches, " +
           "AVG(h.resultCount) as avgResultCount " +
           "FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY h.searchType")
    List<Object[]> getSearchSuccessRateStats(@Param("since") Instant since);

    /**
     * 获取用户搜索统计
     */
    @Query("SELECT " +
           "COUNT(h) as totalSearches, " +
           "COUNT(DISTINCT h.query) as uniqueQueries, " +
           "COUNT(CASE WHEN h.resultCount > 0 THEN 1 END) as successfulSearches, " +
           "AVG(h.resultCount) as avgResultCount " +
           "FROM SearchHistory h " +
           "WHERE h.userId = :userId AND h.searchTime >= :since")
    Object[] getUserSearchStats(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 获取搜索趋势数据
     */
    @Query("SELECT DATE(h.searchTime) as searchDate, COUNT(h) as searchCount " +
           "FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY DATE(h.searchTime) " +
           "ORDER BY searchDate")
    List<Object[]> getSearchTrends(@Param("since") Instant since);

    /**
     * 获取点击率统计
     */
    @Query("SELECT h.searchType, " +
           "COUNT(h) as totalSearches, " +
           "COUNT(CASE WHEN h.clickedEntityId IS NOT NULL THEN 1 END) as clickedSearches " +
           "FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY h.searchType")
    List<Object[]> getClickRateStats(@Param("since") Instant since);

    /**
     * 获取最受欢迎的搜索结果
     */
    @Query("SELECT h.clickedEntityType, h.clickedEntityId, COUNT(h) as clickCount " +
           "FROM SearchHistory h " +
           "WHERE h.clickedEntityId IS NOT NULL AND h.searchTime >= :since " +
           "GROUP BY h.clickedEntityType, h.clickedEntityId " +
           "ORDER BY clickCount DESC")
    List<Object[]> getMostClickedResults(@Param("since") Instant since, Pageable pageable);

    /**
     * 获取无结果搜索
     */
    @Query("SELECT h.query, COUNT(h) as searchCount FROM SearchHistory h " +
           "WHERE h.resultCount = 0 AND h.searchTime >= :since " +
           "GROUP BY h.query " +
           "ORDER BY searchCount DESC")
    List<Object[]> getNoResultQueries(@Param("since") Instant since, Pageable pageable);

    /**
     * 删除用户搜索历史
     */
    void deleteByUserId(Long userId);

    /**
     * 删除指定时间之前的搜索历史
     */
    void deleteBySearchTimeBefore(Instant cutoffTime);

    /**
     * 获取搜索活跃用户
     */
    @Query("SELECT h.userId, COUNT(h) as searchCount FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY h.userId " +
           "ORDER BY searchCount DESC")
    List<Object[]> getActiveSearchUsers(@Param("since") Instant since, Pageable pageable);

    /**
     * 获取搜索高峰时段
     */
    @Query("SELECT EXTRACT(HOUR FROM h.searchTime) as hour, COUNT(h) as searchCount " +
           "FROM SearchHistory h " +
           "WHERE h.searchTime >= :since " +
           "GROUP BY EXTRACT(HOUR FROM h.searchTime) " +
           "ORDER BY hour")
    List<Object[]> getSearchPeakHours(@Param("since") Instant since);

    /**
     * 检查用户是否搜索过某个查询
     */
    boolean existsByUserIdAndQuery(Long userId, String query);

    /**
     * 获取用户最后搜索时间
     */
    @Query("SELECT MAX(h.searchTime) FROM SearchHistory h WHERE h.userId = :userId")
    Instant findLastSearchTime(@Param("userId") Long userId);

    /**
     * 统计指定时间范围内的搜索次数
     */
    @Query("SELECT COUNT(h) FROM SearchHistory h " +
           "WHERE h.searchTime BETWEEN :startTime AND :endTime")
    Long countSearchesBetween(@Param("startTime") Instant startTime,
                             @Param("endTime") Instant endTime);

    /**
     * 获取搜索转化率（有点击的搜索比例）
     */
    @Query("SELECT " +
           "COUNT(h) as totalSearches, " +
           "COUNT(CASE WHEN h.clickedEntityId IS NOT NULL THEN 1 END) as convertedSearches " +
           "FROM SearchHistory h " +
           "WHERE h.userId = :userId AND h.searchTime >= :since")
    Object[] getUserConversionRate(@Param("userId") Long userId, @Param("since") Instant since);

    /**
     * 统计指定时间范围内的搜索次数
     */
    @Query("SELECT COUNT(h) FROM SearchHistory h " +
           "WHERE h.searchTime BETWEEN :startTime AND :endTime")
    Long countBySearchTimeBetween(@Param("startTime") Instant startTime,
                                 @Param("endTime") Instant endTime);

    /**
     * 统计指定时间范围内的唯一用户数
     */
    @Query("SELECT COUNT(DISTINCT h.userId) FROM SearchHistory h " +
           "WHERE h.searchTime BETWEEN :startTime AND :endTime")
    Long countDistinctUsersBySearchTimeBetween(@Param("startTime") Instant startTime,
                                              @Param("endTime") Instant endTime);
}
