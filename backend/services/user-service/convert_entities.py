#!/usr/bin/env python3
"""
批量转换实体类从 JPA 到 R2DBC
"""

import os
import re
import glob

def convert_entity_file(file_path):
    """转换单个实体类文件"""
    print(f"转换文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 执行转换
    content = convert_imports(content)
    content = convert_annotations(content)
    content = convert_column_annotations(content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"完成转换: {file_path}")

def convert_imports(content):
    """转换导入语句"""
    # 移除 JPA 相关导入
    jpa_imports = [
        r'import jakarta\.persistence\.\*;',
        r'import jakarta\.persistence\..*;',
        r'import org\.hibernate\.annotations\.\*;',
        r'import org\.hibernate\.annotations\..*;',
        r'import org\.springframework\.data\.jpa\.domain\.support\.AuditingEntityListener;'
    ]
    
    for pattern in jpa_imports:
        content = re.sub(pattern, '', content)
    
    # 添加 R2DBC 导入（在 package 声明后）
    r2dbc_imports = '''
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;'''
    
    # 在第一个 import 前插入 R2DBC 导入
    if 'import' in content:
        content = re.sub(r'(\npackage [^;]+;)', r'\1' + r2dbc_imports, content)
    
    return content

def convert_annotations(content):
    """转换类级别注解"""
    # 移除 @Entity
    content = re.sub(r'@Entity\s*\n', '', content)
    
    # 转换 @Table 注解
    content = re.sub(r'@Table\(name = "([^"]+)"[^)]*\)', r'@Table("\1")', content)
    
    # 移除 @EntityListeners
    content = re.sub(r'@EntityListeners\([^)]+\)\s*\n', '', content)
    
    # 移除 @GeneratedValue
    content = re.sub(r'@GeneratedValue\([^)]+\)\s*\n', '', content)
    
    # 移除 @Enumerated
    content = re.sub(r'@Enumerated\([^)]+\)\s*\n', '', content)
    
    return content

def convert_column_annotations(content):
    """转换字段级别注解"""
    # 转换 @Column 注解 - 保留 name 参数，移除其他参数
    content = re.sub(r'@Column\(name = "([^"]+)"[^)]*\)', r'@Column("\1")', content)
    
    # 简化其他 @Column 注解
    content = re.sub(r'@Column\([^)]*nullable = false[^)]*\)', '@Column', content)
    content = re.sub(r'@Column\([^)]*length = \d+[^)]*\)', '@Column', content)
    content = re.sub(r'@Column\([^)]*unique = true[^)]*\)', '@Column', content)
    content = re.sub(r'@Column\([^)]*updatable = false[^)]*\)', '@Column', content)
    content = re.sub(r'@Column\([^)]*columnDefinition = "[^"]*"[^)]*\)', '@Column', content)
    
    # 转换时间注解
    content = re.sub(r'@CreationTimestamp', '@CreatedDate', content)
    content = re.sub(r'@UpdateTimestamp', '@LastModifiedDate', content)
    
    # 清理多余的空行
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    
    return content

def main():
    """主函数"""
    entity_dir = "src/main/java/com/implatform/user/entity"
    
    if not os.path.exists(entity_dir):
        print(f"目录不存在: {entity_dir}")
        return
    
    # 获取所有 Java 文件
    java_files = glob.glob(os.path.join(entity_dir, "*.java"))
    
    # 排除已经转换的文件
    excluded_files = ['User.java', 'UserDevice.java']
    java_files = [f for f in java_files if os.path.basename(f) not in excluded_files]
    
    print(f"找到 {len(java_files)} 个实体类文件需要转换")
    
    for file_path in java_files:
        try:
            convert_entity_file(file_path)
        except Exception as e:
            print(f"转换失败 {file_path}: {e}")
    
    print("批量转换完成！")
    print("请检查转换结果并手动调整复杂的注解")

if __name__ == "__main__":
    main()
