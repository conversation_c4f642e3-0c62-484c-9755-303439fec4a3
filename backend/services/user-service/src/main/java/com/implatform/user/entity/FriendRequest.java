package com.implatform.user.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 好友请求实体类 - IM平台好友添加流程管理实体
 *
 * <p><strong>业务用途</strong>：
 * 管理用户之间的好友添加请求流程，包括请求发送、接收、处理等完整的好友申请生命周期。
 * 支持请求状态跟踪、消息传递、过期处理等功能，确保好友添加过程的规范性和可追溯性。
 *
 * <p><strong>使用场景</strong>：
 * <ul>
 *   <li>用户发送好友添加请求（搜索用户名、扫码、群组添加等）</li>
 *   <li>接收方查看和处理好友请求（接受、拒绝、忽略）</li>
 *   <li>好友请求列表展示和管理</li>
 *   <li>请求状态跟踪和通知推送</li>
 *   <li>过期请求自动清理</li>
 *   <li>请求历史记录和统计分析</li>
 * </ul>
 *
 * <p><strong>业务上下文</strong>：
 * 作为好友关系建立的前置流程，FriendRequest连接了用户发现、社交网络扩展、隐私保护等功能。
 * 通过规范化的请求流程，保护用户隐私的同时促进社交连接的建立。
 *
 * <p><strong>关键关系</strong>：
 * <ul>
 *   <li>多对一关系：FriendRequest → User（发送方）</li>
 *   <li>多对一关系：FriendRequest → User（接收方）</li>
 *   <li>一对一关系：FriendRequest → FriendRelation（成功后创建好友关系）</li>
 * </ul>
 *
 * <p><strong>状态流转</strong>：
 * <pre>
 * PENDING（待处理） → ACCEPTED（已接受）→ 创建FriendRelation
 *                  → REJECTED（已拒绝）→ 记录拒绝原因
 *                  → EXPIRED（已过期） → 自动清理
 * </pre>
 *
 * <p><strong>实际应用示例</strong>：
 * <pre>
 * // 发送好友请求
 * FriendRequest request = new FriendRequest();
 * request.setFromUserId(currentUserId);
 * request.setToUserId(targetUserId);
 * request.setMessage("我是通过搜索找到你的，希望能成为朋友");
 * request.setStatus(RequestStatus.PENDING);
 *
 * // 处理好友请求
 * if (accepted) {
 *     request.setStatus(RequestStatus.ACCEPTED);
 *     request.setProcessedAt(LocalDateTime.now());
 *     // 创建好友关系
 * } else {
 *     request.setStatus(RequestStatus.REJECTED);
 *     request.setRejectReason("暂时不添加陌生人");
 * }
 * </pre>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 friend_requests 表，包含发送方、接收方、状态、创建时间等字段的索引，
 * 支持高效的请求查询、状态过滤和过期清理操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("friend_requests"),
        @Index(name = "idx_request_to", columnList = "toUserId"),
        @Index(name = "idx_request_status", columnList = "status"),
        @Index(name = "idx_request_created", columnList = "createdAt")
    }
)
public class FriendRequest {
    
    @Id
        private Long id;
    
    /**
     * 发送请求的用户ID
     */
    @Column
    private Long fromUserId;
    
    /**
     * 接收请求的用户ID
     */
    @Column
    private Long toUserId;
    
    /**
     * 请求状态
     */
        @Column
    private RequestStatus status = RequestStatus.PENDING;
    
    /**
     * 请求消息
     */
    @Column
    private String message;
    
    /**
     * 拒绝原因
     */
    @Column
    private String rejectReason;
    
    /**
     * 处理时间
     */
    private LocalDateTime processedAt;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column
    private LocalDateTime updatedAt;
    
    /**
     * 请求状态枚举
     */
    public enum RequestStatus {
        PENDING("待处理"),
        ACCEPTED("已接受"),
        REJECTED("已拒绝"),
        EXPIRED("已过期");
        
        private final String description;
        
        RequestStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 