package com.implatform.user.repository;

import com.implatform.user.entity.AuthToken;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 认证令牌数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface AuthTokenRepository extends ReactiveCrudRepository<AuthToken, Long> {

    /**
     * 根据访问令牌哈希和状态查找令牌
     */
    Mono<AuthToken> findByAccessTokenHashAndStatus(String accessTokenHash, AuthToken.TokenStatus status);

    /**
     * 根据访问令牌哈希、设备ID和状态查找令牌
     */
    Mono<AuthToken> findByAccessTokenHashAndDeviceIdAndStatus(
            String accessTokenHash, String deviceId, AuthToken.TokenStatus status);

    /**
     * 根据刷新令牌哈希和状态查找令牌
     */
    Mono<AuthToken> findByRefreshTokenHashAndStatus(String refreshTokenHash, AuthToken.TokenStatus status);

    /**
     * 根据刷新令牌哈希、设备ID和状态查找令牌
     */
    Mono<AuthToken> findByRefreshTokenHashAndDeviceIdAndStatus(
            String refreshTokenHash, String deviceId, AuthToken.TokenStatus status);

    /**
     * 根据用户ID和状态查找令牌
     */
    Flux<AuthToken> findByUserIdAndStatus(Long userId, AuthToken.TokenStatus status);

    /**
     * 根据用户ID和设备ID查找令牌
     */
    Flux<AuthToken> findByUserIdAndDeviceId(Long userId, String deviceId);

    /**
     * 根据用户ID、设备ID和状态查找令牌
     */
    Mono<AuthToken> findByUserIdAndDeviceIdAndStatus(Long userId, String deviceId, AuthToken.TokenStatus status);

    /**
     * 根据设备ID查找活跃令牌
     */
    Flux<AuthToken> findByDeviceIdAndStatus(String deviceId, AuthToken.TokenStatus status);

    /**
     * 查找过期的令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE expires_at < :now AND status = :status")
    Flux<AuthToken> findExpiredTokens(Instant now, AuthToken.TokenStatus status);

    /**
     * 查找需要清理的刷新令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE refresh_expires_at < :now AND status = :status")
    Flux<AuthToken> findExpiredRefreshTokens(Instant now, AuthToken.TokenStatus status);

    /**
     * 根据用户ID分页查询令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AuthToken> findByUserIdOrderByCreatedAtDesc(Long userId, int limit, long offset);

    /**
     * 根据用户ID和状态分页查询令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AuthToken> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, AuthToken.TokenStatus status, int limit, long offset);

    /**
     * 统计用户活跃令牌数量
     */
    @Query("SELECT COUNT(*) FROM auth_tokens WHERE user_id = :userId AND status = :status")
    Mono<Long> countByUserIdAndStatus(Long userId, AuthToken.TokenStatus status);

    /**
     * 统计用户在指定设备上的活跃令牌数量
     */
    @Query("SELECT COUNT(*) FROM auth_tokens WHERE user_id = :userId AND device_id = :deviceId AND status = :status")
    Mono<Long> countByUserIdAndDeviceIdAndStatus(Long userId, String deviceId, AuthToken.TokenStatus status);

    /**
     * 查找用户最近的登录令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AuthToken> findRecentTokensByUserId(Long userId, AuthToken.TokenStatus status, int limit, long offset);

    /**
     * 查找指定时间范围内的令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE created_at BETWEEN :startTime AND :endTime")
    Flux<AuthToken> findTokensInTimeRange(Instant startTime, Instant endTime);

    /**
     * 根据客户端IP查找令牌
     */
    Flux<AuthToken> findByClientIpAndStatusOrderByCreatedAtDesc(String clientIp, AuthToken.TokenStatus status);

    /**
     * 查找可疑的令牌（同一用户多个设备同时活跃）
     */
    @Query("SELECT * FROM auth_tokens WHERE user_id IN " +
           "(SELECT user_id FROM auth_tokens WHERE status = :status " +
           "GROUP BY user_id HAVING COUNT(DISTINCT device_id) > :maxDevices)")
    Flux<AuthToken> findSuspiciousTokens(AuthToken.TokenStatus status, int maxDevices);

    /**
     * 批量更新令牌状态
     */
    @Modifying
    @Query("UPDATE auth_tokens SET status = :newStatus, updated_at = :now WHERE id = ANY(:tokenIds)")
    Mono<Integer> batchUpdateTokenStatus(Long[] tokenIds, AuthToken.TokenStatus newStatus, Instant now);

    /**
     * 批量撤销用户令牌
     */
    @Modifying
    @Query("UPDATE auth_tokens SET status = :revokedStatus, updated_at = :now " +
           "WHERE user_id = :userId AND status = :activeStatus")
    Mono<Integer> revokeUserTokens(Long userId, AuthToken.TokenStatus activeStatus,
                                 AuthToken.TokenStatus revokedStatus, Instant now);

    /**
     * 批量撤销设备令牌
     */
    @Modifying
    @Query("UPDATE auth_tokens SET status = :revokedStatus, updated_at = :now " +
           "WHERE device_id = :deviceId AND status = :activeStatus")
    Mono<Integer> revokeDeviceTokens(String deviceId, AuthToken.TokenStatus activeStatus,
                                   AuthToken.TokenStatus revokedStatus, Instant now);

    /**
     * 查找创建时间早于指定时间的令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE created_at < :instant")
    Flux<AuthToken> findTokensCreatedBefore(Instant instant);

    /**
     * 清理过期令牌
     */
    @Modifying
    @Query("DELETE FROM auth_tokens WHERE refresh_expires_at < :expiredBefore")
    Mono<Integer> deleteExpiredTokens(Instant expiredBefore);

    /**
     * 更新令牌最后使用时间
     */
    @Modifying
    @Query("UPDATE auth_tokens SET last_used_at = :now, updated_at = :now WHERE id = :tokenId")
    Mono<Integer> updateLastUsedTime(Long tokenId, Instant now);

    /**
     * 获取令牌统计信息
     */
    @Query("SELECT status, COUNT(*) FROM auth_tokens GROUP BY status")
    Flux<Object[]> getTokenStatistics();

    /**
     * 获取用户令牌统计信息
     */
    @Query("SELECT status, COUNT(*) FROM auth_tokens WHERE user_id = :userId GROUP BY status")
    Flux<Object[]> getUserTokenStatistics(Long userId);

    /**
     * 查找长时间未使用的令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE last_used_at < :threshold AND status = :status")
    Flux<AuthToken> findInactiveTokens(Instant threshold, AuthToken.TokenStatus status);

    /**
     * 根据地理位置查找令牌
     */
    Flux<AuthToken> findByLocationContainingAndStatusOrderByCreatedAtDesc(String location, AuthToken.TokenStatus status);

    /**
     * 查找记住登录的令牌
     */
    Flux<AuthToken> findByRememberMeAndStatusOrderByCreatedAtDesc(Boolean rememberMe, AuthToken.TokenStatus status);

    /**
     * 检查令牌是否存在
     */
    Mono<Boolean> existsByAccessTokenHashAndStatus(String accessTokenHash, AuthToken.TokenStatus status);

    /**
     * 检查刷新令牌是否存在
     */
    Mono<Boolean> existsByRefreshTokenHashAndStatus(String refreshTokenHash, AuthToken.TokenStatus status);

    // ==================== 强制下线相关方法 ====================

    /**
     * 根据用户ID查找除指定设备外的其他设备令牌
     */
    @Query("SELECT * FROM auth_tokens WHERE user_id = :userId AND device_id != :deviceId AND status = 'ACTIVE'")
    Flux<AuthToken> findByUserIdAndDeviceIdNot(Long userId, String deviceId);

    /**
     * 统计用户指定状态的不同设备数量
     */
    @Query("SELECT COUNT(DISTINCT device_id) FROM auth_tokens WHERE user_id = :userId AND status = :status")
    Mono<Integer> countDistinctDevicesByUserIdAndStatus(Long userId, AuthToken.TokenStatus status);
}
