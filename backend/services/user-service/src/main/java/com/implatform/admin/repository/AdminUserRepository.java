package com.implatform.admin.repository;

import java.time.LocalDateTime;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.admin.entity.AdminUser;
import com.implatform.admin.entity.AdminUser.AdminStatus;
import com.implatform.admin.entity.AdminUser.AdminType;

/**
 * 管理员用户数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 */
@Repository
public interface AdminUserRepository extends ReactiveCrudRepository<AdminUser, String> {

    /**
     * 根据用户名查找管理员用户
     */
    Mono<AdminUser> findByUsername(String username);

    /**
     * 根据邮箱查找管理员用户
     */
    Mono<AdminUser> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE username = :username OR email = :email")
    Mono<AdminUser> findByUsernameOrEmail(String username, String email);

    /**
     * 检查用户名是否存在
     */
    Mono<Boolean> existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    Mono<Boolean> existsByEmail(String email);

    /**
     * 检查用户名是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(*) > 0 FROM admin_users WHERE username = :username AND id != :id")
    Mono<Boolean> existsByUsernameAndIdNot(String username, String id);

    /**
     * 检查邮箱是否存在（排除指定ID）
     */
    @Query("SELECT COUNT(*) > 0 FROM admin_users WHERE email = :email AND id != :id")
    Mono<Boolean> existsByEmailAndIdNot(String email, String id);

    /**
     * 根据状态查找管理员用户
     */
    Flux<AdminUser> findByStatus(AdminStatus status);

    /**
     * 根据管理员类型查找用户
     */
    Flux<AdminUser> findByAdminType(AdminType adminType);

    /**
     * 根据状态分页查询
     */
    @Query("SELECT * FROM admin_users WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AdminUser> findByStatusOrderByCreatedAtDesc(AdminStatus status, int limit, long offset);

    /**
     * 根据管理员类型分页查询
     */
    @Query("SELECT * FROM admin_users WHERE admin_type = :adminType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AdminUser> findByAdminTypeOrderByCreatedAtDesc(AdminType adminType, int limit, long offset);

    /**
     * 根据部门查找管理员用户
     */
    Flux<AdminUser> findByDepartment(String department);

    /**
     * 搜索管理员用户（用户名、邮箱、真实姓名、昵称）
     */
    @Query("SELECT * FROM admin_users WHERE " +
           "username LIKE CONCAT('%', :keyword, '%') OR " +
           "email LIKE CONCAT('%', :keyword, '%') OR " +
           "real_name LIKE CONCAT('%', :keyword, '%') OR " +
           "nickname LIKE CONCAT('%', :keyword, '%') " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AdminUser> searchAdminUsers(String keyword, int limit, long offset);

    /**
     * 根据多个条件搜索
     */
    @Query("SELECT * FROM admin_users WHERE " +
           "(:username IS NULL OR username LIKE CONCAT('%', :username, '%')) AND " +
           "(:email IS NULL OR email LIKE CONCAT('%', :email, '%')) AND " +
           "(:realName IS NULL OR real_name LIKE CONCAT('%', :realName, '%')) AND " +
           "(:department IS NULL OR department LIKE CONCAT('%', :department, '%')) AND " +
           "(:status IS NULL OR status = :status) AND " +
           "(:adminType IS NULL OR admin_type = :adminType) " +
           "ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<AdminUser> findByConditions(String username, String email, String realName,
                                   String department, AdminStatus status, AdminType adminType,
                                   int limit, long offset);

    /**
     * 查找拥有指定角色的管理员用户
     */
    @Query("SELECT DISTINCT u.* FROM admin_users u " +
           "JOIN admin_user_roles ur ON u.id = ur.admin_user_id " +
           "JOIN roles r ON ur.role_id = r.id " +
           "WHERE r.code = :roleCode")
    Flux<AdminUser> findByRoleCode(String roleCode);

    /**
     * 查找拥有指定权限的管理员用户
     */
    @Query("SELECT DISTINCT u.* FROM admin_users u " +
           "JOIN admin_user_roles ur ON u.id = ur.admin_user_id " +
           "JOIN roles r ON ur.role_id = r.id " +
           "JOIN role_permissions rp ON r.id = rp.role_id " +
           "JOIN permissions p ON rp.permission_id = p.id " +
           "WHERE p.code = :permissionCode")
    Flux<AdminUser> findByPermissionCode(String permissionCode);

    /**
     * 查找超级管理员
     */
    Flux<AdminUser> findByIsSuperAdminTrue();

    /**
     * 查找被锁定的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE status = 'LOCKED' OR " +
           "(locked_until IS NOT NULL AND locked_until > :now)")
    Flux<AdminUser> findLockedUsers(LocalDateTime now);

    /**
     * 查找密码即将过期的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE password_expires_at IS NOT NULL AND " +
           "password_expires_at BETWEEN :now AND :futureTime")
    Flux<AdminUser> findUsersWithExpiringPasswords(LocalDateTime now, LocalDateTime futureTime);

    /**
     * 查找未验证邮箱的管理员用户
     */
    Flux<AdminUser> findByEmailVerifiedFalse();

    /**
     * 统计各状态的管理员用户数量
     */
    @Query("SELECT status, COUNT(*) FROM admin_users GROUP BY status")
    Flux<Object[]> countByStatusGrouped();

    /**
     * 统计各管理员类型的用户数量
     */
    @Query("SELECT admin_type, COUNT(*) FROM admin_users GROUP BY admin_type")
    Flux<Object[]> countByAdminTypeGrouped();

    /**
     * 统计各部门的管理员用户数量
     */
    @Query("SELECT department, COUNT(*) FROM admin_users " +
           "WHERE department IS NOT NULL GROUP BY department")
    Flux<Object[]> countByDepartmentGrouped();

    /**
     * 查找最近登录的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE last_login_time IS NOT NULL " +
           "ORDER BY last_login_time DESC LIMIT :limit OFFSET :offset")
    Flux<AdminUser> findRecentlyLoggedInUsers(int limit, long offset);

    /**
     * 查找指定时间段内登录的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE last_login_time BETWEEN :startTime AND :endTime")
    Flux<AdminUser> findUsersLoggedInBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找长时间未登录的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE last_login_time IS NULL OR " +
           "last_login_time < :cutoffTime")
    Flux<AdminUser> findInactiveUsers(LocalDateTime cutoffTime);

    /**
     * 查找指定创建者创建的管理员用户
     */
    Flux<AdminUser> findByCreatedBy(String createdBy);

    /**
     * 批量查询管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE id = ANY(:ids)")
    Flux<AdminUser> findByIds(String[] ids);

    /**
     * 查找在线管理员用户（最近15分钟内登录）
     */
    @Query("SELECT * FROM admin_users WHERE last_login_time > :cutoffTime")
    Flux<AdminUser> findOnlineUsers(LocalDateTime cutoffTime);

    /**
     * 统计总的管理员用户数量
     */
    @Query("SELECT COUNT(*) FROM admin_users")
    Mono<Long> countTotal();

    /**
     * 统计活跃管理员用户数量
     */
    Mono<Long> countByStatus(AdminStatus status);

    /**
     * 统计今日新增管理员用户数量
     */
    @Query("SELECT COUNT(*) FROM admin_users WHERE DATE(created_at) = CURRENT_DATE")
    Mono<Long> countTodayRegistrations();

    /**
     * 统计本月新增管理员用户数量
     */
    @Query("SELECT COUNT(*) FROM admin_users WHERE EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM CURRENT_DATE) " +
           "AND EXTRACT(MONTH FROM created_at) = EXTRACT(MONTH FROM CURRENT_DATE)")
    Mono<Long> countMonthlyRegistrations();

    /**
     * 查找需要密码重置的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE password_expires_at < :now")
    Flux<AdminUser> findUsersNeedingPasswordReset(LocalDateTime now);

    /**
     * 根据角色ID查找管理员用户
     */
    @Query("SELECT DISTINCT u.* FROM admin_users u " +
           "JOIN admin_user_roles ur ON u.id = ur.admin_user_id " +
           "WHERE ur.role_id = :roleId")
    Flux<AdminUser> findByRoleId(String roleId);

    /**
     * 查找没有分配角色的管理员用户
     */
    @Query("SELECT * FROM admin_users WHERE id NOT IN " +
           "(SELECT DISTINCT admin_user_id FROM admin_user_roles)")
    Flux<AdminUser> findUsersWithoutRoles();

    /**
     * 按创建时间范围查询
     */
    @Query("SELECT * FROM admin_users WHERE created_at BETWEEN :startTime AND :endTime")
    Flux<AdminUser> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找邮箱域名的管理员用户统计
     */
    @Query("SELECT SUBSTRING(email FROM POSITION('@' IN email) + 1), COUNT(*) " +
           "FROM admin_users GROUP BY SUBSTRING(email FROM POSITION('@' IN email) + 1)")
    Flux<Object[]> countByEmailDomain();
}