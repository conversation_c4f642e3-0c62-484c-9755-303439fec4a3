package com.implatform.user.entity;

import com.implatform.user.enums.RewardType;
import com.implatform.user.enums.RewardStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 用户等级奖励实体
 * 管理用户升级奖励的发放和领取
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_level_reward", indexes = {
    @Index(name = "idx_level_reward_user_id", columnList = "user_id"),
    @Index(name = "idx_level_reward_level_id", columnList = "level_id"),
    @Index(name = "idx_level_reward_status", columnList = "status"),
    @Index(name = "idx_level_reward_user_status", columnList = "user_id, status"),
    @Index(name = "idx_level_reward_user_level", columnList = "user_id, level_id"),
    @Index(name = "idx_level_reward_earned_at", columnList = "earned_at"),
    @Index(name = "idx_level_reward_claimed_at", columnList = "claimed_at"),
    @Index(name = "idx_level_reward_expires_at", columnList = "expires_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLevelReward {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 等级ID
     */
    @NotNull(message = "Level ID cannot be null")
    @Column(name = "level_id", nullable = false)
    private Long levelId;

    /**
     * 达到的等级
     */
    @NotNull(message = "Level cannot be null")
    @Min(value = 1, message = "Level must be at least 1")
    @Column(name = "level", nullable = false)
    private Integer level;

    /**
     * 奖励类型
     */
    @NotNull(message = "Reward type cannot be null")
    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type", nullable = false, length = 30)
    private RewardType rewardType;

    /**
     * 奖励值
     */
    @NotBlank(message = "Reward value cannot be blank")
    @Size(max = 500, message = "Reward value cannot exceed 500 characters")
    @Column(name = "reward_value", nullable = false, length = 500)
    private String rewardValue;

    /**
     * 奖励数量
     */
    @Builder.Default
    @Column(name = "reward_quantity", nullable = false)
    private Integer rewardQuantity = 1;

    /**
     * 奖励描述
     */
    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;

    /**
     * 奖励状态
     */
    @NotNull(message = "Status cannot be null")
    @Enumerated(EnumType.STRING)
    @Builder.Default
    @Column(name = "status", nullable = false, length = 20)
    private RewardStatus status = RewardStatus.PENDING;

    /**
     * 获得时间
     */
    @NotNull(message = "Earned time cannot be null")
    @CreationTimestamp
    @Column(name = "earned_at", nullable = false)
    private Instant earnedAt;

    /**
     * 领取时间
     */
    @Column(name = "claimed_at")
    private Instant claimedAt;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    private Instant expiresAt;

    /**
     * 奖励图标URL
     */
    @Size(max = 500, message = "Icon URL cannot exceed 500 characters")
    @Column(name = "icon_url", length = 500)
    private String iconUrl;

    /**
     * 奖励标题
     */
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    @Column(name = "title", length = 200)
    private String title;

    /**
     * 是否为特殊奖励
     */
    @Builder.Default
    @Column(name = "is_special", nullable = false)
    private Boolean isSpecial = false;

    /**
     * 是否为限时奖励
     */
    @Builder.Default
    @Column(name = "is_limited_time", nullable = false)
    private Boolean isLimitedTime = false;

    /**
     * 奖励优先级
     */
    @Builder.Default
    @Column(name = "priority", nullable = false)
    private Integer priority = 0;

    /**
     * 领取条件（JSON格式）
     */
    @Size(max = 1000, message = "Claim conditions cannot exceed 1000 characters")
    @Column(name = "claim_conditions", length = 1000)
    private String claimConditions;

    /**
     * 额外数据（JSON格式）
     */
    @Size(max = 2000, message = "Extra data cannot exceed 2000 characters")
    @Column(name = "extra_data", length = 2000)
    private String extraData;

    /**
     * 领取IP地址
     */
    @Size(max = 45, message = "Claim IP cannot exceed 45 characters")
    @Column(name = "claim_ip", length = 45)
    private String claimIp;

    /**
     * 领取设备信息
     */
    @Size(max = 500, message = "Claim device cannot exceed 500 characters")
    @Column(name = "claim_device", length = 500)
    private String claimDevice;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    @Column(name = "notes", length = 1000)
    private String notes;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 用户实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 等级配置实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "level_id", insertable = false, updatable = false)
    private UserLevelConfig levelConfig;

    /**
     * 领取奖励
     * 
     * @param claimIp 领取IP
     * @param claimDevice 领取设备
     * @return 是否成功领取
     */
    public boolean claimReward(String claimIp, String claimDevice) {
        if (!canClaim()) {
            return false;
        }

        this.status = RewardStatus.CLAIMED;
        this.claimedAt = Instant.now();
        this.claimIp = claimIp;
        this.claimDevice = claimDevice;
        return true;
    }

    /**
     * 检查是否可以领取
     * 
     * @return 是否可以领取
     */
    public boolean canClaim() {
        if (status != RewardStatus.PENDING) {
            return false;
        }

        // 检查是否过期
        if (isExpired()) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否已过期
     * 
     * @return 是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && Instant.now().isAfter(expiresAt);
    }

    /**
     * 检查是否已领取
     * 
     * @return 是否已领取
     */
    public boolean isClaimed() {
        return RewardStatus.CLAIMED.equals(status);
    }

    /**
     * 检查是否待领取
     * 
     * @return 是否待领取
     */
    public boolean isPending() {
        return RewardStatus.PENDING.equals(status);
    }

    /**
     * 设置过期
     */
    public void markAsExpired() {
        this.status = RewardStatus.EXPIRED;
    }

    /**
     * 设置为已发放
     */
    public void markAsDelivered() {
        this.status = RewardStatus.DELIVERED;
    }

    /**
     * 设置过期时间
     * 
     * @param days 过期天数
     */
    public void setExpirationDays(int days) {
        this.isLimitedTime = true;
        this.expiresAt = Instant.now().plusSeconds(days * 24 * 60 * 60);
    }

    /**
     * 获取剩余有效时间（秒）
     * 
     * @return 剩余秒数，null表示永不过期
     */
    public Long getRemainingSeconds() {
        if (expiresAt == null) {
            return null;
        }
        
        long remaining = expiresAt.getEpochSecond() - Instant.now().getEpochSecond();
        return Math.max(0, remaining);
    }

    /**
     * 获取剩余有效时间显示文本
     * 
     * @return 剩余时间文本
     */
    public String getRemainingTimeDisplay() {
        Long remainingSeconds = getRemainingSeconds();
        if (remainingSeconds == null) {
            return "永久有效";
        }
        
        if (remainingSeconds <= 0) {
            return "已过期";
        }
        
        long days = remainingSeconds / (24 * 60 * 60);
        long hours = (remainingSeconds % (24 * 60 * 60)) / (60 * 60);
        long minutes = (remainingSeconds % (60 * 60)) / 60;
        
        if (days > 0) {
            return String.format("%d天%d小时", days, hours);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes);
        } else {
            return String.format("%d分钟", minutes);
        }
    }

    /**
     * 获取奖励显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (title != null && !title.trim().isEmpty()) {
            return title;
        }
        
        String typeName = rewardType != null ? rewardType.getChineseName() : "未知奖励";
        if (rewardQuantity > 1) {
            return String.format("%s x%d", typeName, rewardQuantity);
        }
        return typeName;
    }

    /**
     * 创建等级奖励
     * 
     * @param userId 用户ID
     * @param levelId 等级ID
     * @param level 等级
     * @param rewardType 奖励类型
     * @param rewardValue 奖励值
     * @param description 描述
     * @return 奖励实体
     */
    public static UserLevelReward create(Long userId, Long levelId, Integer level, 
                                       RewardType rewardType, String rewardValue, String description) {
        return UserLevelReward.builder()
                .userId(userId)
                .levelId(levelId)
                .level(level)
                .rewardType(rewardType)
                .rewardValue(rewardValue)
                .description(description)
                .status(RewardStatus.PENDING)
                .earnedAt(Instant.now())
                .build();
    }

    /**
     * 创建特殊奖励
     * 
     * @param userId 用户ID
     * @param levelId 等级ID
     * @param level 等级
     * @param rewardType 奖励类型
     * @param rewardValue 奖励值
     * @param description 描述
     * @param expirationDays 过期天数
     * @return 特殊奖励实体
     */
    public static UserLevelReward createSpecial(Long userId, Long levelId, Integer level, 
                                              RewardType rewardType, String rewardValue, 
                                              String description, int expirationDays) {
        UserLevelReward reward = create(userId, levelId, level, rewardType, rewardValue, description);
        reward.setIsSpecial(true);
        reward.setExpirationDays(expirationDays);
        return reward;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserLevelReward that = (UserLevelReward) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserLevelReward{" +
                "id=" + id +
                ", userId=" + userId +
                ", level=" + level +
                ", rewardType=" + rewardType +
                ", status=" + status +
                ", earnedAt=" + earnedAt +
                '}';
    }
}
