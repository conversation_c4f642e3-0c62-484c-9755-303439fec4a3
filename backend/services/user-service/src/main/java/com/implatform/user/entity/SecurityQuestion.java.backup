package com.implatform.user.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 密保问题实体类 - IM平台密保问题库
 *
 * <p><strong>实体概述</strong>：
 * 本实体类用于存储系统预设的密保问题，类似QQ安全中心的密保问题库。
 * 用户在注册或设置密保时可以从这些问题中选择，用于账号安全验证和密码找回。
 *
 * <p><strong>核心功能</strong>：
 * <ul>
 *   <li><strong>问题分类</strong>：按类型分类管理密保问题（个人、家庭、教育、喜好等）</li>
 *   <li><strong>问题排序</strong>：支持问题显示顺序管理</li>
 *   <li><strong>状态控制</strong>：支持问题的启用和禁用</li>
 *   <li><strong>扩展性</strong>：支持动态添加新的密保问题</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "security_questions", indexes = {
    @Index(name = "idx_question_type", columnList = "question_type"),
    @Index(name = "idx_active_sort", columnList = "is_active, sort_order")
})
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class SecurityQuestion {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 问题内容
     */
    @NotBlank(message = "问题内容不能为空")
    @Size(max = 200, message = "问题内容长度不能超过200个字符")
    @Column(name = "question_text", nullable = false, length = 200)
    private String questionText;
    
    /**
     * 问题类型
     */
    @NotBlank(message = "问题类型不能为空")
    @Size(max = 50, message = "问题类型长度不能超过50个字符")
    @Column(name = "question_type", nullable = false, length = 50)
    private String questionType;
    
    /**
     * 是否启用
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    /**
     * 排序顺序
     */
    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 问题类型枚举
     */
    public enum QuestionType {
        PERSONAL("个人信息"),
        FAMILY("家庭信息"),
        EDUCATION("教育信息"),
        PREFERENCE("喜好偏好"),
        PET("宠物信息"),
        OTHER("其他信息");
        
        private final String description;
        
        QuestionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 启用问题
     */
    public void activate() {
        this.isActive = true;
    }
    
    /**
     * 禁用问题
     */
    public void deactivate() {
        this.isActive = false;
    }
    
    /**
     * 检查问题是否可用
     */
    public boolean isAvailable() {
        return Boolean.TRUE.equals(isActive);
    }
    
    /**
     * 获取问题类型描述
     */
    public String getQuestionTypeDescription() {
        try {
            return QuestionType.valueOf(questionType.toUpperCase()).getDescription();
        } catch (IllegalArgumentException e) {
            return questionType;
        }
    }
}
