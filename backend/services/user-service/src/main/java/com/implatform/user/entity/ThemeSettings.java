package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * 用户主题设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_theme_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThemeSettings {
    
    @Id
    @Column("user_id")
    private Long userId;
    
    // ==================== 基础主题设置 ====================
    
    /**
     * 主题模式
     */
        @Column("theme_mode")
    private ThemeMode themeMode = ThemeMode.SYSTEM;
    
    /**
     * 主色调
     */
    @Column("primary_color")
    private String primaryColor = "#2196F3";
    
    /**
     * 强调色
     */
    @Column("accent_color")
    private String accentColor = "#FF4081";
    
    /**
     * 背景色
     */
    @Column("background_color")
    private String backgroundColor = "#FFFFFF";
    
    /**
     * 文本颜色
     */
    @Column("text_color")
    private String textColor = "#000000";
    
    // ==================== 字体设置 ====================
    
    /**
     * 字体大小
     */
        @Column("font_size")
    private FontSize fontSize = FontSize.MEDIUM;
    
    /**
     * 字体家族
     */
    @Column("font_family")
    private String fontFamily = "system-ui";
    
    /**
     * 字体粗细
     */
        @Column("font_weight")
    private FontWeight fontWeight = FontWeight.NORMAL;
    
    // ==================== 界面设置 ====================
    
    /**
     * 动画效果开关
     */
    @Column("animations_enabled")
    private Boolean animationsEnabled = true;
    
    /**
     * 动画速度
     */
        @Column("animation_speed")
    private AnimationSpeed animationSpeed = AnimationSpeed.NORMAL;
    
    /**
     * 聊天背景
     */
    @Column("chat_background")
    private String chatBackground = "default";
    
    /**
     * 聊天背景类型
     */
        @Column("chat_background_type")
    private BackgroundType chatBackgroundType = BackgroundType.COLOR;
    
    /**
     * 聊天气泡样式
     */
        @Column("chat_bubble_style")
    private BubbleStyle chatBubbleStyle = BubbleStyle.ROUNDED;
    
    // ==================== 语言和地区设置 ====================
    
    /**
     * 界面语言
     */
        @Column("language")
    private Language language = Language.ZH_CN;
    
    /**
     * 时区
     */
    @Column("timezone")
    private String timezone = "Asia/Shanghai";
    
    /**
     * 日期格式
     */
        @Column("date_format")
    private DateFormat dateFormat = DateFormat.YYYY_MM_DD;
    
    /**
     * 时间格式
     */
        @Column("time_format")
    private TimeFormat timeFormat = TimeFormat.H24;
    
    // ==================== 高级设置 ====================
    
    /**
     * 紧凑模式
     */
    @Column("compact_mode")
    private Boolean compactMode = false;
    
    /**
     * 显示头像
     */
    @Column("show_avatars")
    private Boolean showAvatars = true;
    
    /**
     * 显示在线状态
     */
    @Column("show_online_status")
    private Boolean showOnlineStatus = true;
    
    /**
     * 显示消息时间
     */
    @Column("show_message_time")
    private Boolean showMessageTime = true;
    
    /**
     * 自定义CSS
     */
    @Column("custom_css")
    private String customCss;
    
    // ==================== 时间戳 ====================
    
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;
    
    // ==================== 关联关系 ====================
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    // ==================== 枚举定义 ====================
    
    /**
     * 主题模式枚举
     */
    public enum ThemeMode {
        LIGHT("浅色模式"),
        DARK("深色模式"),
        SYSTEM("跟随系统");
        
        private final String description;
        
        ThemeMode(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 字体大小枚举
     */
    public enum FontSize {
        SMALL("小"),
        MEDIUM("中"),
        LARGE("大"),
        EXTRA_LARGE("特大");
        
        private final String description;
        
        FontSize(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 字体粗细枚举
     */
    public enum FontWeight {
        LIGHT("细体"),
        NORMAL("正常"),
        BOLD("粗体");
        
        private final String description;
        
        FontWeight(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 动画速度枚举
     */
    public enum AnimationSpeed {
        SLOW("慢"),
        NORMAL("正常"),
        FAST("快"),
        OFF("关闭");
        
        private final String description;
        
        AnimationSpeed(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 背景类型枚举
     */
    public enum BackgroundType {
        COLOR("纯色"),
        GRADIENT("渐变"),
        IMAGE("图片"),
        PATTERN("图案");
        
        private final String description;
        
        BackgroundType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 气泡样式枚举
     */
    public enum BubbleStyle {
        ROUNDED("圆角"),
        SQUARE("方角"),
        MINIMAL("简约");
        
        private final String description;
        
        BubbleStyle(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 语言枚举
     */
    public enum Language {
        ZH_CN("简体中文"),
        ZH_TW("繁体中文"),
        EN_US("English"),
        JA_JP("日本語"),
        KO_KR("한국어"),
        ES_ES("Español"),
        FR_FR("Français"),
        DE_DE("Deutsch"),
        RU_RU("Русский");
        
        private final String description;
        
        Language(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 日期格式枚举
     */
    public enum DateFormat {
        YYYY_MM_DD("2024-01-01"),
        MM_DD_YYYY("01/01/2024"),
        DD_MM_YYYY("01/01/2024"),
        YYYY_MM_DD_CN("2024年1月1日");
        
        private final String example;
        
        DateFormat(String example) {
            this.example = example;
        }
        
        public String getExample() {
            return example;
        }
    }
    
    /**
     * 时间格式枚举
     */
    public enum TimeFormat {
        H12("12小时制"),
        H24("24小时制");
        
        private final String description;
        
        TimeFormat(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数 - 创建默认主题设置
     */
    public ThemeSettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查是否为深色主题
     */
    public boolean isDarkTheme() {
        return themeMode == ThemeMode.DARK;
    }
    
    /**
     * 获取有效的背景设置
     */
    public String getEffectiveBackground() {
        if (chatBackground != null && !chatBackground.equals("default")) {
            return chatBackground;
        }
        return isDarkTheme() ? "#121212" : "#FFFFFF";
    }
    
    /**
     * 获取字体大小的像素值
     */
    public int getFontSizePixels() {
        return switch (fontSize) {
            case SMALL -> 12;
            case MEDIUM -> 14;
            case LARGE -> 16;
            case EXTRA_LARGE -> 18;
        };
    }
}
