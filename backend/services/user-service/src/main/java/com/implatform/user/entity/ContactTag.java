package com.implatform.user.entity;

import java.time.Instant;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 联系人标签实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("contact_tags"),
       indexes = {
    @Index(name = "idx_contact_tag_user", columnList = "user_id"),
    @Index(name = "idx_contact_tag_name", columnList = "tag_name"),
    @Index(name = "idx_contact_tag_color", columnList = "tag_color"),
    @Index(name = "idx_contact_tag_sort", columnList = "sort_order")
})
@EqualsAndHashCode(callSuper = false)
public class ContactTag {

    /**
     * 联系人标签ID
     */
    @Id
        private Long id;

    /**
     * 标签所属用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 标签名称
     */
    @Column("tag_name")
    private String tagName;

    /**
     * 标签描述
     */
    @Column("description")
    private String description;

    /**
     * 标签颜色
     */
    @Column("tag_color")
    private String tagColor = "#1890FF";

    /**
     * 标签图标
     */
    @Column("tag_icon")
    private String tagIcon;

    /**
     * 排序顺序
     */
    @Column("sort_order")
    private Integer sortOrder = 0;

    /**
     * 标签类型
     */
        @Column("tag_type")
    private TagType tagType = TagType.CUSTOM;

    /**
     * 是否为系统标签
     */
    @Column("is_system")
    private Boolean isSystem = false;

    /**
     * 是否启用
     */
    @Column("is_enabled")
    private Boolean isEnabled = true;

    /**
     * 联系人数量
     */
    @Column("contact_count")
    private Integer contactCount = 0;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 标签类型枚举
     */
    public enum TagType {
        CUSTOM("自定义标签"),
        SYSTEM("系统标签"),
        SMART("智能标签"),
        TEMPORARY("临时标签");

        private final String description;

        TagType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 默认构造函数
     */
    public ContactTag() {}

    /**
     * 构造函数
     */
    public ContactTag(Long userId, String tagName, String tagColor) {
        this.userId = userId;
        this.tagName = tagName;
        this.tagColor = tagColor;
    }

    /**
     * 增加联系人数量
     */
    public void incrementContactCount() {
        this.contactCount++;
    }

    /**
     * 减少联系人数量
     */
    public void decrementContactCount() {
        if (this.contactCount > 0) {
            this.contactCount--;
        }
    }

    /**
     * 设置联系人数量
     */
    public void setContactCount(Integer count) {
        this.contactCount = count != null && count >= 0 ? count : 0;
    }

    /**
     * 检查是否可以删除
     */
    public boolean canDelete() {
        return !isSystem && contactCount == 0;
    }

    /**
     * 检查是否可以编辑
     */
    public boolean canEdit() {
        return !isSystem;
    }

    /**
     * 获取标签显示名称
     */
    public String getDisplayName() {
        return tagName + (contactCount > 0 ? " (" + contactCount + ")" : "");
    }

    /**
     * 检查标签名称是否有效
     */
    public boolean isValidTagName() {
        return tagName != null && 
               !tagName.trim().isEmpty() && 
               tagName.length() <= 50 &&
               !tagName.contains(",") && 
               !tagName.contains(";");
    }

    /**
     * 检查颜色格式是否有效
     */
    public boolean isValidColor() {
        return tagColor != null && 
               tagColor.matches("^#[0-9A-Fa-f]{6}$");
    }

    /**
     * 获取标签类型描述
     */
    public String getTagTypeDescription() {
        return tagType.getDescription();
    }

    /**
     * 是否为空标签
     */
    public boolean isEmpty() {
        return contactCount == 0;
    }

    /**
     * 是否为热门标签
     */
    public boolean isPopular() {
        return contactCount >= 10;
    }

    /**
     * 获取标签权重（用于排序）
     */
    public int getWeight() {
        // 系统标签权重最高，然后按联系人数量和排序顺序
        int weight = 0;
        if (isSystem) {
            weight += 10000;
        }
        weight += contactCount * 100;
        weight += (1000 - sortOrder); // 排序顺序越小权重越高
        return weight;
    }

    /**
     * 复制标签（不包括ID和时间戳）
     */
    public ContactTag copy() {
        ContactTag copy = new ContactTag();
        copy.setUserId(this.userId);
        copy.setTagName(this.tagName + "_copy");
        copy.setDescription(this.description);
        copy.setTagColor(this.tagColor);
        copy.setTagIcon(this.tagIcon);
        copy.setSortOrder(this.sortOrder + 1);
        copy.setTagType(TagType.CUSTOM); // 复制的标签都是自定义类型
        copy.setIsSystem(false);
        copy.setIsEnabled(this.isEnabled);
        copy.setContactCount(0); // 复制的标签联系人数量为0
        return copy;
    }

    /**
     * 更新标签信息
     */
    public void updateInfo(String tagName, String description, String tagColor, String tagIcon) {
        if (canEdit()) {
            if (tagName != null && !tagName.trim().isEmpty()) {
                this.tagName = tagName.trim();
            }
            if (description != null) {
                this.description = description.trim();
            }
            if (tagColor != null && isValidColorFormat(tagColor)) {
                this.tagColor = tagColor;
            }
            if (tagIcon != null) {
                this.tagIcon = tagIcon.trim();
            }
        }
    }

    /**
     * 验证颜色格式
     */
    private boolean isValidColorFormat(String color) {
        return color.matches("^#[0-9A-Fa-f]{6}$");
    }

    /**
     * 切换启用状态
     */
    public void toggleEnabled() {
        if (canEdit()) {
            this.isEnabled = !this.isEnabled;
        }
    }

    /**
     * 重置联系人数量
     */
    public void resetContactCount() {
        this.contactCount = 0;
    }

    /**
     * 获取标签摘要信息
     */
    public String getSummary() {
        return String.format("标签[%s] 类型[%s] 联系人[%d] 状态[%s]", 
                tagName, 
                tagType.getDescription(), 
                contactCount, 
                isEnabled ? "启用" : "禁用");
    }

    /**
     * 检查是否为默认标签
     */
    public boolean isDefaultTag() {
        return isSystem && ("朋友".equals(tagName) || "同事".equals(tagName) || "家人".equals(tagName));
    }

    /**
     * 获取标签优先级
     */
    public int getPriority() {
        if (isSystem) return 1;
        if (tagType == TagType.SMART) return 2;
        if (contactCount > 0) return 3;
        return 4;
    }
}
