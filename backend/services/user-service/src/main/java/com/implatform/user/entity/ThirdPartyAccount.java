package com.implatform.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.implatform.user.enums.ThirdPartyProvider;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

/**
 * 第三方账号实体类 - IM平台第三方登录账号信息
 *
 * <p><strong>实体概述</strong>：
 * 本实体类用于存储用户的第三方登录账号信息，支持微信、QQ、微博等多种第三方平台的账号绑定。
 * 提供完整的第三方账号管理功能，包括令牌管理、用户信息同步、账号绑定状态等。
 *
 * <p><strong>核心功能</strong>：
 * <ul>
 *   <li><strong>账号绑定</strong>：支持用户绑定多个第三方平台账号</li>
 *   <li><strong>令牌管理</strong>：安全存储和管理第三方平台的访问令牌</li>
 *   <li><strong>用户信息同步</strong>：同步第三方平台的用户基本信息</li>
 *   <li><strong>登录状态跟踪</strong>：记录最后登录时间和登录状态</li>
 *   <li><strong>数据安全</strong>：敏感信息加密存储，支持数据脱敏</li>
 * </ul>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 third_party_accounts 表，包含用户ID、平台类型、平台用户ID等关键字段的索引，
 * 支持高效的第三方账号查询、绑定状态检查和登录验证操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("third_party_accounts"),
    @Index(name = "idx_third_party_provider_type", columnList = "provider_type"),
    @Index(name = "idx_third_party_provider_user_id", columnList = "provider_user_id"),
    @Index(name = "idx_third_party_union_id", columnList = "union_id"),
    @Index(name = "idx_third_party_bind_time", columnList = "bind_time"),
    @Index(name = "idx_third_party_last_login", columnList = "last_login_time")
}, uniqueConstraints = {
    @UniqueConstraint(name = "uk_provider_user", columnNames = {"provider_type", "provider_user_id"}),
    @UniqueConstraint(name = "uk_provider_union", columnNames = {"provider_type", "union_id"})
})
@EqualsAndHashCode(callSuper = false)
public class ThirdPartyAccount {
    
    @Id
        private Long id;
    
    /**
     * 关联的用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column("user_id")
    private Long userId;
    
    /**
     * 第三方平台类型
     */
    @NotNull(message = "第三方平台类型不能为空")
        @Column("provider_type")
    private ThirdPartyProvider providerType;
    
    /**
     * 第三方平台的用户ID
     */
    @NotBlank(message = "第三方平台用户ID不能为空")
    @Size(max = 100, message = "第三方平台用户ID长度不能超过100个字符")
    @Column("provider_user_id")
    private String providerUserId;
    
    /**
     * 第三方平台的UnionID（如微信）
     */
    @Size(max = 100, message = "UnionID长度不能超过100个字符")
    @Column("union_id")
    private String unionId;
    
    /**
     * 访问令牌（加密存储）
     */
    @JsonIgnore
    @Column("access_token")
    private String accessToken;
    
    /**
     * 刷新令牌（加密存储）
     */
    @JsonIgnore
    @Column("refresh_token")
    private String refreshToken;
    
    /**
     * 令牌过期时间
     */
    @Column("expires_at")
    private LocalDateTime expiresAt;
    
    /**
     * 第三方平台返回的用户信息（JSON格式）
     */
    @Column("user_info")
    private String userInfo;
    
    /**
     * 第三方平台昵称
     */
    @Size(max = 100, message = "昵称长度不能超过100个字符")
    @Column
    private String nickname;
    
    /**
     * 第三方平台头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    @Column("avatar_url")
    private String avatarUrl;
    
    /**
     * 第三方平台邮箱
     */
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @Column
    private String email;
    
    /**
     * 第三方平台手机号
     */
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    @Column
    private String phone;
    
    /**
     * 性别：0-未知，1-男，2-女
     */
    @Column
    private Integer gender;
    
    /**
     * 国家
     */
    @Size(max = 50, message = "国家长度不能超过50个字符")
    @Column
    private String country;
    
    /**
     * 省份
     */
    @Size(max = 50, message = "省份长度不能超过50个字符")
    @Column
    private String province;
    
    /**
     * 城市
     */
    @Size(max = 50, message = "城市长度不能超过50个字符")
    @Column
    private String city;
    
    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive = true;
    
    /**
     * 绑定时间
     */
    @Column("bind_time")
    private LocalDateTime bindTime = LocalDateTime.now();
    
    /**
     * 最后登录时间
     */
    @Column("last_login_time")
    private LocalDateTime lastLoginTime;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 用户关联（多对一）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @JsonIgnore
    private User user;
    
    /**
     * 更新最后登录时间
     */
    public void updateLastLoginTime() {
        this.lastLoginTime = LocalDateTime.now();
    }
    
    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * 激活账号
     */
    public void activate() {
        this.isActive = true;
    }
    
    /**
     * 停用账号
     */
    public void deactivate() {
        this.isActive = false;
    }
    
    /**
     * 更新令牌信息
     */
    public void updateTokens(String accessToken, String refreshToken, LocalDateTime expiresAt) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresAt = expiresAt;
    }
    
    /**
     * 更新用户信息
     */
    public void updateUserInfo(String nickname, String avatarUrl, String email, String phone, 
                              Integer gender, String country, String province, String city) {
        this.nickname = nickname;
        this.avatarUrl = avatarUrl;
        this.email = email;
        this.phone = phone;
        this.gender = gender;
        this.country = country;
        this.province = province;
        this.city = city;
    }
}
