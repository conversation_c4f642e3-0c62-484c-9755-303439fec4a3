package com.implatform.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;

/**
 * 用户密保答案实体类 - IM平台用户密保答案存储
 *
 * <p><strong>实体概述</strong>：
 * 本实体类用于存储用户设置的密保问题答案，类似QQ安全中心的密保设置。
 * 用户需要设置3个密保问题的答案，用于账号安全验证和密码找回功能。
 *
 * <p><strong>核心功能</strong>：
 * <ul>
 *   <li><strong>答案加密</strong>：密保答案使用哈希加密存储，确保安全性</li>
 *   <li><strong>问题排序</strong>：支持密保问题的顺序管理（第1、2、3个问题）</li>
 *   <li><strong>关联管理</strong>：与用户和密保问题的关联关系管理</li>
 *   <li><strong>验证支持</strong>：提供答案验证和比对功能</li>
 * </ul>
 *
 * <p><strong>安全特性</strong>：
 * <ul>
 *   <li><strong>哈希存储</strong>：答案使用加盐哈希算法存储</li>
 *   <li><strong>唯一约束</strong>：确保用户每个问题只能有一个答案</li>
 *   <li><strong>顺序约束</strong>：确保用户的密保问题顺序唯一</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("user_security_answers"),
    @Index(name = "idx_user_security_answers_question_id", columnList = "question_id")
}, uniqueConstraints = {
    @UniqueConstraint(name = "uk_user_security_answers_user_question", columnNames = {"user_id", "question_id"}),
    @UniqueConstraint(name = "uk_user_security_answers_user_order", columnNames = {"user_id", "question_order"})
})
@EqualsAndHashCode(callSuper = false)
public class UserSecurityAnswer {
    
    @Id
        private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Column("user_id")
    private Long userId;
    
    /**
     * 问题ID
     */
    @NotNull(message = "问题ID不能为空")
    @Column("question_id")
    private Long questionId;
    
    /**
     * 答案哈希值
     */
    @JsonIgnore
    @NotBlank(message = "答案哈希值不能为空")
    @Column("answer_hash")
    private String answerHash;
    
    /**
     * 答案盐值
     */
    @JsonIgnore
    @NotBlank(message = "答案盐值不能为空")
    @Column("answer_salt")
    private String answerSalt;
    
    /**
     * 问题顺序：1、2、3
     */
    @NotNull(message = "问题顺序不能为空")
    @Min(value = 1, message = "问题顺序最小值为1")
    @Max(value = 3, message = "问题顺序最大值为3")
    @Column("question_order")
    private Integer questionOrder;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 用户关联（多对一）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    @JsonIgnore
    private User user;
    
    /**
     * 密保问题关联（多对一）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "question_id", insertable = false, updatable = false)
    private SecurityQuestion securityQuestion;
    
    /**
     * 更新答案
     */
    public void updateAnswer(String answerHash, String answerSalt) {
        this.answerHash = answerHash;
        this.answerSalt = answerSalt;
    }
    
    /**
     * 检查答案是否有效
     */
    public boolean hasValidAnswer() {
        return answerHash != null && !answerHash.trim().isEmpty() &&
               answerSalt != null && !answerSalt.trim().isEmpty();
    }
    
    /**
     * 获取问题顺序描述
     */
    public String getQuestionOrderDescription() {
        switch (questionOrder) {
            case 1:
                return "第一个密保问题";
            case 2:
                return "第二个密保问题";
            case 3:
                return "第三个密保问题";
            default:
                return "密保问题";
        }
    }
}
