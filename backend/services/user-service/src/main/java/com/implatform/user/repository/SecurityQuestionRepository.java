package com.implatform.user.repository;

import com.implatform.user.entity.SecurityQuestion;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 密保问题数据访问层 - IM平台密保问题库数据库操作接口
 *
 * <p><strong>Repository概述</strong>：
 * 本接口提供密保问题库的数据访问功能，负责密保问题的CRUD操作、分类查询、
 * 状态管理等数据库交互。支持类似QQ安全中心的密保问题管理功能。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface SecurityQuestionRepository extends ReactiveCrudRepository<SecurityQuestion, Long> {

    /**
     * 查询所有启用的密保问题
     */
    Flux<SecurityQuestion> findByIsActiveTrueOrderBySortOrder();

    /**
     * 根据问题类型查询启用的密保问题
     */
    Flux<SecurityQuestion> findByQuestionTypeAndIsActiveTrueOrderBySortOrder(String questionType);

    /**
     * 分页查询密保问题
     */
    @Query("SELECT * FROM security_questions WHERE is_active = true ORDER BY sort_order LIMIT :limit OFFSET :offset")
    Flux<SecurityQuestion> findByIsActiveTrueOrderBySortOrder(int limit, long offset);
    
    /**
     * 根据问题类型分页查询
     */
    @Query("SELECT * FROM security_questions WHERE question_type = :questionType AND is_active = true ORDER BY sort_order LIMIT :limit OFFSET :offset")
    Flux<SecurityQuestion> findByQuestionTypeAndIsActiveTrueOrderBySortOrder(String questionType, int limit, long offset);

    /**
     * 查询所有问题类型
     */
    @Query("SELECT DISTINCT question_type FROM security_questions WHERE is_active = true ORDER BY question_type")
    Flux<String> findDistinctQuestionTypes();

    /**
     * 根据问题类型统计数量
     */
    Mono<Long> countByQuestionTypeAndIsActiveTrue(String questionType);

    /**
     * 统计启用的问题总数
     */
    Mono<Long> countByIsActiveTrue();

    /**
     * 查询指定数量的随机密保问题
     */
    @Query("SELECT * FROM security_questions WHERE is_active = true ORDER BY RANDOM() LIMIT :limit")
    Flux<SecurityQuestion> findRandomQuestions(int limit);

    /**
     * 根据问题内容模糊查询
     */
    @Query("SELECT * FROM security_questions WHERE question_text LIKE CONCAT('%', :keyword, '%') AND is_active = true ORDER BY sort_order")
    Flux<SecurityQuestion> findByQuestionTextContaining(String keyword);

    /**
     * 查询最新添加的密保问题
     */
    @Query("SELECT * FROM security_questions WHERE is_active = true ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<SecurityQuestion> findLatestQuestions(int limit, long offset);
}
