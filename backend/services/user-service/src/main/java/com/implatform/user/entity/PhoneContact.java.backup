package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 手机联系人实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "phone_contacts")
@EqualsAndHashCode(callSuper = false)
public class PhoneContact {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", length = 100)
    private String contactName;

    /**
     * 手机号码
     */
    @Column(name = "phone_number", length = 20, nullable = false)
    private String phoneNumber;

    /**
     * 格式化后的手机号码（用于匹配）
     */
    @Column(name = "normalized_phone", length = 20)
    private String normalizedPhone;

    /**
     * 邮箱地址
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 匹配到的用户ID
     */
    @Column(name = "matched_user_id")
    private Long matchedUserId;

    /**
     * 是否已邀请
     */
    @Column(name = "is_invited", nullable = false)
    private Boolean isInvited = false;

    /**
     * 邀请时间
     */
    @Column(name = "invited_at")
    private Instant invitedAt;

    /**
     * 是否已成为好友
     */
    @Column(name = "is_friend", nullable = false)
    private Boolean isFriend = false;

    /**
     * 成为好友时间
     */
    @Column(name = "friend_since")
    private Instant friendSince;

    /**
     * 联系人来源
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "contact_source", nullable = false)
    private ContactSource contactSource = ContactSource.PHONE_BOOK;

    /**
     * 同步批次ID
     */
    @Column(name = "sync_batch_id")
    private Long syncBatchId;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 联系人来源枚举
     */
    public enum ContactSource {
        /**
         * 手机通讯录
         */
        PHONE_BOOK,
        
        /**
         * 手动添加
         */
        MANUAL,
        
        /**
         * 导入文件
         */
        IMPORT,
        
        /**
         * 社交平台
         */
        SOCIAL_PLATFORM
    }

    /**
     * 标记为已邀请
     */
    public void markAsInvited() {
        this.isInvited = true;
        this.invitedAt = Instant.now();
    }

    /**
     * 标记为好友
     */
    public void markAsFriend(Long friendUserId) {
        this.isFriend = true;
        this.matchedUserId = friendUserId;
        this.friendSince = Instant.now();
    }

    /**
     * 检查是否已匹配到用户
     */
    public boolean isMatched() {
        return matchedUserId != null;
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        if (contactName != null && !contactName.trim().isEmpty()) {
            return contactName;
        }
        return phoneNumber;
    }

    /**
     * 格式化手机号码
     */
    public static String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) return null;
        
        // 移除所有非数字字符
        String normalized = phoneNumber.replaceAll("[^0-9]", "");
        
        // 处理中国手机号码
        if (normalized.startsWith("86") && normalized.length() == 13) {
            normalized = normalized.substring(2);
        } else if (normalized.startsWith("+86") && normalized.length() == 14) {
            normalized = normalized.substring(3);
        }
        
        return normalized;
    }

    /**
     * 设置并格式化手机号码
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        this.normalizedPhone = normalizePhoneNumber(phoneNumber);
    }
}
