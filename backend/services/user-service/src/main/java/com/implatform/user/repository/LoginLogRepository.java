package com.implatform.user.repository;

import com.implatform.user.entity.LoginLog;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 登录日志数据访问层
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface LoginLogRepository extends ReactiveCrudRepository<LoginLog, Long> {

    /**
     * 根据用户ID分页查询登录日志
     */
    @Query("SELECT * FROM login_logs WHERE user_id = :userId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<LoginLog> findByUserIdOrderByCreatedAtDesc(Long userId, int limit, long offset);

    /**
     * 根据用户ID和状态分页查询登录日志
     */
    @Query("SELECT * FROM login_logs WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<LoginLog> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, LoginLog.LoginStatus status, int limit, long offset);

    /**
     * 根据设备ID查询登录日志
     */
    Flux<LoginLog> findByDeviceIdOrderByCreatedAtDesc(String deviceId);

    /**
     * 根据客户端IP查询登录日志
     */
    Flux<LoginLog> findByClientIpOrderByCreatedAtDesc(String clientIp);

    /**
     * 根据登录类型查询登录日志
     */
    @Query("SELECT * FROM login_logs WHERE login_type = :loginType ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<LoginLog> findByLoginTypeOrderByCreatedAtDesc(LoginLog.LoginType loginType, int limit, long offset);

    /**
     * 根据登录状态查询登录日志
     */
    @Query("SELECT * FROM login_logs WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<LoginLog> findByStatusOrderByCreatedAtDesc(LoginLog.LoginStatus status, int limit, long offset);

    /**
     * 查询指定时间范围内的登录日志
     */
    @Query("SELECT * FROM login_logs WHERE created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<LoginLog> findLoginLogsInTimeRange(Instant startTime, Instant endTime);

    /**
     * 查询用户指定时间范围内的登录日志
     */
    @Query("SELECT * FROM login_logs WHERE user_id = :userId AND created_at BETWEEN :startTime AND :endTime ORDER BY created_at DESC")
    Flux<LoginLog> findUserLoginLogsInTimeRange(Long userId, Instant startTime, Instant endTime);

    /**
     * 查询失败的登录尝试
     */
    @Query("SELECT * FROM login_logs WHERE status != :successStatus ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<LoginLog> findFailedLoginAttempts(LoginLog.LoginStatus successStatus, int limit, long offset);

    /**
     * 查询用户失败的登录尝试
     */
    @Query("SELECT * FROM login_logs WHERE user_id = :userId AND status != :successStatus ORDER BY created_at DESC")
    Flux<LoginLog> findUserFailedLoginAttempts(Long userId, LoginLog.LoginStatus successStatus);

    /**
     * 查询可疑的登录日志
     */
    List<LoginLog> findByIsSuspiciousTrueOrderByCreatedAtDesc();

    /**
     * 查询用户可疑的登录日志
     */
    List<LoginLog> findByUserIdAndIsSuspiciousTrueOrderByCreatedAtDesc(Long userId);

    /**
     * 统计用户登录次数
     */
    @Query("SELECT COUNT(l) FROM LoginLog l WHERE l.userId = :userId AND l.status = :status")
    long countUserLogins(@Param("userId") Long userId, @Param("status") LoginLog.LoginStatus status);

    /**
     * 统计指定时间范围内的登录次数
     */
    @Query("SELECT COUNT(l) FROM LoginLog l WHERE l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status")
    long countLoginsInTimeRange(@Param("startTime") Instant startTime, 
                               @Param("endTime") Instant endTime, 
                               @Param("status") LoginLog.LoginStatus status);

    /**
     * 统计用户指定时间范围内的登录次数
     */
    @Query("SELECT COUNT(l) FROM LoginLog l WHERE l.userId = :userId AND l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status")
    long countUserLoginsInTimeRange(@Param("userId") Long userId, 
                                   @Param("startTime") Instant startTime, 
                                   @Param("endTime") Instant endTime, 
                                   @Param("status") LoginLog.LoginStatus status);

    /**
     * 统计IP地址的登录尝试次数
     */
    @Query("SELECT COUNT(l) FROM LoginLog l WHERE l.clientIp = :clientIp AND l.createdAt BETWEEN :startTime AND :endTime")
    long countLoginAttemptsByIp(@Param("clientIp") String clientIp, 
                               @Param("startTime") Instant startTime, 
                               @Param("endTime") Instant endTime);

    /**
     * 统计IP地址的失败登录次数
     */
    @Query("SELECT COUNT(l) FROM LoginLog l WHERE l.clientIp = :clientIp AND l.status != :successStatus AND l.createdAt BETWEEN :startTime AND :endTime")
    long countFailedLoginAttemptsByIp(@Param("clientIp") String clientIp, 
                                     @Param("successStatus") LoginLog.LoginStatus successStatus,
                                     @Param("startTime") Instant startTime, 
                                     @Param("endTime") Instant endTime);

    /**
     * 查询用户最近的成功登录
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId = :userId AND l.status = :successStatus ORDER BY l.createdAt DESC")
    List<LoginLog> findUserRecentSuccessfulLogins(@Param("userId") Long userId, 
                                                 @Param("successStatus") LoginLog.LoginStatus successStatus, 
                                                 Pageable pageable);

    /**
     * 查询用户最后一次成功登录
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId = :userId AND l.status = :successStatus ORDER BY l.createdAt DESC")
    List<LoginLog> findUserLastSuccessfulLoginList(@Param("userId") Long userId, 
                                                   @Param("successStatus") LoginLog.LoginStatus successStatus, 
                                                   Pageable pageable);
    
    /**
     * 查询用户最后一次成功登录（辅助方法）
     */
    default Optional<LoginLog> findUserLastSuccessfulLogin(Long userId, LoginLog.LoginStatus successStatus) {
        List<LoginLog> results = findUserLastSuccessfulLoginList(userId, successStatus, 
                                                                org.springframework.data.domain.PageRequest.of(0, 1));
        return results.isEmpty() ? Optional.empty() : Optional.of(results.get(0));
    }

    /**
     * 根据会话ID查询登录日志
     */
    Optional<LoginLog> findBySessionId(String sessionId);

    /**
     * 查询活跃会话（未登出的登录）
     */
    @Query("SELECT l FROM LoginLog l WHERE l.status = :successStatus AND l.logoutAt IS NULL ORDER BY l.createdAt DESC")
    List<LoginLog> findActiveLogins(@Param("successStatus") LoginLog.LoginStatus successStatus);

    /**
     * 查询用户活跃会话
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId = :userId AND l.status = :successStatus AND l.logoutAt IS NULL ORDER BY l.createdAt DESC")
    List<LoginLog> findUserActiveLogins(@Param("userId") Long userId, 
                                       @Param("successStatus") LoginLog.LoginStatus successStatus);

    /**
     * 查询设备活跃会话
     */
    @Query("SELECT l FROM LoginLog l WHERE l.deviceId = :deviceId AND l.status = :successStatus AND l.logoutAt IS NULL ORDER BY l.createdAt DESC")
    List<LoginLog> findDeviceActiveLogins(@Param("deviceId") String deviceId, 
                                         @Param("successStatus") LoginLog.LoginStatus successStatus);

    /**
     * 获取登录统计信息
     */
    @Query("SELECT l.status, COUNT(l) FROM LoginLog l GROUP BY l.status")
    List<Object[]> getLoginStatistics();

    /**
     * 获取登录类型统计信息
     */
    @Query("SELECT l.loginType, COUNT(l) FROM LoginLog l WHERE l.status = :status GROUP BY l.loginType")
    List<Object[]> getLoginTypeStatistics(@Param("status") LoginLog.LoginStatus status);

    /**
     * 获取地理位置登录统计
     */
    @Query("SELECT l.countryCode, COUNT(l) FROM LoginLog l WHERE l.countryCode IS NOT NULL AND l.status = :status GROUP BY l.countryCode ORDER BY COUNT(l) DESC")
    List<Object[]> getLocationStatistics(@Param("status") LoginLog.LoginStatus status);

    /**
     * 查询异常登录模式
     */
    @Query("SELECT l FROM LoginLog l WHERE l.riskScore > :riskThreshold ORDER BY l.createdAt DESC")
    List<LoginLog> findHighRiskLogins(@Param("riskThreshold") Integer riskThreshold);

    /**
     * 查询用户异常登录
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId = :userId AND l.riskScore > :riskThreshold ORDER BY l.createdAt DESC")
    List<LoginLog> findUserHighRiskLogins(@Param("userId") Long userId, @Param("riskThreshold") Integer riskThreshold);

    /**
     * 查询长时间会话
     */
    @Query("SELECT l FROM LoginLog l WHERE l.status = :successStatus AND l.logoutAt IS NULL AND l.createdAt < :threshold ORDER BY l.createdAt ASC")
    List<LoginLog> findLongRunningLogins(@Param("successStatus") LoginLog.LoginStatus successStatus, 
                                        @Param("threshold") Instant threshold);

    /**
     * 按小时统计登录数量
     */
    @Query("SELECT EXTRACT(HOUR FROM l.createdAt), COUNT(l) FROM LoginLog l WHERE l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status GROUP BY EXTRACT(HOUR FROM l.createdAt) ORDER BY EXTRACT(HOUR FROM l.createdAt)")
    List<Object[]> getHourlyLoginStatistics(@Param("startTime") Instant startTime, 
                                           @Param("endTime") Instant endTime, 
                                           @Param("status") LoginLog.LoginStatus status);

    /**
     * 按天统计登录数量
     */
    @Query("SELECT DATE(l.createdAt), COUNT(l) FROM LoginLog l WHERE l.createdAt BETWEEN :startTime AND :endTime AND l.status = :status GROUP BY DATE(l.createdAt) ORDER BY DATE(l.createdAt)")
    List<Object[]> getDailyLoginStatistics(@Param("startTime") Instant startTime, 
                                          @Param("endTime") Instant endTime, 
                                          @Param("status") LoginLog.LoginStatus status);

    /**
     * 查询首次登录用户
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId IN (SELECT l2.userId FROM LoginLog l2 WHERE l2.status = :successStatus GROUP BY l2.userId HAVING COUNT(l2) = 1) AND l.status = :successStatus ORDER BY l.createdAt DESC")
    List<LoginLog> findFirstTimeLogins(@Param("successStatus") LoginLog.LoginStatus successStatus);

    /**
     * 删除旧的登录日志
     */
    @Query("DELETE FROM LoginLog l WHERE l.createdAt < :cutoffDate")
    int deleteOldLoginLogs(@Param("cutoffDate") Instant cutoffDate);

    /**
     * 统计用户在指定设备上的成功登录次数
     */
    long countByUserIdAndDeviceIdAndStatus(Long userId, String deviceId, LoginLog.LoginStatus status);

    /**
     * 根据会话ID更新登出信息
     */
    @Query("UPDATE LoginLog l SET l.logoutAt = :logoutAt, l.logoutType = :logoutType WHERE l.sessionId = :sessionId")
    int updateLogoutInfoBySessionId(@Param("sessionId") String sessionId,
                                   @Param("logoutAt") Instant logoutAt,
                                   @Param("logoutType") LoginLog.LogoutType logoutType);

    /**
     * 根据用户ID和设备ID查找最近的登录日志
     */
    @Query("SELECT l FROM LoginLog l WHERE l.userId = :userId AND l.deviceId = :deviceId AND l.status = :status ORDER BY l.createdAt DESC")
    Optional<LoginLog> findRecentLoginByUserAndDevice(@Param("userId") Long userId,
                                                     @Param("deviceId") String deviceId,
                                                     @Param("status") LoginLog.LoginStatus status);
}
