package com.implatform.user.repository;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.PrivacySettings;

/**
 * 隐私设置Repository
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface PrivacySettingsRepository extends ReactiveCrudRepository<PrivacySettings, Long> {

    /**
     * 根据用户ID查找隐私设置
     */
    Mono<PrivacySettings> findByUserId(Long userId);

    /**
     * 检查用户是否存在隐私设置
     */
    Mono<Boolean> existsByUserId(Long userId);

    /**
     * 根据用户ID删除隐私设置
     */
    Mono<Void> deleteByUserId(Long userId);

    /**
     * 查找允许所有人查看手机号的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE phone_visibility = 'EVERYBODY'")
    Flux<PrivacySettings> findUsersWithPublicPhone();

    /**
     * 查找允许所有人查看最后在线时间的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE last_seen_visibility = 'EVERYBODY'")
    Flux<PrivacySettings> findUsersWithPublicLastSeen();

    /**
     * 查找允许所有人发送消息的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE message_permission = 'EVERYBODY'")
    Flux<PrivacySettings> findUsersWithPublicMessaging();

    /**
     * 查找显示在线状态的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE show_online_status = true")
    Flux<PrivacySettings> findUsersShowingOnlineStatus();

    /**
     * 查找允许语音通话的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE allow_voice_calls = true")
    Flux<PrivacySettings> findUsersAllowingVoiceCalls();

    /**
     * 查找允许视频通话的用户
     */
    @Query("SELECT * FROM privacy_settings WHERE allow_video_calls = true")
    Flux<PrivacySettings> findUsersAllowingVideoCalls();

    /**
     * 批量查询多个用户的隐私设置
     */
    @Query("SELECT * FROM privacy_settings WHERE user_id = ANY(:userIds)")
    Flux<PrivacySettings> findByUserIdIn(Long[] userIds);

    /**
     * 统计各种隐私设置的使用情况
     */
    @Query("SELECT phone_visibility, COUNT(*) FROM privacy_settings GROUP BY phone_visibility")
    Flux<Object[]> countByPhoneVisibility();

    @Query("SELECT message_permission, COUNT(*) FROM privacy_settings GROUP BY message_permission")
    Flux<Object[]> countByMessagePermission();
}
