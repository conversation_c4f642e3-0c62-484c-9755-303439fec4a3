package com.implatform.admin.repository;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.admin.entity.Role;

/**
 * 角色数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 */
@Repository
public interface RoleRepository extends ReactiveCrudRepository<Role, String> {

    /**
     * 根据角色编码查找角色
     */
    Mono<Role> findByCode(String code);

    /**
     * 检查角色编码是否存在
     */
    Mono<Boolean> existsByCode(String code);

    /**
     * 检查角色编码是否存在（排除指定角色）
     */
    @Query("SELECT COUNT(*) > 0 FROM roles WHERE code = :code AND id != :id")
    Mono<Boolean> existsByCodeAndIdNot(String code, String id);

    /**
     * 根据角色名称查找角色
     */
    Mono<Role> findByName(String name);

    /**
     * 检查角色名称是否存在
     */
    Mono<Boolean> existsByName(String name);

    /**
     * 检查角色名称是否存在（排除指定角色）
     */
    @Query("SELECT COUNT(*) > 0 FROM roles WHERE name = :name AND id != :id")
    Mono<Boolean> existsByNameAndIdNot(String name, String id);

    /**
     * 根据状态查找角色
     */
    Flux<Role> findByStatus(Role.RoleStatus status);

    /**
     * 根据状态分页查找角色
     */
    @Query("SELECT * FROM roles WHERE status = :status ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<Role> findByStatusOrderBySortOrderAsc(Role.RoleStatus status, int limit, long offset);

    /**
     * 查找非系统内置角色
     */
    Flux<Role> findByIsSystemFalse();

    /**
     * 查找系统内置角色
     */
    Flux<Role> findByIsSystemTrue();

    /**
     * 根据用户ID查找角色
     */
    @Query("SELECT r.* FROM roles r " +
           "JOIN admin_user_roles ur ON r.id = ur.role_id " +
           "WHERE ur.admin_user_id = :userId")
    Flux<Role> findByUserId(String userId);

    /**
     * 根据权限编码查找角色
     */
    @Query("SELECT DISTINCT r.* FROM roles r " +
           "JOIN role_permissions rp ON r.id = rp.role_id " +
           "JOIN permissions p ON rp.permission_id = p.id " +
           "WHERE p.code = :permissionCode")
    Flux<Role> findByPermissionCode(String permissionCode);

    /**
     * 根据权限编码列表查找角色
     */
    @Query("SELECT DISTINCT r.* FROM roles r " +
           "JOIN role_permissions rp ON r.id = rp.role_id " +
           "JOIN permissions p ON rp.permission_id = p.id " +
           "WHERE p.code = ANY(:permissionCodes)")
    Flux<Role> findByPermissionCodes(String[] permissionCodes);

    /**
     * 搜索角色（名称、编码、描述模糊匹配）
     */
    @Query("SELECT * FROM roles WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "LOWER(name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(code) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<Role> searchRoles(String keyword, int limit, long offset);

    /**
     * 根据排序号查找角色
     */
    Flux<Role> findAllByOrderBySortOrderAsc();

    /**
     * 统计角色总数
     */
    @Query("SELECT COUNT(*) FROM roles")
    Mono<Long> countTotalRoles();

    /**
     * 统计活跃角色数
     */
    @Query("SELECT COUNT(*) FROM roles WHERE status = 'ACTIVE'")
    Mono<Long> countActiveRoles();

    /**
     * 统计非系统角色数
     */
    @Query("SELECT COUNT(*) FROM roles WHERE is_system = false")
    Mono<Long> countNonSystemRoles();

    /**
     * 查找拥有用户最多的角色
     */
    @Query("SELECT r.* FROM roles r " +
           "LEFT JOIN admin_user_roles ur ON r.id = ur.role_id " +
           "GROUP BY r.id ORDER BY COUNT(ur.admin_user_id) DESC " +
           "LIMIT :limit OFFSET :offset")
    Flux<Role> findRolesOrderByUserCountDesc(int limit, long offset);

    /**
     * 批量更新角色状态
     */
    @Modifying
    @Query("UPDATE roles SET status = :status, updated_by = :updatedBy " +
           "WHERE id = ANY(:roleIds)")
    Mono<Integer> updateStatusByIds(String[] roleIds, Role.RoleStatus status, String updatedBy);

    /**
     * 删除角色的所有权限关联
     */
    @Modifying
    @Query("DELETE FROM role_permissions WHERE role_id = :roleId")
    Mono<Integer> deleteRolePermissions(String roleId);

    /**
     * 检查角色是否被用户使用
     */
    @Query("SELECT COUNT(*) > 0 FROM admin_user_roles WHERE role_id = :roleId")
    Mono<Boolean> isRoleInUse(String roleId);

    /**
     * 获取角色的用户数量
     */
    @Query("SELECT COUNT(*) FROM admin_user_roles WHERE role_id = :roleId")
    Mono<Long> countUsersByRoleId(String roleId);

    /**
     * 获取角色的权限数量
     */
    @Query("SELECT COUNT(*) FROM role_permissions WHERE role_id = :roleId")
    Mono<Long> countPermissionsByRoleId(String roleId);

    /**
     * 根据权限ID查找拥有该权限的角色
     */
    @Query("SELECT r.* FROM roles r " +
           "JOIN role_permissions rp ON r.id = rp.role_id " +
           "WHERE rp.permission_id = :permissionId")
    Flux<Role> findByPermissions_Id(String permissionId);
}