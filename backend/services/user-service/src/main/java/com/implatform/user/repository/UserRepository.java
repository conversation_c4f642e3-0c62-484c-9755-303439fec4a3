package com.implatform.user.repository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.User;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 用户数据访问层 - IM平台用户数据库操作接口
 *
 * <p><strong>Repository概述</strong>：
 * 本接口提供完整的用户数据访问功能，负责用户实体的CRUD操作、身份验证查询、用户搜索、
 * 统计分析等数据库交互。采用Spring Data JPA技术，提供高性能、高安全性的用户数据访问能力。
 *
 * <p><strong>核心数据操作</strong>：
 * <ul>
 *   <li><strong>身份验证</strong>：用户名、邮箱、手机号的唯一性验证和登录查询</li>
 *   <li><strong>用户管理</strong>：用户信息的创建、更新、状态管理</li>
 *   <li><strong>搜索功能</strong>：用户搜索、好友推荐、模糊匹配</li>
 *   <li><strong>统计分析</strong>：用户数量统计、活跃度分析、注册趋势</li>
 *   <li><strong>状态管理</strong>：在线状态、用户状态、权限状态查询</li>
 * </ul>
 *
 * <p><strong>数据库表结构</strong>：
 * 对应数据库表：users
 * <ul>
 *   <li><strong>主键</strong>：id (BIGINT, AUTO_INCREMENT)</li>
 *   <li><strong>唯一索引</strong>：username, email, phone (支持NULL)</li>
 *   <li><strong>普通索引</strong>：status, online_status, created_at</li>
 *   <li><strong>复合索引</strong>：(status, created_at), (online_status, last_active_at)</li>
 *   <li><strong>全文索引</strong>：nickname, signature (用于用户搜索)</li>
 * </ul>
 *
 * <p><strong>安全性设计</strong>：
 * <ul>
 *   <li><strong>敏感数据保护</strong>：密码字段不在查询结果中返回</li>
 *   <li><strong>唯一性约束</strong>：用户名、邮箱、手机号的数据库级唯一约束</li>
 *   <li><strong>软删除机制</strong>：使用状态标记实现用户软删除</li>
 *   <li><strong>数据脱敏</strong>：查询结果中敏感信息的自动脱敏</li>
 * </ul>
 *
 * <p><strong>查询性能优化</strong>：
 * <ul>
 *   <li><strong>索引策略</strong>：基于查询模式优化的索引设计</li>
 *   <li><strong>查询缓存</strong>：用户基本信息的Redis缓存</li>
 *   <li><strong>分页优化</strong>：大数据量查询的分页和排序优化</li>
 *   <li><strong>连接优化</strong>：减少不必要的表连接操作</li>
 * </ul>
 *
 * <p><strong>数据一致性保证</strong>：
 * <ul>
 *   <li><strong>事务支持</strong>：用户注册、状态变更等关键操作的事务保护</li>
 *   <li><strong>约束检查</strong>：数据库约束确保数据完整性</li>
 *   <li><strong>并发控制</strong>：乐观锁防止并发更新冲突</li>
 *   <li><strong>审计日志</strong>：用户数据变更的完整审计追踪</li>
 * </ul>
 *
 * <p><strong>特殊查询功能</strong>：
 * <ul>
 *   <li><strong>多字段登录</strong>：支持用户名、邮箱、手机号任一方式登录</li>
 *   <li><strong>模糊搜索</strong>：用户昵称、用户名的模糊匹配搜索</li>
 *   <li><strong>状态筛选</strong>：基于用户状态、在线状态的条件查询</li>
 *   <li><strong>时间范围</strong>：注册时间、活跃时间的范围查询</li>
 * </ul>
 *
 * <p><strong>缓存策略</strong>：
 * <ul>
 *   <li><strong>用户信息缓存</strong>：基本用户信息的Redis缓存</li>
 *   <li><strong>登录缓存</strong>：登录验证结果的短期缓存</li>
 *   <li><strong>搜索缓存</strong>：热门搜索结果的缓存</li>
 *   <li><strong>统计缓存</strong>：用户统计数据的定期更新缓存</li>
 * </ul>
 *
 * <p><strong>监控和维护</strong>：
 * <ul>
 *   <li><strong>查询监控</strong>：慢查询监控和性能分析</li>
 *   <li><strong>数据质量</strong>：数据完整性和一致性检查</li>
 *   <li><strong>容量规划</strong>：用户增长趋势和存储容量规划</li>
 *   <li><strong>安全审计</strong>：用户数据访问的安全审计</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 * @version 1.0.0
 */
@Repository
public interface UserRepository extends ReactiveCrudRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Mono<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Mono<User> findByEmail(String email);

    /**
     * 根据手机号查找用户
     */
    Mono<User> findByPhone(String phone);
    
    /**
     * 根据用户名、邮箱、手机号或系统账号查找用户（用于登录验证）
     */
    @Query("SELECT * FROM users WHERE username = :identifier OR email = :identifier OR phone = :identifier OR system_account = :identifier")
    Mono<User> findByUsernameOrEmailOrPhoneOrSystemAccount(String identifier);

    /**
     * 根据用户名、邮箱或手机号查找用户（用于登录验证）
     *
     * <p><strong>查询功能</strong>：
     * 支持用户使用用户名、邮箱或手机号中的任意一种方式进行登录验证。
     * 这是登录系统的核心查询方法，提供灵活的身份标识符匹配。
     *
     * <p><strong>JPQL查询</strong>：
     * <pre>{@code
     * SELECT u FROM User u
     * WHERE u.username = :identifier
     *    OR u.email = :identifier
     *    OR u.phone = :identifier
     * }</pre>
     *
     * <p><strong>数据库执行计划</strong>：
     * 由于使用OR条件，数据库会使用以下索引策略：
     * <ul>
     *   <li>username唯一索引：快速精确匹配用户名</li>
     *   <li>email唯一索引：快速精确匹配邮箱</li>
     *   <li>phone唯一索引：快速精确匹配手机号</li>
     *   <li>索引合并：数据库优化器自动选择最优索引</li>
     * </ul>
     *
     * <p><strong>性能特征</strong>：
     * <ul>
     *   <li><strong>查询复杂度</strong>：O(log n)，基于唯一索引的快速查找</li>
     *   <li><strong>缓存友好</strong>：查询结果适合Redis缓存</li>
     *   <li><strong>并发安全</strong>：只读查询，支持高并发登录</li>
     *   <li><strong>索引覆盖</strong>：所有查询字段都有对应索引</li>
     * </ul>
     *
     * <p><strong>安全考虑</strong>：
     * <ul>
     *   <li><strong>大小写敏感</strong>：精确匹配，区分大小写</li>
     *   <li><strong>空值处理</strong>：自动过滤NULL值，避免误匹配</li>
     *   <li><strong>SQL注入防护</strong>：参数化查询防止SQL注入</li>
     *   <li><strong>敏感信息保护</strong>：不返回密码等敏感字段</li>
     * </ul>
     *
     * <p><strong>使用场景</strong>：
     * <ul>
     *   <li>用户登录身份验证</li>
     *   <li>密码重置用户查找</li>
     *   <li>账号存在性验证</li>
     *   <li>多方式用户查询</li>
     * </ul>
     *
     * @param identifier 身份标识符，可以是用户名、邮箱或手机号
     * @return Optional&lt;User&gt; 匹配的用户对象，如果未找到则返回空Optional
     *
     * @implNote
     * 建议在调用前对identifier进行格式验证和清理。
     * 查询结果应该缓存到Redis中，减少数据库访问频率。
     *
     * @since 1.0.0
     */
    @Query("SELECT * FROM users WHERE username = :identifier OR email = :identifier OR phone = :identifier")
    Mono<User> findByUsernameOrEmailOrPhone(String identifier);

    /**
     * 检查用户名是否存在
     */
    Mono<Boolean> existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    Mono<Boolean> existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    Mono<Boolean> existsByPhone(String phone);

    /**
     * 根据系统账号查找用户
     */
    Mono<User> findBySystemAccount(String systemAccount);

    /**
     * 检查系统账号是否存在
     */
    Mono<Boolean> existsBySystemAccount(String systemAccount);

    /**
     * 根据系统账号查询用户ID
     */
    @Query("SELECT id FROM users WHERE system_account = :systemAccount")
    Mono<Long> findUserIdBySystemAccount(String systemAccount);

    /**
     * 根据用户ID查询系统账号
     */
    @Query("SELECT system_account FROM users WHERE id = :userId")
    Mono<String> findSystemAccountByUserId(Long userId);

    /**
     * 更新用户的系统账号
     */
    @Query("UPDATE users SET system_account = :systemAccount WHERE id = :userId")
    Mono<Integer> updateSystemAccountByUserId(Long userId, String systemAccount);

    /**
     * 统计已分配系统账号的用户数量
     */
    Mono<Long> countBySystemAccountIsNotNull();

    /**
     * 根据状态查找用户
     */
    Flux<User> findByStatus(User.UserStatus status);

    /**
     * 根据在线状态查找用户
     */
    Flux<User> findByOnlineStatus(User.OnlineStatus onlineStatus);

    /**
     * 分页查询用户 - 需要自定义实现分页
     */
    @Query("SELECT * FROM users WHERE status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<User> findByStatusOrderByCreatedAtDesc(User.UserStatus status, int limit, int offset);

    /**
     * 根据昵称模糊查询用户 - 需要自定义实现分页
     */
    @Query("SELECT * FROM users WHERE nickname LIKE :nickname AND status = :status LIMIT :limit OFFSET :offset")
    Flux<User> findByNicknameLikeAndStatus(String nickname, User.UserStatus status, int limit, int offset);
    
    /**
     * 更新用户在线状态
     */
    @Modifying
    @Query("UPDATE User u SET u.onlineStatus = :onlineStatus WHERE u.id = :userId")
    int updateOnlineStatus(@Param("userId") Long userId, @Param("onlineStatus") User.OnlineStatus onlineStatus);
    
    /**
     * 更新用户最后登录信息
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :lastLoginAt, u.lastLoginIp = :lastLoginIp, u.loginAttempts = 0 WHERE u.id = :userId")
    int updateLastLoginInfo(@Param("userId") Long userId,
                           @Param("lastLoginAt") LocalDateTime lastLoginAt,
                           @Param("lastLoginIp") String lastLoginIp);

    /**
     * 增加登录尝试次数
     */
    @Modifying
    @Query("UPDATE User u SET u.loginAttempts = u.loginAttempts + 1 WHERE u.id = :userId")
    int incrementLoginAttempts(@Param("userId") Long userId);

    /**
     * 锁定用户账号
     */
    @Modifying
    @Query("UPDATE User u SET u.lockedUntil = :lockedUntil, u.status = 'LOCKED' WHERE u.id = :userId")
    int lockUserAccount(@Param("userId") Long userId, @Param("lockedUntil") LocalDateTime lockedUntil);

    /**
     * 解锁用户账号
     */
    @Modifying
    @Query("UPDATE User u SET u.lockedUntil = NULL, u.loginAttempts = 0, u.status = 'ACTIVE' WHERE u.id = :userId AND u.status = 'LOCKED'")
    int unlockUserAccount(@Param("userId") Long userId);

    /**
     * 更新用户密码
     */
    @Modifying
    @Query("UPDATE User u SET u.password = :passwordHash, u.passwordSalt = :passwordSalt, u.lastPasswordChange = :changeTime WHERE u.id = :userId")
    int updateUserPassword(@Param("userId") Long userId,
                          @Param("passwordHash") String passwordHash,
                          @Param("passwordSalt") String passwordSalt,
                          @Param("changeTime") LocalDateTime changeTime);

    /**
     * 验证用户邮箱
     */
    @Modifying
    @Query("UPDATE User u SET u.isEmailVerified = true WHERE u.id = :userId")
    int verifyUserEmail(@Param("userId") Long userId);

    /**
     * 验证用户手机号
     */
    @Modifying
    @Query("UPDATE User u SET u.isPhoneVerified = true WHERE u.id = :userId")
    int verifyUserPhone(@Param("userId") Long userId);
    
    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = :status")
    long countByStatus(@Param("status") User.UserStatus status);
    
    /**
     * 统计在线用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.onlineStatus IN :onlineStatuses")
    long countByOnlineStatusIn(@Param("onlineStatuses") List<User.OnlineStatus> onlineStatuses);
    
    /**
     * 查找最近注册的用户
     */
    @Query("SELECT u FROM User u WHERE u.status = :status ORDER BY u.createdAt DESC")
    List<User> findRecentUsers(@Param("status") User.UserStatus status, Pageable pageable);

    /**
     * 根据用户名模糊查询（忽略大小写）
     */
    Page<User> findByUsernameContainingIgnoreCase(String username, Pageable pageable);

    /**
     * 根据用户名模糊查询和状态筛选（忽略大小写）
     */
    Page<User> findByUsernameContainingIgnoreCaseAndStatus(String username, User.UserStatus status, Pageable pageable);

    /**
     * 根据状态分页查询
     */
    Page<User> findByStatus(User.UserStatus status, Pageable pageable);

    /**
     * 统计指定时间之后创建的用户数量
     */
    long countByCreatedAtAfter(java.time.Instant createdAt);

    /**
     * 统计邮箱验证状态的用户数量
     */
    long countByIsEmailVerified(Boolean isEmailVerified);

    /**
     * 更新用户ID（用于系统用户初始化）
     */
    @Modifying
    @Query("UPDATE User u SET u.id = :newId WHERE u.id = :oldId")
    void updateUserId(@Param("oldId") Long oldId, @Param("newId") Long newId);

    /**
     * 插入系统用户（如果不存在）
     * 使用原生SQL处理并发插入
     */
    @Modifying
    @Query(value = """
        INSERT INTO users (id, username, nickname, user_type, status, online_status,
                          is_email_verified, is_phone_verified, security_level,
                          language, timezone, experience_points, user_level,
                          login_attempts, two_factor_enabled, verification_badge,
                          last_seen_at, created_at, updated_at)
        SELECT :id, :username, :nickname, 'SYSTEM', 'ACTIVE', 'ONLINE',
               true, true, 3, 'zh-CN', 'Asia/Shanghai', 0, 1,
               0, false, false,
               :lastSeenAt, :lastSeenAt, :lastSeenAt
        WHERE NOT EXISTS (
            SELECT 1 FROM users WHERE id = :id OR username = :username
        )
        """, nativeQuery = true)
    int insertSystemUserIfNotExists(@Param("id") Long id,
                                   @Param("username") String username,
                                   @Param("nickname") String nickname,
                                   @Param("lastSeenAt") Instant lastSeenAt);

    /**
     * 统计手机验证状态的用户数量
     */
    long countByIsPhoneVerified(Boolean isPhoneVerified);

    /**
     * 根据昵称模糊查询（忽略大小写）
     */
    Page<User> findByNicknameContainingIgnoreCase(String nickname, Pageable pageable);

    /**
     * 根据昵称精确查找用户
     */
    Optional<User> findByNickname(String nickname);

    /**
     * 根据邮箱模糊查询（忽略大小写）
     */
    Page<User> findByEmailContainingIgnoreCase(String email, Pageable pageable);

    /**
     * 根据手机号模糊查询
     */
    Page<User> findByPhoneContaining(String phone, Pageable pageable);

    /**
     * 搜索活跃用户（最近指定时间内登录）
     */
    @Query("SELECT u FROM User u WHERE (u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
           "AND u.lastLoginAt > :since AND u.status = 'ACTIVE'")
    Page<User> findActiveUsersByKeyword(@Param("keyword") String keyword,
                                       @Param("since") LocalDateTime since,
                                       Pageable pageable);

    /**
     * 搜索非活跃用户（指定时间之前登录或从未登录）
     */
    @Query("SELECT u FROM User u WHERE (u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
           "AND (u.lastLoginAt IS NULL OR u.lastLoginAt <= :before) AND u.status = 'ACTIVE'")
    Page<User> findInactiveUsersByKeyword(@Param("keyword") String keyword,
                                         @Param("before") LocalDateTime before,
                                         Pageable pageable);

    /**
     * 搜索已验证用户（邮箱或手机已验证）
     */
    @Query("SELECT u FROM User u WHERE (u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
           "AND (u.isEmailVerified = true OR u.isPhoneVerified = true) AND u.status = 'ACTIVE'")
    Page<User> findVerifiedUsersByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 搜索未验证用户（邮箱和手机都未验证）
     */
    @Query("SELECT u FROM User u WHERE (u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
           "AND u.isEmailVerified = false AND u.isPhoneVerified = false AND u.status = 'ACTIVE'")
    Page<User> findUnverifiedUsersByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 多字段关键词搜索（用户名、昵称、邮箱）
     */
    @Query("SELECT u FROM User u WHERE (u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR u.email LIKE %:keyword%) " +
           "AND u.status = 'ACTIVE'")
    Page<User> findByKeywordInMultipleFields(@Param("keyword") String keyword, Pageable pageable);

    // ==================== 账户锁定相关查询方法 ====================

    /**
     * 查找锁定时间已过期的用户
     */
    @Query("SELECT u FROM User u WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil < :currentTime")
    List<User> findExpiredLockedUsers(@Param("currentTime") Instant currentTime);

    /**
     * 统计当前锁定的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.lockedUntil IS NOT NULL AND u.lockedUntil > :currentTime")
    long countCurrentLockedUsers(@Param("currentTime") Instant currentTime);

    /**
     * 统计指定时间后解锁的用户数量（通过最后活跃时间判断）
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.lockedUntil IS NULL AND u.lastActiveAt > :sinceTime")
    long countUnlockedUsersSince(@Param("sinceTime") Instant sinceTime);

    /**
     * 统计登录失败次数较高的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.loginAttempts >= :threshold")
    long countUsersWithHighFailureAttempts(@Param("threshold") int threshold);

    /**
     * 重置过期的登录失败次数
     * 对于超过指定时间没有活动的用户，重置其登录失败次数
     */
    @Modifying
    @Query("UPDATE User u SET u.loginAttempts = 0 WHERE u.loginAttempts > 0 AND (u.lastActiveAt IS NULL OR u.lastActiveAt < :cutoffTime)")
    int resetExpiredLoginAttempts(@Param("cutoffTime") Instant cutoffTime);

    /**
     * 查找需要重置登录失败次数的用户
     */
    @Query("SELECT u FROM User u WHERE u.loginAttempts > 0 AND (u.lastActiveAt IS NULL OR u.lastActiveAt < :cutoffTime)")
    List<User> findUsersWithExpiredLoginAttempts(@Param("cutoffTime") Instant cutoffTime);
}