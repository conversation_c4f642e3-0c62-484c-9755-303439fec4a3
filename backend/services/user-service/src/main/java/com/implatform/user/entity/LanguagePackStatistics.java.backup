package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.time.LocalDate;

/**
 * 语言包使用统计实体类
 * 
 * <p>该实体类用于记录语言包的使用统计数据，包括下载次数、活跃用户数、API请求次数等。
 * 按日期进行统计，支持趋势分析和使用情况监控。</p>
 * 
 * <p><strong>主要功能</strong>：
 * <ul>
 *   <li>语言包使用统计</li>
 *   <li>按日期统计数据</li>
 *   <li>下载次数统计</li>
 *   <li>活跃用户统计</li>
 *   <li>API请求统计</li>
 * </ul>
 * 
 * <p><strong>数据库映射</strong>：
 * 映射到 language_pack_statistics 表，包含语言包ID和日期的复合唯一索引。
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "language_pack_statistics", 
       indexes = {
           @Index(name = "idx_language_pack_statistics_language_pack_id", columnList = "language_pack_id"),
           @Index(name = "idx_language_pack_statistics_date", columnList = "date")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "unique_stat", columnNames = {"language_pack_id", "date"})
       })
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class LanguagePackStatistics {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的语言包
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "language_pack_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_language_pack_statistics_language_pack_id"))
    private LanguagePack languagePack;
    
    /**
     * 统计日期
     */
    @Column(name = "date", nullable = false)
    private LocalDate date;
    
    /**
     * 下载次数
     * 记录该语言包在当日的下载次数
     */
    @Builder.Default
    @Min(value = 0, message = "下载次数不能小于0")
    @Column(name = "download_count", nullable = false)
    private Integer downloadCount = 0;
    
    /**
     * 活跃用户数
     * 记录该语言包在当日的活跃用户数
     */
    @Builder.Default
    @Min(value = 0, message = "活跃用户数不能小于0")
    @Column(name = "active_users", nullable = false)
    private Integer activeUsers = 0;
    
    /**
     * API请求次数
     * 记录该语言包相关的API请求次数
     */
    @Builder.Default
    @Min(value = 0, message = "API请求次数不能小于0")
    @Column(name = "api_requests", nullable = false)
    private Integer apiRequests = 0;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(nullable = false, updatable = false, name = "created_at")
    private Instant createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(nullable = false, name = "updated_at")
    private Instant updatedAt;
    
    /**
     * 增加下载次数
     * 
     * @param count 增加的次数
     */
    public void addDownloadCount(int count) {
        this.downloadCount += Math.max(0, count);
    }
    
    /**
     * 增加活跃用户数
     * 
     * @param count 增加的用户数
     */
    public void addActiveUsers(int count) {
        this.activeUsers += Math.max(0, count);
    }
    
    /**
     * 增加API请求次数
     * 
     * @param count 增加的请求次数
     */
    public void addApiRequests(int count) {
        this.apiRequests += Math.max(0, count);
    }
    
    /**
     * 重置所有统计数据
     */
    public void resetStatistics() {
        this.downloadCount = 0;
        this.activeUsers = 0;
        this.apiRequests = 0;
    }
    
    /**
     * 获取总活动数
     * 
     * @return 下载次数 + API请求次数
     */
    public int getTotalActivity() {
        return downloadCount + apiRequests;
    }
    
    /**
     * 检查是否有活动
     * 
     * @return true如果有任何统计数据大于0
     */
    public boolean hasActivity() {
        return downloadCount > 0 || activeUsers > 0 || apiRequests > 0;
    }
    
    /**
     * 获取平均每用户API请求数
     * 
     * @return 平均每用户API请求数，如果没有活跃用户则返回0
     */
    public double getAverageApiRequestsPerUser() {
        return activeUsers > 0 ? (double) apiRequests / activeUsers : 0.0;
    }
    
    /**
     * 创建今日统计记录
     * 
     * @param languagePack 语言包
     * @return 今日统计记录
     */
    public static LanguagePackStatistics createTodayStatistics(LanguagePack languagePack) {
        return LanguagePackStatistics.builder()
            .languagePack(languagePack)
            .date(LocalDate.now())
            .downloadCount(0)
            .activeUsers(0)
            .apiRequests(0)
            .build();
    }
    
    /**
     * 创建指定日期的统计记录
     * 
     * @param languagePack 语言包
     * @param date 统计日期
     * @return 统计记录
     */
    public static LanguagePackStatistics createStatistics(LanguagePack languagePack, LocalDate date) {
        return LanguagePackStatistics.builder()
            .languagePack(languagePack)
            .date(date)
            .downloadCount(0)
            .activeUsers(0)
            .apiRequests(0)
            .build();
    }
    
    /**
     * 合并统计数据
     * 
     * @param other 其他统计数据
     */
    public void mergeStatistics(LanguagePackStatistics other) {
        if (other != null) {
            this.downloadCount += other.downloadCount;
            this.activeUsers = Math.max(this.activeUsers, other.activeUsers); // 活跃用户数取最大值
            this.apiRequests += other.apiRequests;
        }
    }
    
    /**
     * 获取统计摘要信息
     * 
     * @return 统计摘要字符串
     */
    public String getSummary() {
        return String.format("日期: %s, 下载: %d, 活跃用户: %d, API请求: %d", 
            date, downloadCount, activeUsers, apiRequests);
    }
    
    /**
     * 检查是否为今日统计
     * 
     * @return true如果是今日统计
     */
    public boolean isToday() {
        return LocalDate.now().equals(date);
    }
    
    /**
     * 检查是否为本周统计
     * 
     * @return true如果是本周统计
     */
    public boolean isThisWeek() {
        LocalDate now = LocalDate.now();
        LocalDate weekStart = now.minusDays(now.getDayOfWeek().getValue() - 1);
        return !date.isBefore(weekStart) && !date.isAfter(now);
    }
    
    /**
     * 检查是否为本月统计
     * 
     * @return true如果是本月统计
     */
    public boolean isThisMonth() {
        LocalDate now = LocalDate.now();
        return date.getYear() == now.getYear() && date.getMonth() == now.getMonth();
    }
}
