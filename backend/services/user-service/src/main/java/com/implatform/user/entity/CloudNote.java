package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;
import java.util.List;

/**
 * 云笔记实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("cloud_notes"),
    @Index(name = "idx_cloud_note_category", columnList = "category"),
    @Index(name = "idx_cloud_note_status", columnList = "status"),
    @Index(name = "idx_cloud_note_created_at", columnList = "created_at")
})
public class CloudNote {

    /**
     * 笔记ID
     */
    @Id
        private Long id;

    /**
     * 笔记唯一标识
     */
    @Column("note_id")
    private String noteId;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 笔记标题
     */
    @Column("title")
    private String title;

    /**
     * 笔记内容
     */
    @Column("content")
    private String content;

    /**
     * 笔记摘要
     */
    @Column("summary")
    private String summary;

    /**
     * 分类
     */
    @Column("category")
    private String category;

    /**
     * 标签列表（JSON格式）
     */
    @Column("tags")
    private List<String> tags;

    /**
     * 笔记状态
     */
        @Column("status")
    private NoteStatus status = NoteStatus.DRAFT;

    /**
     * 是否公开
     */
    @Column("is_public")
    private Boolean isPublic = false;

    /**
     * 是否收藏
     */
    @Column("is_favorite")
    private Boolean isFavorite = false;

    /**
     * 是否置顶
     */
    @Column("is_pinned")
    private Boolean isPinned = false;

    /**
     * 阅读次数
     */
    @Column("view_count")
    private Long viewCount = 0L;

    /**
     * 点赞次数
     */
    @Column("like_count")
    private Long likeCount = 0L;

    /**
     * 分享次数
     */
    @Column("share_count")
    private Long shareCount = 0L;

    /**
     * 字数统计
     */
    @Column("word_count")
    private Integer wordCount = 0;

    /**
     * 预计阅读时间（分钟）
     */
    @Column("reading_time")
    private Integer readingTime = 0;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 发布时间
     */
    @Column("published_at")
    private Instant publishedAt;

    /**
     * 删除时间（软删除）
     */
    @Column("deleted_at")
    private Instant deletedAt;

    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 是否加密
     */
    @Column("is_encrypted")
    private Boolean isEncrypted = false;

    /**
     * 版本号
     */
    @Column("version")
    private Integer version = 1;

    /**
     * 最后查看时间
     */
    @Column("last_viewed_at")
    private Instant lastViewedAt;

    /**
     * 笔记状态枚举
     */
    public enum NoteStatus {
        DRAFT,      // 草稿
        PUBLISHED,  // 已发布
        ARCHIVED,   // 已归档
        DELETED     // 已删除
    }

    /**
     * 检查笔记是否已删除
     */
    public boolean isDeleted() {
        return status == NoteStatus.DELETED || deletedAt != null;
    }

    /**
     * 检查笔记是否已发布
     */
    public boolean isPublished() {
        return status == NoteStatus.PUBLISHED && publishedAt != null;
    }

    /**
     * 软删除笔记
     */
    public void softDelete() {
        this.status = NoteStatus.DELETED;
        this.deletedAt = Instant.now();
    }

    /**
     * 发布笔记
     */
    public void publish() {
        this.status = NoteStatus.PUBLISHED;
        this.publishedAt = Instant.now();
    }

    /**
     * 归档笔记
     */
    public void archive() {
        this.status = NoteStatus.ARCHIVED;
    }

    /**
     * 增加阅读次数
     */
    public void incrementViewCount() {
        this.viewCount++;
    }

    /**
     * 增加点赞次数
     */
    public void incrementLikeCount() {
        this.likeCount++;
    }

    /**
     * 减少点赞次数
     */
    public void decrementLikeCount() {
        if (this.likeCount > 0) {
            this.likeCount--;
        }
    }

    /**
     * 增加分享次数
     */
    public void incrementShareCount() {
        this.shareCount++;
    }

    /**
     * 获取是否激活
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * 设置是否激活
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * 获取是否加密
     */
    public Boolean getIsEncrypted() {
        return isEncrypted;
    }

    /**
     * 设置是否加密
     */
    public void setIsEncrypted(Boolean isEncrypted) {
        this.isEncrypted = isEncrypted;
    }

    /**
     * 获取版本号
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * 设置版本号
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 获取最后查看时间
     */
    public Instant getLastViewedAt() {
        return lastViewedAt;
    }

    /**
     * 设置最后查看时间
     */
    public void setLastViewedAt(Instant lastViewedAt) {
        this.lastViewedAt = lastViewedAt;
    }

    /**
     * 创建Builder
     */
    public static CloudNoteBuilder builder() {
        return new CloudNoteBuilder();
    }

    /**
     * Builder类
     */
    public static class CloudNoteBuilder {
        private CloudNote cloudNote = new CloudNote();

        public CloudNoteBuilder userId(Long userId) {
            cloudNote.userId = userId;
            return this;
        }

        public CloudNoteBuilder title(String title) {
            cloudNote.title = title;
            return this;
        }

        public CloudNoteBuilder content(String content) {
            cloudNote.content = content;
            return this;
        }

        public CloudNoteBuilder category(String category) {
            cloudNote.category = category;
            return this;
        }

        public CloudNoteBuilder tags(java.util.List<String> tags) {
            cloudNote.tags = tags;
            return this;
        }

        public CloudNoteBuilder status(NoteStatus status) {
            cloudNote.status = status;
            return this;
        }

        public CloudNoteBuilder isPublic(Boolean isPublic) {
            cloudNote.isPublic = isPublic;
            return this;
        }

        public CloudNoteBuilder isActive(Boolean isActive) {
            cloudNote.isActive = isActive;
            return this;
        }

        public CloudNote build() {
            return cloudNote;
        }
    }
}
