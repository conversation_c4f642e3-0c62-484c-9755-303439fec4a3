package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalTime;

/**
 * 用户通知设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_notification_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSettings {
    
    @Id
    @Column("user_id")
    private Long userId;
    
    // ==================== 基础通知设置 ====================
    
    /**
     * 消息通知开关
     */
    @Column("message_notifications")
    private Boolean messageNotifications = true;
    
    /**
     * 群组通知开关
     */
    @Column("group_notifications")
    private Boolean groupNotifications = true;
    
    /**
     * 频道通知开关
     */
    @Column("channel_notifications")
    private Boolean channelNotifications = true;
    
    /**
     * 通话通知开关
     */
    @Column("call_notifications")
    private Boolean callNotifications = true;
    
    // ==================== 通知详情设置 ====================
    
    /**
     * 显示消息预览
     */
    @Column("show_preview")
    private Boolean showPreview = true;
    
    /**
     * 显示发送者姓名
     */
    @Column("show_sender")
    private Boolean showSender = true;
    
    /**
     * 通知声音
     */
    @Column("notification_sound")
    private String notificationSound = "default";
    
    /**
     * 自定义通知声音URL
     */
    @Column("custom_sound_url")
    private String customSoundUrl;
    
    // ==================== 免打扰设置 ====================
    
    /**
     * 免打扰模式开关
     */
    @Column("do_not_disturb")
    private Boolean doNotDisturb = false;
    
    /**
     * 免打扰开始时间
     */
    @Column("quiet_hours_start")
    private LocalTime quietHoursStart;
    
    /**
     * 免打扰结束时间
     */
    @Column("quiet_hours_end")
    private LocalTime quietHoursEnd;
    
    /**
     * 周末免打扰
     */
    @Column("weekend_quiet")
    private Boolean weekendQuiet = false;
    
    // ==================== 振动和LED设置 ====================
    
    /**
     * 振动开关
     */
    @Column("vibration_enabled")
    private Boolean vibrationEnabled = true;
    
    /**
     * 振动模式
     */
        @Column("vibration_pattern")
    private VibrationPattern vibrationPattern = VibrationPattern.DEFAULT;
    
    /**
     * LED灯颜色
     */
        @Column("led_color")
    private LedColor ledColor = LedColor.BLUE;
    
    /**
     * LED灯开关
     */
    @Column("led_enabled")
    private Boolean ledEnabled = true;
    
    // ==================== 高级设置 ====================
    
    /**
     * 重复通知间隔（分钟）
     */
    @Column("repeat_interval")
    private Integer repeatInterval = 0; // 0表示不重复
    
    /**
     * 通知优先级
     */
        @Column("notification_priority")
    private NotificationPriority notificationPriority = NotificationPriority.NORMAL;
    
    /**
     * 锁屏显示通知
     */
    @Column("show_on_lock_screen")
    private Boolean showOnLockScreen = true;
    
    /**
     * 通知分组
     */
    @Column("group_notifications_enabled")
    private Boolean groupNotificationsEnabled = true;
    
    // ==================== 时间戳 ====================
    
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;
    
    // ==================== 关联关系 ====================
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    // ==================== 枚举定义 ====================
    
    /**
     * 振动模式枚举
     */
    public enum VibrationPattern {
        DEFAULT("默认"),
        SHORT("短振动"),
        LONG("长振动"),
        DOUBLE("双振动"),
        CUSTOM("自定义");
        
        private final String description;
        
        VibrationPattern(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * LED颜色枚举
     */
    public enum LedColor {
        RED("红色"),
        GREEN("绿色"),
        BLUE("蓝色"),
        YELLOW("黄色"),
        PURPLE("紫色"),
        WHITE("白色"),
        OFF("关闭");
        
        private final String description;
        
        LedColor(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 通知优先级枚举
     */
    public enum NotificationPriority {
        LOW("低"),
        NORMAL("普通"),
        HIGH("高"),
        URGENT("紧急");
        
        private final String description;
        
        NotificationPriority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数 - 创建默认通知设置
     */
    public NotificationSettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查当前时间是否在免打扰时段
     */
    public boolean isInQuietHours() {
        if (!doNotDisturb || quietHoursStart == null || quietHoursEnd == null) {
            return false;
        }
        
        LocalTime now = LocalTime.now();
        
        // 处理跨天的情况
        if (quietHoursStart.isAfter(quietHoursEnd)) {
            return now.isAfter(quietHoursStart) || now.isBefore(quietHoursEnd);
        } else {
            return now.isAfter(quietHoursStart) && now.isBefore(quietHoursEnd);
        }
    }
    
    /**
     * 检查是否应该显示通知
     */
    public boolean shouldShowNotification(String notificationType) {
        if (isInQuietHours()) {
            return false;
        }
        
        return switch (notificationType.toLowerCase()) {
            case "message" -> messageNotifications;
            case "group" -> groupNotifications;
            case "channel" -> channelNotifications;
            case "call" -> callNotifications;
            default -> true;
        };
    }
    
    /**
     * 获取有效的通知声音
     */
    public String getEffectiveNotificationSound() {
        if (customSoundUrl != null && !customSoundUrl.trim().isEmpty()) {
            return customSoundUrl;
        }
        return notificationSound;
    }
}
