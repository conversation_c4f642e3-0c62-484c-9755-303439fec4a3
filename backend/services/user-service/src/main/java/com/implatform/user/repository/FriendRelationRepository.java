package com.implatform.user.repository;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.FriendRelation;

/**
 * 好友关系Repository
 */
@Repository
public interface FriendRelationRepository extends ReactiveCrudRepository<FriendRelation, Long> {

    /**
     * 查找用户的好友关系
     */
    Mono<FriendRelation> findByUserIdAndFriendId(Long userId, Long friendId);

    /**
     * 查找用户的所有好友
     */
    Flux<FriendRelation> findByUserIdAndStatus(Long userId, FriendRelation.FriendStatus status);

    /**
     * 查找用户的所有好友（分页）
     */
    @Query("SELECT * FROM friend_relations WHERE user_id = :userId AND status = :status ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<FriendRelation> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, FriendRelation.FriendStatus status, int limit, long offset);

    /**
     * 查找用户的黑名单
     */
    Flux<FriendRelation> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, FriendRelation.FriendStatus status);

    /**
     * 搜索好友
     */
    @Query("SELECT * FROM friend_relations WHERE user_id = :userId AND status = :status AND remark LIKE CONCAT('%', :keyword, '%')")
    Flux<FriendRelation> searchFriends(Long userId, FriendRelation.FriendStatus status, String keyword);

    /**
     * 检查是否为好友
     */
    Mono<Boolean> existsByUserIdAndFriendIdAndStatus(Long userId, Long friendId, FriendRelation.FriendStatus status);

    /**
     * 删除好友关系（双向）
     */
    Mono<Void> deleteByUserIdAndFriendId(Long userId, Long friendId);

    /**
     * 统计好友数量
     */
    Mono<Long> countByUserIdAndStatus(Long userId, FriendRelation.FriendStatus status);
}