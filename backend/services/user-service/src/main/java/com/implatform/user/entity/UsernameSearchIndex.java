package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.LocalDateTime;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 用户名搜索索引实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("username_search_index"),
    @Index(name = "idx_username_search_index_user_id", columnList = "user_id"),
    @Index(name = "idx_username_search_index_verified", columnList = "is_verified"),
    @Index(name = "idx_username_search_index_premium", columnList = "is_premium"),
    @Index(name = "idx_username_search_index_public", columnList = "is_public"),
    @Index(name = "idx_username_search_index_last_active", columnList = "last_active")
})
@EqualsAndHashCode(callSuper = false)
public class UsernameSearchIndex {

    @Id
        private Long id;

    /**
     * 用户名
     */
    @Column
    private String username;

    /**
     * 用户ID
     */
    @Column
    private Long userId;

    /**
     * 显示名称
     */
    @Column
    private String displayName;

    /**
     * 个人简介
     */
    @Column
    private String bio;

    /**
     * 头像URL
     */
    @Column
    private String avatarUrl;

    /**
     * 是否已验证
     */
    @Column
    private Boolean isVerified = false;

    /**
     * 是否为高级用户
     */
    @Column
    private Boolean isPremium = false;

    /**
     * 是否公开可搜索
     */
    @Column
    private Boolean isPublic = true;

    /**
     * 最后活跃时间
     */
    @Column("last_active")
    private LocalDateTime lastActive;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column
    private LocalDateTime updatedAt;

    /**
     * 检查是否已验证
     */
    public boolean isUserVerified() {
        return Boolean.TRUE.equals(isVerified);
    }

    /**
     * 检查是否为高级用户
     */
    public boolean isPremiumUser() {
        return Boolean.TRUE.equals(isPremium);
    }

    /**
     * 检查是否公开可搜索
     */
    public boolean isPubliclySearchable() {
        return Boolean.TRUE.equals(isPublic);
    }

    /**
     * 检查是否为活跃用户
     */
    public boolean isActiveUser() {
        if (lastActive == null) {
            return false;
        }
        
        // 30天内活跃算作活跃用户
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        return lastActive.isAfter(thirtyDaysAgo);
    }

    /**
     * 检查是否为最近活跃用户
     */
    public boolean isRecentlyActive() {
        if (lastActive == null) {
            return false;
        }
        
        // 7天内活跃算作最近活跃
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        return lastActive.isAfter(sevenDaysAgo);
    }

    /**
     * 检查是否在线
     */
    public boolean isOnline() {
        if (lastActive == null) {
            return false;
        }
        
        // 5分钟内活跃算作在线
        LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
        return lastActive.isAfter(fiveMinutesAgo);
    }

    /**
     * 获取用户状态描述
     */
    public String getUserStatusDescription() {
        if (isOnline()) {
            return "在线";
        } else if (isRecentlyActive()) {
            return "最近活跃";
        } else if (isActiveUser()) {
            return "活跃用户";
        } else {
            return "离线";
        }
    }

    /**
     * 获取用户类型描述
     */
    public String getUserTypeDescription() {
        StringBuilder type = new StringBuilder();
        
        if (isUserVerified()) {
            type.append("已验证");
        }
        
        if (isPremiumUser()) {
            if (type.length() > 0) {
                type.append(" + ");
            }
            type.append("高级用户");
        }
        
        if (type.length() == 0) {
            type.append("普通用户");
        }
        
        return type.toString();
    }

    /**
     * 获取最后活跃时间描述
     */
    public String getLastActiveDescription() {
        if (lastActive == null) {
            return "从未活跃";
        }
        
        LocalDateTime now = LocalDateTime.now();
        java.time.Duration duration = java.time.Duration.between(lastActive, now);
        
        long seconds = duration.getSeconds();
        if (seconds < 60) {
            return "刚刚活跃";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟前活跃";
        } else if (seconds < 86400) {
            return (seconds / 3600) + "小时前活跃";
        } else {
            long days = seconds / 86400;
            if (days == 1) {
                return "昨天活跃";
            } else if (days < 30) {
                return days + "天前活跃";
            } else {
                return "很久未活跃";
            }
        }
    }

    /**
     * 获取搜索显示名称
     */
    public String getSearchDisplayName() {
        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        } else {
            return "@" + username;
        }
    }

    /**
     * 获取完整显示信息
     */
    public String getFullDisplayInfo() {
        StringBuilder info = new StringBuilder();
        info.append("@").append(username);
        
        if (displayName != null && !displayName.trim().isEmpty()) {
            info.append(" (").append(displayName).append(")");
        }
        
        if (isUserVerified()) {
            info.append(" ✓");
        }
        
        if (isPremiumUser()) {
            info.append(" ⭐");
        }
        
        return info.toString();
    }

    /**
     * 获取搜索结果摘要
     */
    public String getSearchResultSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(getFullDisplayInfo());
        summary.append(" - ").append(getUserStatusDescription());
        
        if (bio != null && !bio.trim().isEmpty()) {
            String shortBio = bio.length() > 50 ? bio.substring(0, 47) + "..." : bio;
            summary.append(" - ").append(shortBio);
        }
        
        return summary.toString();
    }

    /**
     * 检查是否匹配搜索关键词
     */
    public boolean matchesSearch(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return true;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        
        // 检查用户名
        if (username != null && username.toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        // 检查显示名称
        if (displayName != null && displayName.toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        // 检查个人简介
        if (bio != null && bio.toLowerCase().contains(lowerKeyword)) {
            return true;
        }
        
        return false;
    }

    /**
     * 获取搜索匹配度分数
     */
    public int getSearchMatchScore(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return 0;
        }
        
        String lowerKeyword = keyword.toLowerCase();
        int score = 0;
        
        // 用户名匹配
        if (username != null) {
            String lowerUsername = username.toLowerCase();
            if (lowerUsername.equals(lowerKeyword)) {
                score += 100; // 精确匹配
            } else if (lowerUsername.startsWith(lowerKeyword)) {
                score += 80; // 前缀匹配
            } else if (lowerUsername.contains(lowerKeyword)) {
                score += 60; // 包含匹配
            }
        }
        
        // 显示名称匹配
        if (displayName != null) {
            String lowerDisplayName = displayName.toLowerCase();
            if (lowerDisplayName.equals(lowerKeyword)) {
                score += 50;
            } else if (lowerDisplayName.contains(lowerKeyword)) {
                score += 30;
            }
        }
        
        // 个人简介匹配
        if (bio != null && bio.toLowerCase().contains(lowerKeyword)) {
            score += 20;
        }
        
        // 验证用户加分
        if (isUserVerified()) {
            score += 10;
        }
        
        // 高级用户加分
        if (isPremiumUser()) {
            score += 5;
        }
        
        // 活跃用户加分
        if (isActiveUser()) {
            score += 5;
        }
        
        return score;
    }

    /**
     * 获取用户详细信息
     */
    public String getDetailedUserInfo() {
        StringBuilder info = new StringBuilder();
        info.append("用户名: @").append(username);
        
        if (displayName != null && !displayName.trim().isEmpty()) {
            info.append("\n显示名称: ").append(displayName);
        }
        
        info.append("\n用户类型: ").append(getUserTypeDescription());
        info.append("\n状态: ").append(getUserStatusDescription());
        info.append("\n最后活跃: ").append(getLastActiveDescription());
        
        if (bio != null && !bio.trim().isEmpty()) {
            info.append("\n个人简介: ").append(bio);
        }
        
        info.append("\n可搜索: ").append(isPubliclySearchable() ? "是" : "否");
        
        return info.toString();
    }

    /**
     * 更新最后活跃时间
     */
    public void updateLastActive() {
        this.lastActive = LocalDateTime.now();
    }

    /**
     * 设置为公开可搜索
     */
    public void makePublic() {
        this.isPublic = true;
    }

    /**
     * 设置为私有不可搜索
     */
    public void makePrivate() {
        this.isPublic = false;
    }
}
