package com.implatform.admin.repository;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.admin.entity.Permission;

/**
 * 权限数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 */
@Repository
public interface PermissionRepository extends ReactiveCrudRepository<Permission, String> {

    /**
     * 根据权限编码查找权限
     */
    Mono<Permission> findByCode(String code);

    /**
     * 检查权限编码是否存在
     */
    Mono<Boolean> existsByCode(String code);

    /**
     * 检查权限编码是否存在（排除指定权限）
     */
    @Query("SELECT COUNT(*) > 0 FROM permissions WHERE code = :code AND id != :id")
    Mono<Boolean> existsByCodeAndIdNot(String code, String id);

    /**
     * 根据权限名称查找权限
     */
    Mono<Permission> findByName(String name);

    /**
     * 检查权限名称是否存在
     */
    Mono<Boolean> existsByName(String name);

    /**
     * 检查权限名称是否存在（排除指定权限）
     */
    @Query("SELECT COUNT(*) > 0 FROM permissions WHERE name = :name AND id != :id")
    Mono<Boolean> existsByNameAndIdNot(String name, String id);

    /**
     * 根据权限类型查找权限
     */
    Flux<Permission> findByType(Permission.PermissionType type);

    /**
     * 根据权限类型分页查找权限
     */
    @Query("SELECT * FROM permissions WHERE type = :type ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<Permission> findByTypeOrderBySortOrderAsc(Permission.PermissionType type, int limit, long offset);

    /**
     * 根据状态查找权限
     */
    Flux<Permission> findByStatus(Permission.PermissionStatus status);

    /**
     * 根据状态分页查找权限
     */
    @Query("SELECT * FROM permissions WHERE status = :status ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<Permission> findByStatusOrderBySortOrderAsc(Permission.PermissionStatus status, int limit, long offset);

    /**
     * 查找根权限（无父权限）
     */
    Flux<Permission> findByParentIdIsNull();

    /**
     * 根据父权限ID查找子权限
     */
    Flux<Permission> findByParentId(String parentId);

    /**
     * 根据父权限ID查找子权限（按排序号排序）
     */
    Flux<Permission> findByParentIdOrderBySortOrderAsc(String parentId);

    /**
     * 查找非系统内置权限
     */
    Flux<Permission> findByIsSystemFalse();

    /**
     * 查找系统内置权限
     */
    Flux<Permission> findByIsSystemTrue();

    /**
     * 根据资源和动作查找权限
     */
    Mono<Permission> findByResourceAndAction(String resource, String action);

    /**
     * 根据资源查找权限
     */
    Flux<Permission> findByResource(String resource);

    /**
     * 根据角色ID查找权限
     */
    @Query("SELECT p.* FROM permissions p " +
           "JOIN role_permissions rp ON p.id = rp.permission_id " +
           "WHERE rp.role_id = :roleId")
    Flux<Permission> findByRoleId(String roleId);

    /**
     * 根据角色编码查找权限
     */
    @Query("SELECT p.* FROM permissions p " +
           "JOIN role_permissions rp ON p.id = rp.permission_id " +
           "JOIN roles r ON rp.role_id = r.id " +
           "WHERE r.code = :roleCode")
    Flux<Permission> findByRoleCode(String roleCode);

    /**
     * 根据用户ID查找权限
     */
    @Query("SELECT DISTINCT p.* FROM permissions p " +
           "JOIN role_permissions rp ON p.id = rp.permission_id " +
           "JOIN roles r ON rp.role_id = r.id " +
           "JOIN admin_user_roles ur ON r.id = ur.role_id " +
           "WHERE ur.admin_user_id = :userId")
    Flux<Permission> findByUserId(String userId);

    /**
     * 搜索权限（名称、编码、描述模糊匹配）
     */
    @Query("SELECT * FROM permissions WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "LOWER(name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(code) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "ORDER BY sort_order ASC LIMIT :limit OFFSET :offset")
    Flux<Permission> searchPermissions(String keyword, int limit, long offset);

    /**
     * 根据排序号查找权限
     */
    Flux<Permission> findAllByOrderBySortOrderAsc();

    /**
     * 构建权限树（根权限及其子权限）
     */
    @Query("SELECT * FROM permissions WHERE parent_id IS NULL ORDER BY sort_order ASC")
    Flux<Permission> findRootPermissions();

    /**
     * 获取权限的所有子权限ID（递归）
     */
    @Query("WITH RECURSIVE permission_tree AS (" +
           "  SELECT id, parent_id FROM permissions WHERE id = :permissionId " +
           "  UNION ALL " +
           "  SELECT p.id, p.parent_id FROM permissions p " +
           "  INNER JOIN permission_tree pt ON p.parent_id = pt.id" +
           ") SELECT id FROM permission_tree WHERE id != :permissionId")
    Flux<String> findChildPermissionIds(String permissionId);

    /**
     * 统计权限总数
     */
    @Query("SELECT COUNT(*) FROM permissions")
    Mono<Long> countTotalPermissions();

    /**
     * 统计活跃权限数
     */
    @Query("SELECT COUNT(*) FROM permissions WHERE status = 'ACTIVE'")
    Mono<Long> countActivePermissions();

    /**
     * 统计各类型权限数量
     */
    @Query("SELECT type, COUNT(*) FROM permissions GROUP BY type")
    Flux<Object[]> countPermissionsByType();

    /**
     * 批量更新权限状态
     */
    @Query("UPDATE permissions SET status = :status, updated_by = :updatedBy " +
           "WHERE id = ANY(:permissionIds)")
    Mono<Integer> updateStatusByIds(String[] permissionIds, Permission.PermissionStatus status, String updatedBy);

    /**
     * 检查权限是否被角色使用
     */
    @Query("SELECT COUNT(*) > 0 FROM role_permissions WHERE permission_id = :permissionId")
    Mono<Boolean> isPermissionInUse(String permissionId);

    /**
     * 获取权限的角色数量
     */
    @Query("SELECT COUNT(*) FROM role_permissions WHERE permission_id = :permissionId")
    Mono<Long> countRolesByPermissionId(String permissionId);

    /**
     * 检查是否存在循环依赖
     */
    @Query("WITH RECURSIVE permission_path AS (" +
           "  SELECT id, parent_id, 1 as level FROM permissions WHERE id = :childId " +
           "  UNION ALL " +
           "  SELECT p.id, p.parent_id, pp.level + 1 FROM permissions p " +
           "  INNER JOIN permission_path pp ON p.id = pp.parent_id " +
           "  WHERE pp.level < 10" +
           ") SELECT COUNT(*) > 0 FROM permission_path WHERE parent_id = :parentId")
    Mono<Boolean> hasCircularDependency(String childId, String parentId);
}