package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 昵称更改历史实体
 *
 * <p><strong>业务用途</strong>：
 * 记录用户昵称的更改历史，用于：
 * <ul>
 *   <li>昵称更改时间限制控制</li>
 *   <li>昵称更改审计追踪</li>
 *   <li>异常行为检测</li>
 *   <li>数据恢复和回滚</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "nickname_change_history", indexes = {
    @Index(name = "idx_nickname_history_user_id", columnList = "user_id"),
    @Index(name = "idx_nickname_history_user_created", columnList = "user_id, created_at"),
    @Index(name = "idx_nickname_history_created_at", columnList = "created_at")
})
@Data
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class NicknameChangeHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 旧昵称
     */
    @Column(name = "old_nickname", length = 50)
    private String oldNickname;

    /**
     * 新昵称
     */
    @Column(name = "new_nickname", length = 50, nullable = false)
    private String newNickname;

    /**
     * 更改类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "change_type", nullable = false, length = 20)
    private ChangeType changeType;

    /**
     * 更改原因
     */
    @Column(name = "change_reason", length = 200)
    private String changeReason;

    /**
     * 操作者ID（管理员强制更改时记录）
     */
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 审核状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "review_status", nullable = false, length = 20)
    private ReviewStatus reviewStatus = ReviewStatus.APPROVED;

    /**
     * 审核者ID
     */
    @Column(name = "reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;

    /**
     * 审核备注
     */
    @Column(name = "review_notes", length = 500)
    private String reviewNotes;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更改类型枚举
     */
    public enum ChangeType {
        /**
         * 用户主动更改
         */
        USER_CHANGE,
        
        /**
         * 系统自动更改
         */
        SYSTEM_CHANGE,
        
        /**
         * 管理员强制更改
         */
        ADMIN_FORCE,
        
        /**
         * 初始设置
         */
        INITIAL_SET,
        
        /**
         * 违规处理
         */
        VIOLATION_HANDLE
    }

    /**
     * 审核状态枚举
     */
    public enum ReviewStatus {
        /**
         * 待审核
         */
        PENDING,
        
        /**
         * 已通过
         */
        APPROVED,
        
        /**
         * 已拒绝
         */
        REJECTED,
        
        /**
         * 需要人工审核
         */
        MANUAL_REVIEW
    }

    /**
     * 构造函数
     */
    public NicknameChangeHistory() {}

    /**
     * 创建用户更改记录
     */
    public static NicknameChangeHistory createUserChange(Long userId, String oldNickname, 
                                                        String newNickname, String ipAddress, 
                                                        String userAgent) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(oldNickname);
        history.setNewNickname(newNickname);
        history.setChangeType(ChangeType.USER_CHANGE);
        history.setIpAddress(ipAddress);
        history.setUserAgent(userAgent);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 创建管理员强制更改记录
     */
    public static NicknameChangeHistory createAdminForce(Long userId, String oldNickname, 
                                                        String newNickname, Long operatorId, 
                                                        String reason) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(oldNickname);
        history.setNewNickname(newNickname);
        history.setChangeType(ChangeType.ADMIN_FORCE);
        history.setOperatorId(operatorId);
        history.setChangeReason(reason);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 创建初始设置记录
     */
    public static NicknameChangeHistory createInitialSet(Long userId, String nickname, 
                                                        String ipAddress, String userAgent) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(null);
        history.setNewNickname(nickname);
        history.setChangeType(ChangeType.INITIAL_SET);
        history.setIpAddress(ipAddress);
        history.setUserAgent(userAgent);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 设置审核通过
     */
    public void approve(Long reviewerId, String notes) {
        this.reviewStatus = ReviewStatus.APPROVED;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.reviewNotes = notes;
    }

    /**
     * 设置审核拒绝
     */
    public void reject(Long reviewerId, String notes) {
        this.reviewStatus = ReviewStatus.REJECTED;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.reviewNotes = notes;
    }

    /**
     * 是否需要审核
     */
    public boolean needsReview() {
        return reviewStatus == ReviewStatus.PENDING || reviewStatus == ReviewStatus.MANUAL_REVIEW;
    }

    /**
     * 是否已通过审核
     */
    public boolean isApproved() {
        return reviewStatus == ReviewStatus.APPROVED;
    }
}
