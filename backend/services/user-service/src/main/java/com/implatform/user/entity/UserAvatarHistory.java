package com.implatform.user.entity;

import com.implatform.user.enums.AvatarStatus;
import com.implatform.user.enums.ChangeReason;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 用户头像历史记录实体
 * 跟踪用户所有头像变更记录，包括审核状态和变更原因
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_avatar_history"),
    @Index(name = "idx_avatar_history_status", columnList = "status"),
    @Index(name = "idx_avatar_history_submitted_at", columnList = "submitted_at"),
    @Index(name = "idx_avatar_history_user_status", columnList = "user_id, status"),
    @Index(name = "idx_avatar_history_active", columnList = "is_active")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAvatarHistory {

    /**
     * 主键ID
     */
    @Id
        @Column("id")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    @Column("user_id")
    private Long userId;

    /**
     * 头像URL
     */
    @NotBlank(message = "Avatar URL cannot be blank")
    @Size(max = 500, message = "Avatar URL cannot exceed 500 characters")
    @Column("avatar_url")
    private String avatarUrl;

    /**
     * 之前的头像URL
     */
    @Size(max = 500, message = "Previous avatar URL cannot exceed 500 characters")
    @Column("previous_avatar_url")
    private String previousAvatarUrl;

    /**
     * 审核状态
     */
    @NotNull(message = "Status cannot be null")
        @Column("status")
    private AvatarStatus status;

    /**
     * 变更原因
     */
    @NotNull(message = "Change reason cannot be null")
        @Column("change_reason")
    private ChangeReason changeReason;

    /**
     * 提交时间
     */
    @NotNull(message = "Submitted time cannot be null")
    @CreatedDate
    @Column("submitted_at")
    private Instant submittedAt;

    /**
     * 审核时间
     */
    @Column("reviewed_at")
    private Instant reviewedAt;

    /**
     * 审核人ID
     */
    @Column("reviewed_by")
    private Long reviewedBy;

    /**
     * 拒绝原因
     */
    @Size(max = 1000, message = "Rejection reason cannot exceed 1000 characters")
    @Column("rejection_reason")
    private String rejectionReason;

    /**
     * 管理员备注
     */
    @Size(max = 2000, message = "Admin notes cannot exceed 2000 characters")
    @Column("admin_notes")
    private String adminNotes;

    /**
     * 客户端IP地址
     */
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    @Column("client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    @Column("user_agent")
    private String userAgent;

    /**
     * 文件大小（字节）
     */
    @Column("file_size")
    private Long fileSize;

    /**
     * 原始文件名
     */
    @Size(max = 255, message = "Original file name cannot exceed 255 characters")
    @Column("original_file_name")
    private String originalFileName;

    /**
     * 内容类型
     */
    @Size(max = 100, message = "Content type cannot exceed 100 characters")
    @Column("content_type")
    private String contentType;

    /**
     * 是否为当前活跃头像
     */
    @NotNull(message = "Is active cannot be null")
    @Builder.Default
    @Column("is_active")
    private Boolean isActive = false;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 用户实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 审核人实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reviewed_by", insertable = false, updatable = false)
    private User reviewer;

    /**
     * 设置为已审核状态
     * 
     * @param reviewerId 审核人ID
     * @param status 审核状态
     * @param rejectionReason 拒绝原因（如果被拒绝）
     * @param adminNotes 管理员备注
     */
    public void markAsReviewed(Long reviewerId, AvatarStatus status, String rejectionReason, String adminNotes) {
        this.reviewedBy = reviewerId;
        this.reviewedAt = Instant.now();
        this.status = status;
        this.rejectionReason = rejectionReason;
        this.adminNotes = adminNotes;
    }

    /**
     * 设置为活跃头像
     */
    public void setAsActive() {
        this.isActive = true;
        this.status = AvatarStatus.APPROVED;
    }

    /**
     * 取消活跃状态
     */
    public void setAsInactive() {
        this.isActive = false;
    }

    /**
     * 检查是否已审核
     * 
     * @return 是否已审核
     */
    public boolean isReviewed() {
        return reviewedAt != null && reviewedBy != null;
    }

    /**
     * 检查是否被拒绝
     * 
     * @return 是否被拒绝
     */
    public boolean isRejected() {
        return AvatarStatus.REJECTED.equals(status);
    }

    /**
     * 检查是否已批准
     * 
     * @return 是否已批准
     */
    public boolean isApproved() {
        return AvatarStatus.APPROVED.equals(status);
    }

    /**
     * 检查是否待审核
     * 
     * @return 是否待审核
     */
    public boolean isPending() {
        return AvatarStatus.PENDING.equals(status);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserAvatarHistory that = (UserAvatarHistory) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserAvatarHistory{" +
                "id=" + id +
                ", userId=" + userId +
                ", status=" + status +
                ", changeReason=" + changeReason +
                ", submittedAt=" + submittedAt +
                ", reviewedAt=" + reviewedAt +
                ", isActive=" + isActive +
                '}';
    }
}
