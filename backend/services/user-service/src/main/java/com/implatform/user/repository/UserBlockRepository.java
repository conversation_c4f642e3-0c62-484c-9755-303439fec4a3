package com.implatform.user.repository;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.UserBlock;

/**
 * 用户屏蔽Repository
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface UserBlockRepository extends ReactiveCrudRepository<UserBlock, Long> {

    /**
     * 检查用户A是否屏蔽了用户B
     */
    Mono<Boolean> existsByBlockerIdAndBlockedId(Long blockerId, Long blockedId);

    /**
     * 查找用户屏蔽的所有用户
     */
    Flux<UserBlock> findByBlockerIdOrderByCreatedAtDesc(Long blockerId);

    /**
     * 分页查找用户屏蔽的所有用户
     */
    @Query("SELECT * FROM user_blocks WHERE blocker_id = :blockerId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<UserBlock> findByBlockerIdOrderByCreatedAtDescPaged(Long blockerId, int limit, long offset);

    /**
     * 查找屏蔽了某用户的所有用户
     */
    Flux<UserBlock> findByBlockedIdOrderByCreatedAtDesc(Long blockedId);

    /**
     * 根据屏蔽者和被屏蔽者查找记录
     */
    Mono<UserBlock> findByBlockerIdAndBlockedId(Long blockerId, Long blockedId);
    
    /**
     * 删除屏蔽关系
     */
    Mono<Void> deleteByBlockerIdAndBlockedId(Long blockerId, Long blockedId);

    /**
     * 删除用户的所有屏蔽关系（作为屏蔽者）
     */
    Mono<Void> deleteByBlockerId(Long blockerId);

    /**
     * 删除用户的所有被屏蔽关系（作为被屏蔽者）
     */
    Mono<Void> deleteByBlockedId(Long blockedId);

    /**
     * 统计用户屏蔽的用户数量
     */
    Mono<Long> countByBlockerId(Long blockerId);

    /**
     * 统计屏蔽某用户的用户数量
     */
    Mono<Long> countByBlockedId(Long blockedId);

    /**
     * 查找用户屏蔽的用户ID列表
     */
    @Query("SELECT blocked_id FROM user_blocks WHERE blocker_id = :blockerId")
    Flux<Long> findBlockedUserIdsByBlockerId(Long blockerId);

    /**
     * 查找屏蔽某用户的用户ID列表
     */
    @Query("SELECT blocker_id FROM user_blocks WHERE blocked_id = :blockedId")
    Flux<Long> findBlockerUserIdsByBlockedId(Long blockedId);

    /**
     * 检查两个用户之间是否存在任何屏蔽关系（双向检查）
     */
    @Query("SELECT COUNT(*) > 0 FROM user_blocks WHERE " +
           "(blocker_id = :userId1 AND blocked_id = :userId2) OR " +
           "(blocker_id = :userId2 AND blocked_id = :userId1)")
    Mono<Boolean> existsBlockRelationBetween(Long userId1, Long userId2);

    /**
     * 批量检查用户是否被某用户屏蔽
     */
    @Query("SELECT blocked_id FROM user_blocks WHERE blocker_id = :blockerId AND blocked_id = ANY(:userIds)")
    Flux<Long> findBlockedUserIdsFromList(Long blockerId, Long[] userIds);

    /**
     * 获取最近的屏蔽记录
     */
    @Query("SELECT * FROM user_blocks ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<UserBlock> findRecentBlocks(int limit, long offset);
}
