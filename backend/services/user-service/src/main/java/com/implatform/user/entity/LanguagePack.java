package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;
import java.util.List;

/**
 * 语言包基本信息实体类
 * 
 * <p>该实体类用于存储语言包的基本信息，包括语言代码、名称、标志等属性。
 * 支持多版本管理，每个语言包可以有多个版本。</p>
 * 
 * <p><strong>主要功能</strong>：
 * <ul>
 *   <li>语言包基本信息管理</li>
 *   <li>支持RTL语言标识</li>
 *   <li>国旗表情符号显示</li>
 *   <li>语言包状态管理</li>
 *   <li>显示排序支持</li>
 * </ul>
 * 
 * <p><strong>数据库映射</strong>：
 * 映射到 language_packs 表，包含语言代码的唯一索引和状态查询索引。
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("language_packs"),
    @Index(name = "idx_language_packs_status", columnList = "status"),
    @Index(name = "idx_language_packs_sort_order", columnList = "sort_order")
})
@EqualsAndHashCode(callSuper = false)
public class LanguagePack {
    
    @Id
        private Long id;
    
    /**
     * 语言代码（唯一）
     * 格式：zh-CN, en-US, ja-JP 等
     */
    @NotBlank(message = "语言代码不能为空")
    @Size(max = 10, message = "语言代码长度不能超过10个字符")
    @Column("language_code")
    private String languageCode;
    
    /**
     * 本地化名称
     * 如：简体中文、English、日本語
     */
    @NotBlank(message = "本地化名称不能为空")
    @Size(max = 100, message = "本地化名称长度不能超过100个字符")
    @Column("native_name")
    private String nativeName;
    
    /**
     * 英文名称
     * 如：Chinese (Simplified)、English、Japanese
     */
    @NotBlank(message = "英文名称不能为空")
    @Size(max = 100, message = "英文名称长度不能超过100个字符")
    @Column("english_name")
    private String englishName;
    
    /**
     * 国旗表情符号
     * 如：🇨🇳、🇺🇸、🇯🇵
     */
    @Size(max = 10, message = "国旗表情符号长度不能超过10个字符")
    @Column("flag")
    private String flag;
    
    /**
     * 是否为RTL语言
     * 如阿拉伯语、希伯来语等从右到左的语言
     */
    @Builder.Default
    @Column("is_rtl")
    private Boolean isRtl = false;
    
    /**
     * 默认日期格式
     * 如：YYYY-MM-DD、MM/DD/YYYY、DD/MM/YYYY
     */
    @Size(max = 50, message = "默认日期格式长度不能超过50个字符")
    @Column("default_date_format")
    private String defaultDateFormat;
    
    /**
     * 主语言代码
     * 如：zh、en、ja（不包含地区代码）
     */
    @Size(max = 5, message = "主语言代码长度不能超过5个字符")
    @Column("main_code")
    private String mainCode;
    
    /**
     * 地区代码
     * 如：CN、US、JP
     */
    @Size(max = 5, message = "地区代码长度不能超过5个字符")
    @Column("region_code")
    private String regionCode;
    
    /**
     * 语言包状态
     */
    @Builder.Default
        @Column("status")
    private LanguagePackStatus status = LanguagePackStatus.ACTIVE;
    
    /**
     * 显示排序
     * 数值越小越靠前
     */
    @Builder.Default
    @Min(value = 0, message = "排序值不能小于0")
    @Column("sort_order")
    private Integer sortOrder = 0;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column
    private Instant createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column
    private Instant updatedAt;
    
    /**
     * 语言包版本列表（一对多关系）
     */
    @OneToMany(mappedBy = "languagePack", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LanguagePackVersion> versions;
    
    /**
     * 语言包状态枚举
     */
    public enum LanguagePackStatus {
        /**
         * 激活状态 - 可正常使用
         */
        ACTIVE,
        /**
         * 非激活状态 - 暂时禁用
         */
        INACTIVE,
        /**
         * 已弃用状态 - 不再维护
         */
        DEPRECATED
    }
    
    /**
     * 获取当前发布的版本
     * 
     * @return 当前发布的版本，如果没有则返回null
     */
    public LanguagePackVersion getCurrentVersion() {
        if (versions == null || versions.isEmpty()) {
            return null;
        }
        
        return versions.stream()
            .filter(version -> version.getIsCurrent() && 
                             version.getStatus() == LanguagePackVersion.VersionStatus.PUBLISHED)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 检查是否为激活状态
     * 
     * @return true如果状态为ACTIVE
     */
    public boolean isActive() {
        return status == LanguagePackStatus.ACTIVE;
    }
    
    /**
     * 检查是否为RTL语言
     * 
     * @return true如果是RTL语言
     */
    public boolean isRightToLeft() {
        return Boolean.TRUE.equals(isRtl);
    }
    
    /**
     * 获取完整的语言显示名称
     * 格式：本地化名称 (英文名称)
     * 
     * @return 完整的语言显示名称
     */
    public String getFullDisplayName() {
        if (nativeName != null && englishName != null && !nativeName.equals(englishName)) {
            return String.format("%s (%s)", nativeName, englishName);
        }
        return nativeName != null ? nativeName : englishName;
    }
    
    /**
     * 设置为激活状态
     */
    public void activate() {
        this.status = LanguagePackStatus.ACTIVE;
    }
    
    /**
     * 设置为非激活状态
     */
    public void deactivate() {
        this.status = LanguagePackStatus.INACTIVE;
    }
    
    /**
     * 设置为已弃用状态
     */
    public void deprecate() {
        this.status = LanguagePackStatus.DEPRECATED;
    }
}
