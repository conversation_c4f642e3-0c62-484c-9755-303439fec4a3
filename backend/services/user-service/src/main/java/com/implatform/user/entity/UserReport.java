package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户举报实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_reports")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserReport {
    
    @Id
        private Long id;
    
    @Column("reporter_id")
    private Long reporterId;
    
    @Column("target_user_id")
    private Long targetUserId;
    
        @Column("report_type")
    private ReportType reportType;
    
    @Column("reason")
    private String reason;
    
    @Column("message_id")
    private Long messageId;
    
    @Column("group_id")
    private Long groupId;
    
        @Column("status")
    private ReportStatus status = ReportStatus.PENDING;
    
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    @Column("handled_at")
    private LocalDateTime handledAt;
    
    @Column("handled_by")
    private Long handledBy;

    @Column("handle_note")
    private String handleNote;

    @Column("is_urgent")
    private Boolean isUrgent = false;

    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 举报者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reporter_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User reporter;
    
    /**
     * 被举报者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_user_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User targetUser;
    
    /**
     * 处理者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "handled_by", referencedColumnName = "id", insertable = false, updatable = false)
    private User handler;
    
    /**
     * 举报类型枚举
     */
    public enum ReportType {
        SPAM("垃圾信息"),
        HARASSMENT("骚扰"),
        INAPPROPRIATE_CONTENT("不当内容"),
        FAKE_ACCOUNT("虚假账户"),
        VIOLENCE("暴力威胁"),
        COPYRIGHT("版权侵犯"),
        OTHER("其他");
        
        private final String description;
        
        ReportType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 举报状态枚举
     */
    public enum ReportStatus {
        PENDING("待处理"),
        REVIEWING("审核中"),
        RESOLVED("已解决"),
        REJECTED("已拒绝");
        
        private final String description;
        
        ReportStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数
     */
    public UserReport(Long reporterId, Long targetUserId, ReportType reportType, String reason) {
        this.reporterId = reporterId;
        this.targetUserId = targetUserId;
        this.reportType = reportType;
        this.reason = reason;
        this.status = ReportStatus.PENDING;
        this.isUrgent = false;
    }
    
    /**
     * 标记为已处理
     */
    public void markAsHandled(Long handlerId, ReportStatus newStatus) {
        this.handledBy = handlerId;
        this.handledAt = LocalDateTime.now();
        this.status = newStatus;
    }

    /**
     * 标记为已处理（带备注）
     */
    public void markAsHandled(Long handlerId, ReportStatus newStatus, String handleNote) {
        this.handledBy = handlerId;
        this.handledAt = LocalDateTime.now();
        this.status = newStatus;
        this.handleNote = handleNote;
    }
    
    /**
     * 检查是否已处理
     */
    public boolean isHandled() {
        return handledAt != null && (status == ReportStatus.RESOLVED || status == ReportStatus.REJECTED);
    }
    
    /**
     * 检查是否为有效的举报
     */
    public boolean isValid() {
        return reporterId != null && targetUserId != null && 
               !reporterId.equals(targetUserId) && reportType != null;
    }
}
