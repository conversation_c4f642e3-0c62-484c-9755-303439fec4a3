package com.implatform.user.repository;

import com.implatform.user.entity.QrLoginSession;
import com.implatform.user.enums.QrLoginStatus;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 二维码登录会话自定义Repository接口
 * 用于实现Redis不支持的复杂查询操作
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
public interface QrLoginSessionCustomRepository {

    /**
     * 删除过期的会话
     * 手动实现，因为Redis不支持deleteByExpiresAtBefore查询派生
     *
     * @param expireTime 过期时间
     * @return 删除的会话数量
     */
    Mono<Integer> deleteExpiredSessions(Instant expireTime);

    /**
     * 查找过期的会话
     *
     * @param expireTime 过期时间
     * @return 过期的会话列表
     */
    Flux<QrLoginSession> findExpiredSessions(Instant expireTime);

    /**
     * 批量删除指定状态的会话
     *
     * @param statuses 要删除的状态列表
     * @return 删除的会话数量
     */
    Mono<Integer> deleteByStatuses(QrLoginStatus[] statuses);

    /**
     * 清理用户的旧会话（保留最新的N个）
     *
     * @param userId 用户ID
     * @param keepCount 保留的会话数量
     * @return 删除的会话数量
     */
    Mono<Integer> cleanupUserOldSessions(Long userId, int keepCount);
}
