package com.implatform.user.repository;

import com.implatform.user.entity.VerificationCode;
import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 验证码数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface VerificationCodeRepository extends ReactiveCrudRepository<VerificationCode, Long> {

    /**
     * 根据目标和验证码查找
     */
    Mono<VerificationCode> findByTargetAndCode(String target, String code);

    /**
     * 根据目标、验证码和状态查找
     */
    Mono<VerificationCode> findByTargetAndCodeAndStatus(String target, String code, VerificationCode.CodeStatus status);

    /**
     * 根据目标、验证码、用途和状态查找
     */
    Mono<VerificationCode> findByTargetAndCodeAndPurposeAndStatus(
            String target, String code, VerificationCode.Purpose purpose, VerificationCode.CodeStatus status);

    /**
     * 根据目标和用途查找最新的验证码
     */
    @Query("SELECT * FROM verification_codes WHERE target = :target AND purpose = :purpose ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<VerificationCode> findLatestByTargetAndPurpose(String target, VerificationCode.Purpose purpose, int limit, long offset);

    /**
     * 根据目标、用途和状态查找最新的验证码
     */
    @Query("SELECT v FROM VerificationCode v WHERE v.target = :target AND v.purpose = :purpose AND v.status = :status ORDER BY v.createdAt DESC")
    List<VerificationCode> findLatestByTargetAndPurposeAndStatus(@Param("target") String target, 
                                                               @Param("purpose") VerificationCode.Purpose purpose,
                                                               @Param("status") VerificationCode.CodeStatus status,
                                                               Pageable pageable);

    /**
     * 根据目标查找所有验证码
     */
    List<VerificationCode> findByTargetOrderByCreatedAtDesc(String target);

    /**
     * 根据目标和状态查找验证码
     */
    List<VerificationCode> findByTargetAndStatusOrderByCreatedAtDesc(String target, VerificationCode.CodeStatus status);

    /**
     * 根据用户ID查找验证码
     */
    List<VerificationCode> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 根据设备ID查找验证码
     */
    List<VerificationCode> findByDeviceIdOrderByCreatedAtDesc(String deviceId);

    /**
     * 根据客户端IP查找验证码
     */
    List<VerificationCode> findByClientIpOrderByCreatedAtDesc(String clientIp);

    /**
     * 查找过期的验证码
     */
    @Query("SELECT v FROM VerificationCode v WHERE v.expiresAt < :now AND v.status = :status")
    List<VerificationCode> findExpiredCodes(@Param("now") Instant now, @Param("status") VerificationCode.CodeStatus status);

    /**
     * 查找指定时间范围内的验证码
     */
    @Query("SELECT v FROM VerificationCode v WHERE v.createdAt BETWEEN :startTime AND :endTime ORDER BY v.createdAt DESC")
    List<VerificationCode> findCodesInTimeRange(@Param("startTime") Instant startTime, @Param("endTime") Instant endTime);

    /**
     * 统计目标在指定时间内的验证码数量
     */
    @Query("SELECT COUNT(v) FROM VerificationCode v WHERE v.target = :target AND v.createdAt BETWEEN :startTime AND :endTime")
    long countByTargetInTimeRange(@Param("target") String target, 
                                 @Param("startTime") Instant startTime, 
                                 @Param("endTime") Instant endTime);

    /**
     * 统计目标在指定时间内特定用途的验证码数量
     */
    @Query("SELECT COUNT(v) FROM VerificationCode v WHERE v.target = :target AND v.purpose = :purpose AND v.createdAt BETWEEN :startTime AND :endTime")
    long countByTargetAndPurposeInTimeRange(@Param("target") String target, 
                                           @Param("purpose") VerificationCode.Purpose purpose,
                                           @Param("startTime") Instant startTime, 
                                           @Param("endTime") Instant endTime);

    /**
     * 统计IP地址在指定时间内的验证码数量
     */
    @Query("SELECT COUNT(v) FROM VerificationCode v WHERE v.clientIp = :clientIp AND v.createdAt BETWEEN :startTime AND :endTime")
    long countByClientIpInTimeRange(@Param("clientIp") String clientIp, 
                                   @Param("startTime") Instant startTime, 
                                   @Param("endTime") Instant endTime);

    /**
     * 统计设备在指定时间内的验证码数量
     */
    @Query("SELECT COUNT(v) FROM VerificationCode v WHERE v.deviceId = :deviceId AND v.createdAt BETWEEN :startTime AND :endTime")
    long countByDeviceIdInTimeRange(@Param("deviceId") String deviceId, 
                                   @Param("startTime") Instant startTime, 
                                   @Param("endTime") Instant endTime);

    /**
     * 查找验证失败次数过多的验证码
     */
    @Query("SELECT v FROM VerificationCode v WHERE v.attemptCount >= v.maxAttempts AND v.status = :status")
    List<VerificationCode> findExhaustedCodes(@Param("status") VerificationCode.CodeStatus status);

    /**
     * 根据验证码类型分页查询
     */
    Page<VerificationCode> findByCodeTypeOrderByCreatedAtDesc(VerificationCode.CodeType codeType, Pageable pageable);

    /**
     * 根据用途分页查询
     */
    Page<VerificationCode> findByPurposeOrderByCreatedAtDesc(VerificationCode.Purpose purpose, Pageable pageable);

    /**
     * 根据状态分页查询
     */
    Page<VerificationCode> findByStatusOrderByCreatedAtDesc(VerificationCode.CodeStatus status, Pageable pageable);

    /**
     * 根据发送状态查询
     */
    List<VerificationCode> findBySendStatusOrderByCreatedAtDesc(VerificationCode.SendStatus sendStatus);

    /**
     * 查找发送失败的验证码
     */
    List<VerificationCode> findBySendStatusAndCreatedAtAfterOrderByCreatedAtDesc(
            VerificationCode.SendStatus sendStatus, Instant after);

    /**
     * 批量更新过期验证码状态
     */
    @Modifying
    @Query("UPDATE VerificationCode v SET v.status = :expiredStatus, v.updatedAt = :now WHERE v.expiresAt < :now AND v.status = :pendingStatus")
    int markExpiredCodes(@Param("expiredStatus") VerificationCode.CodeStatus expiredStatus,
                        @Param("pendingStatus") VerificationCode.CodeStatus pendingStatus,
                        @Param("now") Instant now);

    /**
     * 批量取消目标的待用验证码
     */
    @Modifying
    @Query("UPDATE VerificationCode v SET v.status = :cancelledStatus, v.updatedAt = :now WHERE v.target = :target AND v.purpose = :purpose AND v.status = :pendingStatus")
    int cancelPendingCodes(@Param("target") String target,
                          @Param("purpose") VerificationCode.Purpose purpose,
                          @Param("cancelledStatus") VerificationCode.CodeStatus cancelledStatus,
                          @Param("pendingStatus") VerificationCode.CodeStatus pendingStatus,
                          @Param("now") Instant now);

    /**
     * 删除过期的验证码
     */
    @Modifying
    @Query("DELETE FROM VerificationCode v WHERE v.expiresAt < :cutoffDate")
    int deleteExpiredCodes(@Param("cutoffDate") Instant cutoffDate);

    /**
     * 删除旧的验证码
     */
    @Modifying
    @Query("DELETE FROM VerificationCode v WHERE v.createdAt < :cutoffDate")
    int deleteOldCodes(@Param("cutoffDate") Instant cutoffDate);

    /**
     * 获取验证码统计信息
     */
    @Query("SELECT v.codeType, v.status, COUNT(v) FROM VerificationCode v GROUP BY v.codeType, v.status")
    List<Object[]> getCodeStatistics();

    /**
     * 获取验证码用途统计
     */
    @Query("SELECT v.purpose, COUNT(v) FROM VerificationCode v WHERE v.status = :status GROUP BY v.purpose")
    List<Object[]> getPurposeStatistics(@Param("status") VerificationCode.CodeStatus status);

    /**
     * 获取验证码成功率统计
     */
    @Query("SELECT v.purpose, " +
           "SUM(CASE WHEN v.status = :usedStatus THEN 1 ELSE 0 END) as used, " +
           "COUNT(v) as total " +
           "FROM VerificationCode v " +
           "WHERE v.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY v.purpose")
    List<Object[]> getSuccessRateStatistics(@Param("usedStatus") VerificationCode.CodeStatus usedStatus,
                                           @Param("startTime") Instant startTime,
                                           @Param("endTime") Instant endTime);

    /**
     * 查找可疑的验证码请求
     */
    @Query("SELECT v.clientIp, COUNT(v) as count FROM VerificationCode v " +
           "WHERE v.createdAt BETWEEN :startTime AND :endTime " +
           "GROUP BY v.clientIp " +
           "HAVING COUNT(v) > :threshold " +
           "ORDER BY COUNT(v) DESC")
    List<Object[]> findSuspiciousCodeRequests(@Param("startTime") Instant startTime,
                                             @Param("endTime") Instant endTime,
                                             @Param("threshold") long threshold);

    /**
     * 检查验证码是否存在
     */
    boolean existsByTargetAndCodeAndStatus(String target, String code, VerificationCode.CodeStatus status);

    /**
     * 检查目标是否有有效的验证码
     */
    @Query("SELECT COUNT(v) > 0 FROM VerificationCode v WHERE v.target = :target AND v.purpose = :purpose AND v.status = :status AND v.expiresAt > :now")
    boolean hasValidCode(@Param("target") String target,
                        @Param("purpose") VerificationCode.Purpose purpose,
                        @Param("status") VerificationCode.CodeStatus status,
                        @Param("now") Instant now);

    /**
     * 获取目标最新的有效验证码
     */
    @Query("SELECT v FROM VerificationCode v WHERE v.target = :target AND v.purpose = :purpose AND v.status = :status AND v.expiresAt > :now ORDER BY v.createdAt DESC")
    Optional<VerificationCode> findLatestValidCode(@Param("target") String target,
                                                  @Param("purpose") VerificationCode.Purpose purpose,
                                                  @Param("status") VerificationCode.CodeStatus status,
                                                  @Param("now") Instant now);
}
