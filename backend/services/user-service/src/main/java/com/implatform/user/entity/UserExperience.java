package com.implatform.user.entity;

import com.implatform.user.enums.ExperienceType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 用户经验记录实体
 * 记录用户所有经验值变动的详细信息
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_experience"),
    @Index(name = "idx_user_exp_earned_at", columnList = "earned_at"),
    @Index(name = "idx_user_exp_type", columnList = "exp_type"),
    @Index(name = "idx_user_exp_user_date", columnList = "user_id, earned_at"),
    @Index(name = "idx_user_exp_user_type", columnList = "user_id, exp_type"),
    @Index(name = "idx_user_exp_amount", columnList = "exp_amount"),
    @Index(name = "idx_user_exp_source", columnList = "source_type, source_id")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserExperience {

    /**
     * 主键ID
     */
    @Id
        @Column("id")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    @Column("user_id")
    private Long userId;

    /**
     * 经验值数量（可为负数，表示扣除）
     */
    @NotNull(message = "Experience amount cannot be null")
    @Column("exp_amount")
    private Long expAmount;

    /**
     * 经验类型
     */
    @NotNull(message = "Experience type cannot be null")
        @Column("exp_type")
    private ExperienceType expType;

    /**
     * 获得原因
     */
    @NotBlank(message = "Reason cannot be blank")
    @Size(max = 200, message = "Reason cannot exceed 200 characters")
    @Column("reason")
    private String reason;

    /**
     * 详细描述
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    @Column("description")
    private String description;

    /**
     * 来源类型（如：MESSAGE, GROUP, FILE, LIKE等）
     */
    @Size(max = 50, message = "Source type cannot exceed 50 characters")
    @Column("source_type")
    private String sourceType;

    /**
     * 来源ID（如：消息ID、群组ID、文件ID等）
     */
    @Column("source_id")
    private Long sourceId;

    /**
     * 经验倍率（百分比）
     */
    @Builder.Default
    @Column("exp_multiplier")
    private Integer expMultiplier = 100;

    /**
     * 基础经验值（未计算倍率前）
     */
    @Column("base_exp_amount")
    private Long baseExpAmount;

    /**
     * 获得时间
     */
    @NotNull(message = "Earned time cannot be null")
    @CreatedDate
    @Column("earned_at")
    private Instant earnedAt;

    /**
     * 是否为每日首次获得
     */
    @Builder.Default
    @Column("is_daily_first")
    private Boolean isDailyFirst = false;

    /**
     * 是否为连续登录奖励
     */
    @Builder.Default
    @Column("is_consecutive_bonus")
    private Boolean isConsecutiveBonus = false;

    /**
     * 连续天数（用于连续登录奖励）
     */
    @Column("consecutive_days")
    private Integer consecutiveDays;

    /**
     * 是否为VIP加成
     */
    @Builder.Default
    @Column("is_vip_bonus")
    private Boolean isVipBonus = false;

    /**
     * 是否为活动加成
     */
    @Builder.Default
    @Column("is_event_bonus")
    private Boolean isEventBonus = false;

    /**
     * 活动ID（如果是活动加成）
     */
    @Column("event_id")
    private Long eventId;

    /**
     * 客户端IP地址
     */
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    @Column("client_ip")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    @Column("user_agent")
    private String userAgent;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "Notes cannot exceed 1000 characters")
    @Column("notes")
    private String notes;

    /**
     * 用户实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 检查是否为正向经验
     * 
     * @return 是否为正向经验
     */
    public boolean isPositiveExp() {
        return expAmount != null && expAmount > 0;
    }

    /**
     * 检查是否为负向经验（扣除）
     * 
     * @return 是否为负向经验
     */
    public boolean isNegativeExp() {
        return expAmount != null && expAmount < 0;
    }

    /**
     * 获取绝对经验值
     * 
     * @return 绝对经验值
     */
    public Long getAbsoluteExpAmount() {
        return expAmount != null ? Math.abs(expAmount) : 0L;
    }

    /**
     * 检查是否有加成
     * 
     * @return 是否有加成
     */
    public boolean hasBonus() {
        return isVipBonus || isEventBonus || isConsecutiveBonus || 
               (expMultiplier != null && expMultiplier > 100);
    }

    /**
     * 获取加成类型描述
     * 
     * @return 加成类型描述
     */
    public String getBonusDescription() {
        if (!hasBonus()) {
            return "无加成";
        }
        
        StringBuilder desc = new StringBuilder();
        if (isVipBonus) {
            desc.append("VIP加成 ");
        }
        if (isEventBonus) {
            desc.append("活动加成 ");
        }
        if (isConsecutiveBonus) {
            desc.append("连续登录加成 ");
        }
        if (expMultiplier != null && expMultiplier > 100) {
            desc.append("倍率加成(").append(expMultiplier).append("%) ");
        }
        
        return desc.toString().trim();
    }

    /**
     * 设置为每日首次获得
     * 
     * @param isDailyFirst 是否为每日首次
     */
    public void setAsDailyFirst(boolean isDailyFirst) {
        this.isDailyFirst = isDailyFirst;
        if (isDailyFirst) {
            this.reason = this.reason + " (每日首次)";
        }
    }

    /**
     * 设置连续登录奖励
     * 
     * @param consecutiveDays 连续天数
     */
    public void setAsConsecutiveBonus(Integer consecutiveDays) {
        this.isConsecutiveBonus = true;
        this.consecutiveDays = consecutiveDays;
        this.reason = this.reason + " (连续" + consecutiveDays + "天)";
    }

    /**
     * 设置VIP加成
     */
    public void setAsVipBonus() {
        this.isVipBonus = true;
        this.reason = this.reason + " (VIP加成)";
    }

    /**
     * 设置活动加成
     * 
     * @param eventId 活动ID
     */
    public void setAsEventBonus(Long eventId) {
        this.isEventBonus = true;
        this.eventId = eventId;
        this.reason = this.reason + " (活动加成)";
    }

    /**
     * 计算实际经验值（考虑倍率）
     * 
     * @param baseAmount 基础经验值
     * @param multiplier 倍率
     * @return 实际经验值
     */
    public static Long calculateActualExp(Long baseAmount, Integer multiplier) {
        if (baseAmount == null || baseAmount == 0) {
            return 0L;
        }
        if (multiplier == null || multiplier <= 0) {
            return baseAmount;
        }
        return baseAmount * multiplier / 100;
    }

    /**
     * 创建经验记录
     * 
     * @param userId 用户ID
     * @param expAmount 经验值
     * @param expType 经验类型
     * @param reason 获得原因
     * @return 经验记录
     */
    public static UserExperience create(Long userId, Long expAmount, ExperienceType expType, String reason) {
        return UserExperience.builder()
                .userId(userId)
                .expAmount(expAmount)
                .expType(expType)
                .reason(reason)
                .baseExpAmount(expAmount)
                .earnedAt(Instant.now())
                .build();
    }

    /**
     * 创建带来源的经验记录
     * 
     * @param userId 用户ID
     * @param expAmount 经验值
     * @param expType 经验类型
     * @param reason 获得原因
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @return 经验记录
     */
    public static UserExperience createWithSource(Long userId, Long expAmount, ExperienceType expType, 
                                                 String reason, String sourceType, Long sourceId) {
        return UserExperience.builder()
                .userId(userId)
                .expAmount(expAmount)
                .expType(expType)
                .reason(reason)
                .sourceType(sourceType)
                .sourceId(sourceId)
                .baseExpAmount(expAmount)
                .earnedAt(Instant.now())
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserExperience that = (UserExperience) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserExperience{" +
                "id=" + id +
                ", userId=" + userId +
                ", expAmount=" + expAmount +
                ", expType=" + expType +
                ", reason='" + reason + '\'' +
                ", earnedAt=" + earnedAt +
                '}';
    }
}
