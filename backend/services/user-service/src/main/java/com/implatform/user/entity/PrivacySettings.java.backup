package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 隐私设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_privacy_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrivacySettings {
    
    @Id
    @Column(name = "user_id")
    private Long userId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "phone_visibility", nullable = false, length = 20)
    private PrivacyLevel phoneVisibility = PrivacyLevel.NOBODY;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "last_seen_visibility", nullable = false, length = 20)
    private PrivacyLevel lastSeenVisibility = PrivacyLevel.EVERYBODY;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "profile_photo_visibility", nullable = false, length = 20)
    private PrivacyLevel profilePhotoVisibility = PrivacyLevel.EVERYBODY;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "message_permission", nullable = false, length = 20)
    private PrivacyLevel messagePermission = PrivacyLevel.EVERYBODY;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "group_invite_permission", nullable = false, length = 20)
    private PrivacyLevel groupInvitePermission = PrivacyLevel.CONTACTS;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "phone_discovery", nullable = false, length = 20)
    private PrivacyLevel phoneDiscovery = PrivacyLevel.CONTACTS;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "username_discovery", nullable = false, length = 20)
    private PrivacyLevel usernameDiscovery = PrivacyLevel.EVERYBODY;
    
    @Column(name = "allow_forwarding", nullable = false)
    private Boolean allowForwarding = true;
    
    @Column(name = "show_read_receipts", nullable = false)
    private Boolean showReadReceipts = true;
    
    @Column(name = "show_online_status", nullable = false)
    private Boolean showOnlineStatus = true;
    
    @Column(name = "allow_voice_calls", nullable = false)
    private Boolean allowVoiceCalls = true;
    
    @Column(name = "allow_video_calls", nullable = false)
    private Boolean allowVideoCalls = true;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    /**
     * 隐私级别枚举
     */
    public enum PrivacyLevel {
        EVERYBODY("所有人"),
        CONTACTS("联系人"),
        NOBODY("没有人");
        
        private final String description;
        
        PrivacyLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数 - 创建默认隐私设置
     */
    public PrivacySettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    /**
     * 检查是否允许某用户查看手机号
     */
    public boolean canViewPhone(boolean isContact) {
        return switch (phoneVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户查看最后在线时间
     */
    public boolean canViewLastSeen(boolean isContact) {
        return switch (lastSeenVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户查看头像
     */
    public boolean canViewProfilePhoto(boolean isContact) {
        return switch (profilePhotoVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户发送消息
     */
    public boolean canSendMessage(boolean isContact) {
        return switch (messagePermission) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户邀请加入群组
     */
    public boolean canInviteToGroup(boolean isContact) {
        return switch (groupInvitePermission) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
}
