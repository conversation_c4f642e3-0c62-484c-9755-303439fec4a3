package com.implatform.user.entity;

import com.implatform.user.enums.AvatarStatus;
import com.implatform.user.enums.ModerationPriority;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 头像审核请求实体
 * 处理头像审核工作流程，包括审核状态和管理员操作记录
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "avatar_moderation_request", indexes = {
    @Index(name = "idx_moderation_user_id", columnList = "user_id"),
    @Index(name = "idx_moderation_status", columnList = "status"),
    @Index(name = "idx_moderation_submitted_at", columnList = "submitted_at"),
    @Index(name = "idx_moderation_priority", columnList = "priority"),
    @Index(name = "idx_moderation_admin_id", columnList = "admin_id"),
    @Index(name = "idx_moderation_status_priority", columnList = "status, priority")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AvatarModerationRequest {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 头像URL
     */
    @NotBlank(message = "Avatar URL cannot be blank")
    @Size(max = 500, message = "Avatar URL cannot exceed 500 characters")
    @Column(name = "avatar_url", nullable = false, length = 500)
    private String avatarUrl;

    /**
     * 审核状态
     */
    @NotNull(message = "Status cannot be null")
    @Enumerated(EnumType.STRING)
    @Builder.Default
    @Column(name = "status", nullable = false, length = 20)
    private AvatarStatus status = AvatarStatus.PENDING;

    /**
     * 审核优先级
     */
    @NotNull(message = "Priority cannot be null")
    @Enumerated(EnumType.STRING)
    @Builder.Default
    @Column(name = "priority", nullable = false, length = 20)
    private ModerationPriority priority = ModerationPriority.NORMAL;

    /**
     * 提交时间
     */
    @NotNull(message = "Submitted time cannot be null")
    @CreationTimestamp
    @Column(name = "submitted_at", nullable = false)
    private Instant submittedAt;

    /**
     * 审核时间
     */
    @Column(name = "reviewed_at")
    private Instant reviewedAt;

    /**
     * 审核管理员ID
     */
    @Column(name = "admin_id")
    private Long adminId;

    /**
     * 拒绝原因
     */
    @Size(max = 1000, message = "Rejection reason cannot exceed 1000 characters")
    @Column(name = "rejection_reason", length = 1000)
    private String rejectionReason;

    /**
     * 审核备注
     */
    @Size(max = 2000, message = "Moderation notes cannot exceed 2000 characters")
    @Column(name = "moderation_notes", length = 2000)
    private String moderationNotes;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 原始文件名
     */
    @Size(max = 255, message = "Original file name cannot exceed 255 characters")
    @Column(name = "original_file_name", length = 255)
    private String originalFileName;

    /**
     * 内容类型
     */
    @Size(max = 100, message = "Content type cannot exceed 100 characters")
    @Column(name = "content_type", length = 100)
    private String contentType;

    /**
     * 客户端IP地址
     */
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    @Column(name = "client_ip", length = 45)
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 自动审核标记
     */
    @Builder.Default
    @Column(name = "auto_approved", nullable = false)
    private Boolean autoApproved = false;

    /**
     * 审核耗时（毫秒）
     */
    @Column(name = "review_duration_ms")
    private Long reviewDurationMs;

    /**
     * 违规标签（JSON格式）
     */
    @Size(max = 1000, message = "Violation tags cannot exceed 1000 characters")
    @Column(name = "violation_tags", length = 1000)
    private String violationTags;

    /**
     * 置信度分数（0-100）
     */
    @Column(name = "confidence_score")
    private Integer confidenceScore;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 用户实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 审核管理员实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "admin_id", insertable = false, updatable = false)
    private User admin;

    /**
     * 批准审核请求
     * 
     * @param adminId 审核管理员ID
     * @param notes 审核备注
     */
    public void approve(Long adminId, String notes) {
        this.status = AvatarStatus.APPROVED;
        this.adminId = adminId;
        this.reviewedAt = Instant.now();
        this.moderationNotes = notes;
        this.reviewDurationMs = calculateReviewDuration();
    }

    /**
     * 拒绝审核请求
     * 
     * @param adminId 审核管理员ID
     * @param rejectionReason 拒绝原因
     * @param notes 审核备注
     */
    public void reject(Long adminId, String rejectionReason, String notes) {
        this.status = AvatarStatus.REJECTED;
        this.adminId = adminId;
        this.reviewedAt = Instant.now();
        this.rejectionReason = rejectionReason;
        this.moderationNotes = notes;
        this.reviewDurationMs = calculateReviewDuration();
    }

    /**
     * 设置为自动批准
     * 
     * @param confidenceScore 置信度分数
     */
    public void autoApprove(Integer confidenceScore) {
        this.status = AvatarStatus.APPROVED;
        this.autoApproved = true;
        this.reviewedAt = Instant.now();
        this.confidenceScore = confidenceScore;
        this.reviewDurationMs = calculateReviewDuration();
        this.moderationNotes = "Auto-approved by system";
    }

    /**
     * 设置优先级
     * 
     * @param priority 优先级
     */
    public void setPriority(ModerationPriority priority) {
        this.priority = priority;
    }

    /**
     * 添加违规标签
     * 
     * @param tags 违规标签
     */
    public void addViolationTags(String tags) {
        this.violationTags = tags;
    }

    /**
     * 计算审核耗时
     * 
     * @return 审核耗时（毫秒）
     */
    private Long calculateReviewDuration() {
        if (submittedAt != null && reviewedAt != null) {
            return reviewedAt.toEpochMilli() - submittedAt.toEpochMilli();
        }
        return null;
    }

    /**
     * 检查是否已审核
     * 
     * @return 是否已审核
     */
    public boolean isReviewed() {
        return reviewedAt != null && !AvatarStatus.PENDING.equals(status);
    }

    /**
     * 检查是否被拒绝
     * 
     * @return 是否被拒绝
     */
    public boolean isRejected() {
        return AvatarStatus.REJECTED.equals(status);
    }

    /**
     * 检查是否已批准
     * 
     * @return 是否已批准
     */
    public boolean isApproved() {
        return AvatarStatus.APPROVED.equals(status);
    }

    /**
     * 检查是否待审核
     * 
     * @return 是否待审核
     */
    public boolean isPending() {
        return AvatarStatus.PENDING.equals(status);
    }

    /**
     * 检查是否高优先级
     * 
     * @return 是否高优先级
     */
    public boolean isHighPriority() {
        return ModerationPriority.HIGH.equals(priority) || ModerationPriority.URGENT.equals(priority);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AvatarModerationRequest that = (AvatarModerationRequest) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "AvatarModerationRequest{" +
                "id=" + id +
                ", userId=" + userId +
                ", status=" + status +
                ", priority=" + priority +
                ", submittedAt=" + submittedAt +
                ", reviewedAt=" + reviewedAt +
                ", autoApproved=" + autoApproved +
                '}';
    }
}
