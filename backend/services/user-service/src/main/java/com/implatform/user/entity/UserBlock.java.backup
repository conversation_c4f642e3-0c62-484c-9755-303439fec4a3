package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

/**
 * 用户屏蔽关系实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_blocks", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"blocker_id", "blocked_id"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBlock {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "blocker_id", nullable = false)
    private Long blockerId;
    
    @Column(name = "blocked_id", nullable = false)
    private Long blockedId;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 屏蔽者用户关联（可选，用于查询优化）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blocker_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User blocker;
    
    /**
     * 被屏蔽者用户关联（可选，用于查询优化）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blocked_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User blocked;
    
    /**
     * 构造函数
     */
    public UserBlock(Long blockerId, Long blockedId) {
        this.blockerId = blockerId;
        this.blockedId = blockedId;
    }
    
    /**
     * 检查是否为有效的屏蔽关系
     */
    public boolean isValid() {
        return blockerId != null && blockedId != null && !blockerId.equals(blockedId);
    }
}
