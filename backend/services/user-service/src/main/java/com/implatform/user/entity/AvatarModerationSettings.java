package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 头像审核系统配置实体
 * 管理头像审核系统的全局配置参数
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("avatar_moderation_settings")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AvatarModerationSettings {

    /**
     * 主键ID
     */
    @Id
        @Column("id")
    private Long id;

    /**
     * 是否启用审核
     */
    @NotNull(message = "Moderation enabled cannot be null")
    @Builder.Default
    @Column("moderation_enabled")
    private Boolean moderationEnabled = true;

    /**
     * 是否启用自动批准
     */
    @NotNull(message = "Auto approval enabled cannot be null")
    @Builder.Default
    @Column("auto_approval_enabled")
    private Boolean autoApprovalEnabled = false;

    /**
     * 最大文件大小（字节）
     */
    @NotNull(message = "Max file size cannot be null")
    @Min(value = 1024, message = "Max file size must be at least 1KB")
    @Builder.Default
    @Column("max_file_size_bytes")
    private Long maxFileSizeBytes = 5242880L; // 5MB

    /**
     * 允许的文件格式（逗号分隔）
     */
    @NotNull(message = "Allowed formats cannot be null")
    @Size(max = 200, message = "Allowed formats cannot exceed 200 characters")
    @Builder.Default
    @Column("allowed_formats")
    private String allowedFormats = "jpg,jpeg,png,gif,webp";

    /**
     * 是否需要审核批准
     */
    @NotNull(message = "Requires approval cannot be null")
    @Builder.Default
    @Column("requires_approval")
    private Boolean requiresApproval = true;

    /**
     * 自动批准置信度阈值（0-100）
     */
    @Builder.Default
    @Column("auto_approval_threshold")
    private Integer autoApprovalThreshold = 85;

    /**
     * 审核超时时间（小时）
     */
    @Builder.Default
    @Column("review_timeout_hours")
    private Integer reviewTimeoutHours = 24;

    /**
     * 最大宽度（像素）
     */
    @Builder.Default
    @Column("max_width_pixels")
    private Integer maxWidthPixels = 1024;

    /**
     * 最大高度（像素）
     */
    @Builder.Default
    @Column("max_height_pixels")
    private Integer maxHeightPixels = 1024;

    /**
     * 最小宽度（像素）
     */
    @Builder.Default
    @Column("min_width_pixels")
    private Integer minWidthPixels = 64;

    /**
     * 最小高度（像素）
     */
    @Builder.Default
    @Column("min_height_pixels")
    private Integer minHeightPixels = 64;

    /**
     * 是否启用内容扫描
     */
    @Builder.Default
    @Column("content_scan_enabled")
    private Boolean contentScanEnabled = true;

    /**
     * 是否启用人脸检测
     */
    @Builder.Default
    @Column("face_detection_enabled")
    private Boolean faceDetectionEnabled = false;

    /**
     * 是否启用NSFW检测
     */
    @Builder.Default
    @Column("nsfw_detection_enabled")
    private Boolean nsfwDetectionEnabled = true;

    /**
     * NSFW检测阈值（0-100）
     */
    @Builder.Default
    @Column("nsfw_threshold")
    private Integer nsfwThreshold = 70;

    /**
     * 每日上传限制
     */
    @Builder.Default
    @Column("daily_upload_limit")
    private Integer dailyUploadLimit = 5;

    /**
     * 历史记录保留天数
     */
    @Builder.Default
    @Column("history_retention_days")
    private Integer historyRetentionDays = 365;

    /**
     * 是否启用水印
     */
    @Builder.Default
    @Column("watermark_enabled")
    private Boolean watermarkEnabled = false;

    /**
     * 水印文本
     */
    @Size(max = 100, message = "Watermark text cannot exceed 100 characters")
    @Column("watermark_text")
    private String watermarkText;

    /**
     * 是否启用压缩
     */
    @Builder.Default
    @Column("compression_enabled")
    private Boolean compressionEnabled = true;

    /**
     * 压缩质量（0-100）
     */
    @Builder.Default
    @Column("compression_quality")
    private Integer compressionQuality = 85;

    /**
     * 配置是否激活
     */
    @NotNull(message = "Is active cannot be null")
    @Builder.Default
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 配置版本号
     */
    @Builder.Default
    @Column("config_version")
    private Integer configVersion = 1;

    /**
     * 最后修改人ID
     */
    @Column("last_modified_by")
    private Long lastModifiedBy;

    /**
     * 修改备注
     */
    @Size(max = 500, message = "Modification notes cannot exceed 500 characters")
    @Column("modification_notes")
    private String modificationNotes;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 最后修改人实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "last_modified_by", insertable = false, updatable = false)
    private User lastModifier;

    /**
     * 更新配置
     * 
     * @param modifierId 修改人ID
     * @param notes 修改备注
     */
    public void updateConfiguration(Long modifierId, String notes) {
        this.lastModifiedBy = modifierId;
        this.modificationNotes = notes;
        this.configVersion = this.configVersion + 1;
    }

    /**
     * 启用审核
     * 
     * @param modifierId 修改人ID
     */
    public void enableModeration(Long modifierId) {
        this.moderationEnabled = true;
        this.requiresApproval = true;
        updateConfiguration(modifierId, "Enabled avatar moderation");
    }

    /**
     * 禁用审核
     * 
     * @param modifierId 修改人ID
     */
    public void disableModeration(Long modifierId) {
        this.moderationEnabled = false;
        this.requiresApproval = false;
        updateConfiguration(modifierId, "Disabled avatar moderation");
    }

    /**
     * 启用自动批准
     * 
     * @param modifierId 修改人ID
     * @param threshold 置信度阈值
     */
    public void enableAutoApproval(Long modifierId, Integer threshold) {
        this.autoApprovalEnabled = true;
        this.autoApprovalThreshold = threshold;
        updateConfiguration(modifierId, "Enabled auto approval with threshold: " + threshold);
    }

    /**
     * 禁用自动批准
     * 
     * @param modifierId 修改人ID
     */
    public void disableAutoApproval(Long modifierId) {
        this.autoApprovalEnabled = false;
        updateConfiguration(modifierId, "Disabled auto approval");
    }

    /**
     * 检查文件格式是否允许
     * 
     * @param format 文件格式
     * @return 是否允许
     */
    public boolean isFormatAllowed(String format) {
        if (allowedFormats == null || format == null) {
            return false;
        }
        return allowedFormats.toLowerCase().contains(format.toLowerCase());
    }

    /**
     * 检查文件大小是否符合要求
     * 
     * @param fileSize 文件大小
     * @return 是否符合要求
     */
    public boolean isFileSizeValid(Long fileSize) {
        return fileSize != null && fileSize <= maxFileSizeBytes && fileSize > 0;
    }

    /**
     * 检查图片尺寸是否符合要求
     * 
     * @param width 宽度
     * @param height 高度
     * @return 是否符合要求
     */
    public boolean isDimensionValid(Integer width, Integer height) {
        if (width == null || height == null) {
            return false;
        }
        return width >= minWidthPixels && width <= maxWidthPixels &&
               height >= minHeightPixels && height <= maxHeightPixels;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AvatarModerationSettings that = (AvatarModerationSettings) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "AvatarModerationSettings{" +
                "id=" + id +
                ", moderationEnabled=" + moderationEnabled +
                ", autoApprovalEnabled=" + autoApprovalEnabled +
                ", maxFileSizeBytes=" + maxFileSizeBytes +
                ", allowedFormats='" + allowedFormats + '\'' +
                ", requiresApproval=" + requiresApproval +
                ", configVersion=" + configVersion +
                '}';
    }
}
