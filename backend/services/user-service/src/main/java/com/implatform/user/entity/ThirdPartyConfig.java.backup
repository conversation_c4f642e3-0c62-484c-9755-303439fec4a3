package com.implatform.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.implatform.user.enums.ThirdPartyProvider;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 第三方登录配置实体类 - IM平台第三方登录平台配置信息
 *
 * <p><strong>实体概述</strong>：
 * 本实体类用于存储各个第三方登录平台的配置信息，包括应用ID、应用密钥、回调地址等。
 * 支持动态配置管理，可以通过管理后台动态启用或禁用特定的第三方登录平台。
 *
 * <p><strong>核心功能</strong>：
 * <ul>
 *   <li><strong>配置管理</strong>：统一管理各平台的应用配置信息</li>
 *   <li><strong>安全存储</strong>：应用密钥等敏感信息加密存储</li>
 *   <li><strong>动态控制</strong>：支持动态启用或禁用特定平台</li>
 *   <li><strong>扩展配置</strong>：支持JSON格式的扩展配置信息</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "third_party_configs", indexes = {
    @Index(name = "idx_provider_type", columnList = "provider_type")
}, uniqueConstraints = {
    @UniqueConstraint(name = "uk_provider_type", columnNames = {"provider_type"})
})
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class ThirdPartyConfig {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 第三方平台类型
     */
    @NotNull(message = "第三方平台类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "provider_type", nullable = false, length = 20)
    private ThirdPartyProvider providerType;
    
    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    @Size(max = 100, message = "应用ID长度不能超过100个字符")
    @Column(name = "app_id", nullable = false, length = 100)
    private String appId;
    
    /**
     * 应用密钥（加密存储）
     */
    @JsonIgnore
    @NotBlank(message = "应用密钥不能为空")
    @Size(max = 200, message = "应用密钥长度不能超过200个字符")
    @Column(name = "app_secret", nullable = false, length = 200)
    private String appSecret;
    
    /**
     * 回调地址
     */
    @Size(max = 500, message = "回调地址长度不能超过500个字符")
    @Column(name = "redirect_uri", length = 500)
    private String redirectUri;
    
    /**
     * 授权范围
     */
    @Size(max = 200, message = "授权范围长度不能超过200个字符")
    @Column(length = 200)
    private String scope;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;
    
    /**
     * 其他配置信息（JSON格式）
     */
    @Column(name = "config_data", columnDefinition = "JSON")
    private String configData;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 启用配置
     */
    public void enable() {
        this.isEnabled = true;
    }
    
    /**
     * 禁用配置
     */
    public void disable() {
        this.isEnabled = false;
    }
    
    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return isEnabled && appId != null && !appId.trim().isEmpty() 
               && appSecret != null && !appSecret.trim().isEmpty();
    }
    
    /**
     * 获取完整的回调地址
     */
    public String getFullRedirectUri() {
        if (redirectUri != null && !redirectUri.trim().isEmpty()) {
            return redirectUri;
        }
        // 如果没有配置回调地址，使用默认格式
        return String.format("/api/v1/auth/third-party/%s/callback", 
                           providerType.getCode());
    }
    
    /**
     * 获取授权范围，如果未配置则使用默认值
     */
    public String getEffectiveScope() {
        if (scope != null && !scope.trim().isEmpty()) {
            return scope;
        }
        return providerType.getDefaultScope();
    }
}
