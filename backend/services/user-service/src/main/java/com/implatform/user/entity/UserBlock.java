package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户屏蔽关系实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_blocks"))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBlock {
    
    @Id
        private Long id;
    
    @Column("blocker_id")
    private Long blockerId;
    
    @Column("blocked_id")
    private Long blockedId;
    
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 屏蔽者用户关联（可选，用于查询优化）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blocker_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User blocker;
    
    /**
     * 被屏蔽者用户关联（可选，用于查询优化）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blocked_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User blocked;
    
    /**
     * 构造函数
     */
    public UserBlock(Long blockerId, Long blockedId) {
        this.blockerId = blockerId;
        this.blockedId = blockedId;
    }
    
    /**
     * 检查是否为有效的屏蔽关系
     */
    public boolean isValid() {
        return blockerId != null && blockedId != null && !blockerId.equals(blockedId);
    }
}
