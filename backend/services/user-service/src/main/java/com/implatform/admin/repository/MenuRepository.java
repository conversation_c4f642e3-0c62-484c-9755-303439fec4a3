package com.implatform.admin.repository;

import org.springframework.data.r2dbc.repository.Modifying;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.admin.entity.Menu;

/**
 * 菜单数据访问层 - R2DBC响应式版本
 *
 * <AUTHOR> Platform Team
 */
@Repository
public interface MenuRepository extends ReactiveCrudRepository<Menu, String> {

    /**
     * 根据菜单路径查找菜单
     */
    Mono<Menu> findByPath(String path);

    /**
     * 检查菜单路径是否存在
     */
    Mono<Boolean> existsByPath(String path);

    /**
     * 检查菜单路径是否存在（排除指定菜单）
     */
    @Query("SELECT COUNT(*) > 0 FROM menus WHERE path = :path AND id != :id")
    Mono<Boolean> existsByPathAndIdNot(String path, String id);

    /**
     * 根据菜单名称查找菜单
     */
    Mono<Menu> findByName(String name);

    /**
     * 检查菜单名称是否存在
     */
    Mono<Boolean> existsByName(String name);

    /**
     * 检查菜单名称是否存在（排除指定菜单）
     */
    @Query("SELECT COUNT(*) > 0 FROM menus WHERE name = :name AND id != :id")
    Mono<Boolean> existsByNameAndIdNot(String name, String id);

    /**
     * 根据菜单类型查找菜单
     */
    Flux<Menu> findByType(Menu.MenuType type);

    /**
     * 根据菜单类型分页查找菜单
     */
    @Query("SELECT * FROM menus WHERE type = :type ORDER BY order_num ASC LIMIT :limit OFFSET :offset")
    Flux<Menu> findByTypeOrderByOrderNumAsc(Menu.MenuType type, int limit, long offset);

    /**
     * 根据状态查找菜单
     */
    Flux<Menu> findByStatus(Menu.MenuStatus status);

    /**
     * 根据状态分页查找菜单
     */
    @Query("SELECT * FROM menus WHERE status = :status ORDER BY order_num ASC LIMIT :limit OFFSET :offset")
    Flux<Menu> findByStatusOrderByOrderNumAsc(Menu.MenuStatus status, int limit, long offset);

    /**
     * 查找可见菜单
     */
    Flux<Menu> findByVisibleTrue();

    /**
     * 查找不可见菜单
     */
    Flux<Menu> findByVisibleFalse();

    /**
     * 查找根菜单（无父菜单）
     */
    Flux<Menu> findByParentIdIsNull();

    /**
     * 查找根菜单（按排序号排序）
     */
    Flux<Menu> findByParentIdIsNullOrderByOrderNumAsc();

    /**
     * 根据父菜单ID查找子菜单
     */
    Flux<Menu> findByParentId(String parentId);

    /**
     * 根据父菜单ID查找子菜单（按排序号排序）
     */
    Flux<Menu> findByParentIdOrderByOrderNumAsc(String parentId);

    /**
     * 查找非系统内置菜单
     */
    List<Menu> findByIsSystemFalse();

    /**
     * 查找系统内置菜单
     */
    List<Menu> findByIsSystemTrue();

    /**
     * 查找外链菜单
     */
    List<Menu> findByIsExternalTrue();

    /**
     * 根据权限编码查找菜单
     */
    @Query("SELECT m FROM Menu m WHERE :permissionCode MEMBER OF m.permissions")
    List<Menu> findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据权限编码列表查找菜单
     */
    @Query("SELECT DISTINCT m FROM Menu m WHERE EXISTS " +
           "(SELECT 1 FROM m.permissions p WHERE p IN :permissionCodes)")
    List<Menu> findByPermissionCodes(@Param("permissionCodes") List<String> permissionCodes);

    /**
     * 根据用户权限查找可访问菜单
     */
    @Query("SELECT DISTINCT m FROM Menu m WHERE " +
           "m.status = 'ACTIVE' AND m.visible = true AND " +
           "(m.permissions IS EMPTY OR EXISTS " +
           "(SELECT 1 FROM m.permissions p WHERE p IN :userPermissions))")
    List<Menu> findAccessibleMenus(@Param("userPermissions") List<String> userPermissions);

    /**
     * 搜索菜单（名称、路径模糊匹配）
     */
    @Query("SELECT m FROM Menu m WHERE " +
           "(:keyword IS NULL OR :keyword = '' OR " +
           "LOWER(m.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(m.path) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Menu> searchMenus(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据排序号查找菜单
     */
    List<Menu> findAllByOrderByOrderNumAsc();

    /**
     * 构建菜单树（根菜单及其子菜单）
     */
    @Query("SELECT m FROM Menu m WHERE m.parentId IS NULL AND m.status = 'ACTIVE' ORDER BY m.orderNum ASC")
    List<Menu> findRootMenus();

    /**
     * 获取菜单的所有子菜单ID（递归）
     */
    @Query(value = "WITH RECURSIVE menu_tree AS (" +
                   "  SELECT id, parent_id FROM menus WHERE id = :menuId " +
                   "  UNION ALL " +
                   "  SELECT m.id, m.parent_id FROM menus m " +
                   "  INNER JOIN menu_tree mt ON m.parent_id = mt.id" +
                   ") SELECT id FROM menu_tree WHERE id != :menuId", 
           nativeQuery = true)
    List<String> findChildMenuIds(@Param("menuId") String menuId);

    /**
     * 获取用户菜单树
     */
    @Query("SELECT DISTINCT m FROM Menu m WHERE " +
           "m.status = 'ACTIVE' AND m.visible = true AND m.type IN ('DIRECTORY', 'MENU') AND " +
           "(m.permissions IS EMPTY OR EXISTS " +
           "(SELECT 1 FROM m.permissions p WHERE p IN :userPermissions)) " +
           "ORDER BY m.orderNum ASC")
    List<Menu> findUserMenuTree(@Param("userPermissions") List<String> userPermissions);

    /**
     * 统计菜单总数
     */
    @Query("SELECT COUNT(m) FROM Menu m")
    long countTotalMenus();

    /**
     * 统计活跃菜单数
     */
    @Query("SELECT COUNT(m) FROM Menu m WHERE m.status = 'ACTIVE'")
    long countActiveMenus();

    /**
     * 统计各类型菜单数量
     */
    @Query("SELECT m.type, COUNT(m) FROM Menu m GROUP BY m.type")
    List<Object[]> countMenusByType();

    /**
     * 批量更新菜单状态
     */
    @Query("UPDATE Menu m SET m.status = :status, m.updatedBy = :updatedBy WHERE m.id IN :menuIds")
    int updateStatusByIds(@Param("menuIds") List<String> menuIds, 
                         @Param("status") Menu.MenuStatus status,
                         @Param("updatedBy") String updatedBy);

    /**
     * 批量更新菜单可见性
     */
    @Query("UPDATE Menu m SET m.visible = :visible, m.updatedBy = :updatedBy WHERE m.id IN :menuIds")
    int updateVisibilityByIds(@Param("menuIds") List<String> menuIds, 
                             @Param("visible") Boolean visible,
                             @Param("updatedBy") String updatedBy);

    /**
     * 检查是否存在循环依赖
     */
    @Query(value = "WITH RECURSIVE menu_path AS (" +
                   "  SELECT id, parent_id, 1 as level FROM menus WHERE id = :childId " +
                   "  UNION ALL " +
                   "  SELECT m.id, m.parent_id, mp.level + 1 FROM menus m " +
                   "  INNER JOIN menu_path mp ON m.id = mp.parent_id " +
                   "  WHERE mp.level < 10" +
                   ") SELECT COUNT(*) > 0 FROM menu_path WHERE parent_id = :parentId", 
           nativeQuery = true)
    boolean hasCircularDependency(@Param("childId") String childId, @Param("parentId") String parentId);

    /**
     * 获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(m.orderNum), 0) FROM Menu m WHERE " +
           "(:parentId IS NULL AND m.parentId IS NULL) OR m.parentId = :parentId")
    Integer getMaxOrderNum(@Param("parentId") String parentId);

    /**
     * 查找指定路径的所有父菜单路径
     */
    @Query(value = "WITH RECURSIVE menu_path AS (" +
                   "  SELECT id, parent_id, path, 0 as level FROM menus WHERE path = :path " +
                   "  UNION ALL " +
                   "  SELECT m.id, m.parent_id, m.path, mp.level + 1 FROM menus m " +
                   "  INNER JOIN menu_path mp ON m.id = mp.parent_id" +
                   ") SELECT path FROM menu_path ORDER BY level DESC", 
           nativeQuery = true)
    List<String> findParentPaths(@Param("path") String path);
} 