package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;

import java.time.LocalDateTime;

/**
 * 昵称更改历史实体
 *
 * <p><strong>业务用途</strong>：
 * 记录用户昵称的更改历史，用于：
 * <ul>
 *   <li>昵称更改时间限制控制</li>
 *   <li>昵称更改审计追踪</li>
 *   <li>异常行为检测</li>
 *   <li>数据恢复和回滚</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("nickname_change_history"),
    @Index(name = "idx_nickname_history_user_created", columnList = "user_id, created_at"),
    @Index(name = "idx_nickname_history_created_at", columnList = "created_at")
})
@Data
@EqualsAndHashCode(callSuper = false)
public class NicknameChangeHistory {

    @Id
        private Long id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 旧昵称
     */
    @Column("old_nickname")
    private String oldNickname;

    /**
     * 新昵称
     */
    @Column("new_nickname")
    private String newNickname;

    /**
     * 更改类型
     */
        @Column("change_type")
    private ChangeType changeType;

    /**
     * 更改原因
     */
    @Column("change_reason")
    private String changeReason;

    /**
     * 操作者ID（管理员强制更改时记录）
     */
    @Column("operator_id")
    private Long operatorId;

    /**
     * IP地址
     */
    @Column("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column("user_agent")
    private String userAgent;

    /**
     * 审核状态
     */
        @Column("review_status")
    private ReviewStatus reviewStatus = ReviewStatus.APPROVED;

    /**
     * 审核者ID
     */
    @Column("reviewer_id")
    private Long reviewerId;

    /**
     * 审核时间
     */
    @Column("reviewed_at")
    private LocalDateTime reviewedAt;

    /**
     * 审核备注
     */
    @Column("review_notes")
    private String reviewNotes;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更改类型枚举
     */
    public enum ChangeType {
        /**
         * 用户主动更改
         */
        USER_CHANGE,
        
        /**
         * 系统自动更改
         */
        SYSTEM_CHANGE,
        
        /**
         * 管理员强制更改
         */
        ADMIN_FORCE,
        
        /**
         * 初始设置
         */
        INITIAL_SET,
        
        /**
         * 违规处理
         */
        VIOLATION_HANDLE
    }

    /**
     * 审核状态枚举
     */
    public enum ReviewStatus {
        /**
         * 待审核
         */
        PENDING,
        
        /**
         * 已通过
         */
        APPROVED,
        
        /**
         * 已拒绝
         */
        REJECTED,
        
        /**
         * 需要人工审核
         */
        MANUAL_REVIEW
    }

    /**
     * 构造函数
     */
    public NicknameChangeHistory() {}

    /**
     * 创建用户更改记录
     */
    public static NicknameChangeHistory createUserChange(Long userId, String oldNickname, 
                                                        String newNickname, String ipAddress, 
                                                        String userAgent) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(oldNickname);
        history.setNewNickname(newNickname);
        history.setChangeType(ChangeType.USER_CHANGE);
        history.setIpAddress(ipAddress);
        history.setUserAgent(userAgent);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 创建管理员强制更改记录
     */
    public static NicknameChangeHistory createAdminForce(Long userId, String oldNickname, 
                                                        String newNickname, Long operatorId, 
                                                        String reason) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(oldNickname);
        history.setNewNickname(newNickname);
        history.setChangeType(ChangeType.ADMIN_FORCE);
        history.setOperatorId(operatorId);
        history.setChangeReason(reason);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 创建初始设置记录
     */
    public static NicknameChangeHistory createInitialSet(Long userId, String nickname, 
                                                        String ipAddress, String userAgent) {
        NicknameChangeHistory history = new NicknameChangeHistory();
        history.setUserId(userId);
        history.setOldNickname(null);
        history.setNewNickname(nickname);
        history.setChangeType(ChangeType.INITIAL_SET);
        history.setIpAddress(ipAddress);
        history.setUserAgent(userAgent);
        history.setReviewStatus(ReviewStatus.APPROVED);
        return history;
    }

    /**
     * 设置审核通过
     */
    public void approve(Long reviewerId, String notes) {
        this.reviewStatus = ReviewStatus.APPROVED;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.reviewNotes = notes;
    }

    /**
     * 设置审核拒绝
     */
    public void reject(Long reviewerId, String notes) {
        this.reviewStatus = ReviewStatus.REJECTED;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.reviewNotes = notes;
    }

    /**
     * 是否需要审核
     */
    public boolean needsReview() {
        return reviewStatus == ReviewStatus.PENDING || reviewStatus == ReviewStatus.MANUAL_REVIEW;
    }

    /**
     * 是否已通过审核
     */
    public boolean isApproved() {
        return reviewStatus == ReviewStatus.APPROVED;
    }
}
