package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.Objects;

/**
 * 用户等级实体
 * 记录用户当前等级信息和经验值
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_level"),
    @Index(name = "idx_user_level_current_level", columnList = "current_level"),
    @Index(name = "idx_user_level_total_exp", columnList = "total_exp"),
    @Index(name = "idx_user_level_level_exp", columnList = "current_level, total_exp"),
    @Index(name = "idx_user_level_updated_at", columnList = "updated_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLevel {

    /**
     * 主键ID
     */
    @Id
        @Column("id")
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "User ID cannot be null")
    @Column("user_id")
    private Long userId;

    /**
     * 当前等级
     */
    @NotNull(message = "Current level cannot be null")
    @Min(value = 1, message = "Current level must be at least 1")
    @Builder.Default
    @Column("current_level")
    private Integer currentLevel = 1;

    /**
     * 当前等级经验值
     */
    @NotNull(message = "Current experience cannot be null")
    @Min(value = 0, message = "Current experience cannot be negative")
    @Builder.Default
    @Column("current_exp")
    private Long currentExp = 0L;

    /**
     * 总经验值
     */
    @NotNull(message = "Total experience cannot be null")
    @Min(value = 0, message = "Total experience cannot be negative")
    @Builder.Default
    @Column("total_exp")
    private Long totalExp = 0L;

    /**
     * 升级时间
     */
    @Column("level_up_at")
    private Instant levelUpAt;

    /**
     * 上次经验获得时间
     */
    @Column("last_exp_earned_at")
    private Instant lastExpEarnedAt;

    /**
     * 今日获得经验值
     */
    @Builder.Default
    @Column("today_exp")
    private Long todayExp = 0L;

    /**
     * 今日经验重置时间
     */
    @Column("today_exp_reset_at")
    private Instant todayExpResetAt;

    /**
     * 连续登录天数
     */
    @Builder.Default
    @Column("consecutive_login_days")
    private Integer consecutiveLoginDays = 0;

    /**
     * 上次登录时间
     */
    @Column("last_login_at")
    private Instant lastLoginAt;

    /**
     * 等级称号
     */
    @Column("level_title")
    private String levelTitle;

    /**
     * 等级图标URL
     */
    @Column("level_icon_url")
    private String levelIconUrl;

    /**
     * 是否有待领取奖励
     */
    @Builder.Default
    @Column("has_pending_rewards")
    private Boolean hasPendingRewards = false;

    /**
     * 经验加成倍率（百分比）
     */
    @Builder.Default
    @Column("exp_multiplier")
    private Integer expMultiplier = 100;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 用户实体关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;

    /**
     * 增加经验值
     * 
     * @param expAmount 经验值数量
     * @param requiredExpForNextLevel 下一级所需经验
     * @return 是否升级
     */
    public boolean addExperience(Long expAmount, Long requiredExpForNextLevel) {
        if (expAmount == null || expAmount <= 0) {
            return false;
        }

        this.currentExp += expAmount;
        this.totalExp += expAmount;
        this.todayExp += expAmount;
        this.lastExpEarnedAt = Instant.now();

        // 检查是否升级
        if (requiredExpForNextLevel != null && this.currentExp >= requiredExpForNextLevel) {
            return levelUp(requiredExpForNextLevel);
        }

        return false;
    }

    /**
     * 升级处理
     * 
     * @param requiredExp 当前等级所需经验
     * @return 是否成功升级
     */
    private boolean levelUp(Long requiredExp) {
        this.currentLevel++;
        this.currentExp -= requiredExp;
        this.levelUpAt = Instant.now();
        this.hasPendingRewards = true;
        return true;
    }

    /**
     * 重置今日经验
     */
    public void resetTodayExperience() {
        this.todayExp = 0L;
        this.todayExpResetAt = Instant.now();
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo() {
        Instant now = Instant.now();
        
        // 检查是否连续登录
        if (lastLoginAt != null) {
            long daysBetween = java.time.Duration.between(lastLoginAt, now).toDays();
            if (daysBetween == 1) {
                // 连续登录
                this.consecutiveLoginDays++;
            } else if (daysBetween > 1) {
                // 中断连续登录
                this.consecutiveLoginDays = 1;
            }
            // daysBetween == 0 表示同一天登录，不更新连续天数
        } else {
            // 首次登录
            this.consecutiveLoginDays = 1;
        }
        
        this.lastLoginAt = now;
    }

    /**
     * 设置等级信息
     * 
     * @param levelTitle 等级称号
     * @param levelIconUrl 等级图标URL
     */
    public void setLevelInfo(String levelTitle, String levelIconUrl) {
        this.levelTitle = levelTitle;
        this.levelIconUrl = levelIconUrl;
    }

    /**
     * 领取奖励
     */
    public void claimRewards() {
        this.hasPendingRewards = false;
    }

    /**
     * 设置经验加成
     * 
     * @param multiplier 加成倍率（百分比）
     */
    public void setExpMultiplier(Integer multiplier) {
        if (multiplier != null && multiplier > 0) {
            this.expMultiplier = multiplier;
        }
    }

    /**
     * 计算实际获得经验（考虑加成）
     * 
     * @param baseExp 基础经验
     * @return 实际经验
     */
    public Long calculateActualExp(Long baseExp) {
        if (baseExp == null || baseExp <= 0) {
            return 0L;
        }
        return baseExp * expMultiplier / 100;
    }

    /**
     * 检查是否需要重置今日经验
     * 
     * @return 是否需要重置
     */
    public boolean needsResetTodayExp() {
        if (todayExpResetAt == null) {
            return true;
        }
        
        Instant now = Instant.now();
        Instant startOfToday = now.truncatedTo(java.time.temporal.ChronoUnit.DAYS);
        return todayExpResetAt.isBefore(startOfToday);
    }

    /**
     * 获取等级进度百分比
     * 
     * @param requiredExpForNextLevel 下一级所需经验
     * @return 进度百分比（0-100）
     */
    public Double getLevelProgress(Long requiredExpForNextLevel) {
        if (requiredExpForNextLevel == null || requiredExpForNextLevel <= 0) {
            return 100.0;
        }
        return Math.min(100.0, (currentExp.doubleValue() / requiredExpForNextLevel.doubleValue()) * 100);
    }

    /**
     * 获取距离下一级还需经验
     * 
     * @param requiredExpForNextLevel 下一级所需经验
     * @return 还需经验值
     */
    public Long getExpToNextLevel(Long requiredExpForNextLevel) {
        if (requiredExpForNextLevel == null || requiredExpForNextLevel <= currentExp) {
            return 0L;
        }
        return requiredExpForNextLevel - currentExp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserLevel userLevel = (UserLevel) o;
        return Objects.equals(id, userLevel.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserLevel{" +
                "id=" + id +
                ", userId=" + userId +
                ", currentLevel=" + currentLevel +
                ", currentExp=" + currentExp +
                ", totalExp=" + totalExp +
                ", levelTitle='" + levelTitle + '\'' +
                '}';
    }
}
