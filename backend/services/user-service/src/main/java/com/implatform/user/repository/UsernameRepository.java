package com.implatform.user.repository;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.Username;

import java.time.LocalDateTime;

/**
 * 用户名Repository
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface UsernameRepository extends ReactiveCrudRepository<Username, Long> {

    /**
     * 根据用户名查找（不区分大小写）
     */
    @Query("SELECT * FROM usernames WHERE LOWER(username) = LOWER(:username)")
    Mono<Username> findByUsernameIgnoreCase(String username);

    /**
     * 根据用户ID查找用户名
     */
    Mono<Username> findByUserId(Long userId);

    /**
     * 根据用户名查找（区分大小写）
     */
    Mono<Username> findByUsername(String username);

    /**
     * 检查用户名是否存在（不区分大小写）
     */
    @Query("SELECT COUNT(*) > 0 FROM usernames WHERE LOWER(username) = LOWER(:username)")
    Mono<Boolean> existsByUsernameIgnoreCase(String username);

    /**
     * 检查用户名是否可用
     */
    @Query("SELECT COUNT(u) = 0 FROM Username u WHERE LOWER(u.username) = LOWER(:username) AND u.isActive = true")
    boolean isUsernameAvailable(@Param("username") String username);

    /**
     * 查找活跃的用户名
     */
    List<Username> findByIsActiveTrue();

    /**
     * 查找已验证的用户名
     */
    List<Username> findByIsVerifiedTrue();

    /**
     * 查找高级用户名
     */
    List<Username> findByIsPremiumTrue();

    /**
     * 查找保留的用户名
     */
    @Query("SELECT u FROM Username u WHERE u.reservedUntil IS NOT NULL AND u.reservedUntil > CURRENT_TIMESTAMP")
    List<Username> findReservedUsernames();

    /**
     * 查找过期的保留用户名
     */
    @Query("SELECT u FROM Username u WHERE u.reservedUntil IS NOT NULL AND u.reservedUntil <= CURRENT_TIMESTAMP")
    List<Username> findExpiredReservedUsernames();

    /**
     * 按用户名前缀搜索
     */
    @Query("SELECT u FROM Username u WHERE LOWER(u.username) LIKE LOWER(CONCAT(:prefix, '%')) AND u.isActive = true ORDER BY u.username")
    List<Username> findByUsernameStartingWithIgnoreCase(@Param("prefix") String prefix, Pageable pageable);

    /**
     * 按用户名包含搜索
     */
    @Query("SELECT u FROM Username u WHERE LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) AND u.isActive = true ORDER BY u.username")
    List<Username> findByUsernameContainingIgnoreCase(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找短用户名（5个字符以下）
     */
    @Query("SELECT u FROM Username u WHERE LENGTH(u.username) <= 5 AND u.isActive = true ORDER BY LENGTH(u.username), u.username")
    List<Username> findShortUsernames();

    /**
     * 统计用户名数量
     */
    @Query("SELECT COUNT(u) FROM Username u WHERE u.isActive = true")
    Long countActiveUsernames();

    /**
     * 统计已验证用户名数量
     */
    @Query("SELECT COUNT(u) FROM Username u WHERE u.isVerified = true AND u.isActive = true")
    Long countVerifiedUsernames();

    /**
     * 统计高级用户名数量
     */
    @Query("SELECT COUNT(u) FROM Username u WHERE u.isPremium = true AND u.isActive = true")
    Long countPremiumUsernames();

    /**
     * 统计今日注册的用户名数量
     */
    @Query("SELECT COUNT(u) FROM Username u WHERE u.createdAt >= CURRENT_DATE AND u.isActive = true")
    Long countTodayRegistrations();

    /**
     * 统计指定时间范围内的注册数量
     */
    @Query("SELECT COUNT(u) FROM Username u WHERE u.createdAt BETWEEN :startTime AND :endTime AND u.isActive = true")
    Long countRegistrationsBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查找最近注册的用户名
     */
    @Query("SELECT u FROM Username u WHERE u.isActive = true ORDER BY u.createdAt DESC")
    List<Username> findRecentRegistrations(Pageable pageable);

    /**
     * 查找最受欢迎的用户名（按长度排序）
     */
    @Query("SELECT u FROM Username u WHERE u.isActive = true ORDER BY LENGTH(u.username), u.createdAt")
    List<Username> findPopularUsernames(Pageable pageable);

    /**
     * 批量停用用户名
     */
    @Modifying
    @Query("UPDATE Username u SET u.isActive = false, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :ids")
    int deactivateUsernames(@Param("ids") List<Long> ids);

    /**
     * 批量激活用户名
     */
    @Modifying
    @Query("UPDATE Username u SET u.isActive = true, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :ids")
    int activateUsernames(@Param("ids") List<Long> ids);

    /**
     * 批量验证用户名
     */
    @Modifying
    @Query("UPDATE Username u SET u.isVerified = true, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :ids")
    int verifyUsernames(@Param("ids") List<Long> ids);

    /**
     * 批量设置为高级用户名
     */
    @Modifying
    @Query("UPDATE Username u SET u.isPremium = true, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :ids")
    int setPremiumUsernames(@Param("ids") List<Long> ids);

    /**
     * 清除过期的保留
     */
    @Modifying
    @Query("UPDATE Username u SET u.reservedUntil = NULL, u.updatedAt = CURRENT_TIMESTAMP WHERE u.reservedUntil IS NOT NULL AND u.reservedUntil <= CURRENT_TIMESTAMP")
    int clearExpiredReservations();

    /**
     * 查找用户名长度分布
     */
    @Query("SELECT LENGTH(u.username) as length, COUNT(u) as count FROM Username u WHERE u.isActive = true GROUP BY LENGTH(u.username) ORDER BY length")
    List<Object[]> getUsernameLengthDistribution();

    /**
     * 查找用户名创建时间分布
     */
    @Query("SELECT DATE(u.createdAt) as date, COUNT(u) as count FROM Username u WHERE u.isActive = true AND u.createdAt >= :since GROUP BY DATE(u.createdAt) ORDER BY date")
    List<Object[]> getUsernameCreationDistribution(@Param("since") LocalDateTime since);

    /**
     * 查找重复的用户名（不同大小写）
     */
    @Query("SELECT LOWER(u.username) as lowerUsername, COUNT(u) as count FROM Username u WHERE u.isActive = true GROUP BY LOWER(u.username) HAVING COUNT(u) > 1")
    List<Object[]> findDuplicateUsernames();

    /**
     * 查找用户的所有历史用户名
     */
    @Query("SELECT DISTINCT uh.oldUsername FROM UsernameHistory uh WHERE uh.userId = :userId AND uh.oldUsername IS NOT NULL ORDER BY uh.createdAt DESC")
    List<String> findUserHistoricalUsernames(@Param("userId") Long userId);

    /**
     * 检查用户是否曾经使用过某个用户名
     */
    @Query("SELECT COUNT(uh) > 0 FROM UsernameHistory uh WHERE uh.userId = :userId AND (LOWER(uh.oldUsername) = LOWER(:username) OR LOWER(uh.newUsername) = LOWER(:username))")
    boolean hasUserEverUsedUsername(@Param("userId") Long userId, @Param("username") String username);

    /**
     * 查找用户名变更频繁的用户
     */
    @Query("SELECT uh.userId, COUNT(uh) as changeCount FROM UsernameHistory uh WHERE uh.createdAt >= :since GROUP BY uh.userId HAVING COUNT(uh) >= :minChanges ORDER BY changeCount DESC")
    List<Object[]> findFrequentUsernameChangers(@Param("since") LocalDateTime since, @Param("minChanges") int minChanges);

    /**
     * 分页查询用户名
     */
    @Query("SELECT u FROM Username u WHERE u.isActive = true ORDER BY u.createdAt DESC")
    Page<Username> findActiveUsernames(Pageable pageable);

    /**
     * 按条件搜索用户名
     */
    @Query("SELECT u FROM Username u WHERE " +
           "(:username IS NULL OR LOWER(u.username) LIKE LOWER(CONCAT('%', :username, '%'))) AND " +
           "(:isVerified IS NULL OR u.isVerified = :isVerified) AND " +
           "(:isPremium IS NULL OR u.isPremium = :isPremium) AND " +
           "u.isActive = true " +
           "ORDER BY u.createdAt DESC")
    Page<Username> searchUsernames(@Param("username") String username,
                                  @Param("isVerified") Boolean isVerified,
                                  @Param("isPremium") Boolean isPremium,
                                  Pageable pageable);
}
