package com.implatform.user.repository;

import com.implatform.user.entity.QrLoginSession;
import com.implatform.user.enums.QrLoginStatus;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * 二维码登录会话Repository
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface QrLoginSessionRepository extends ReactiveCrudRepository<QrLoginSession, String>, QrLoginSessionCustomRepository {

    /**
     * 根据状态查找会话
     */
    Flux<QrLoginSession> findByStatus(QrLoginStatus status);

    /**
     * 根据用户ID查找会话
     */
    Flux<QrLoginSession> findByUserId(Long userId);

    /**
     * 根据WebSocket连接ID查找会话
     */
    Mono<QrLoginSession> findByWebsocketConnectionId(String websocketConnectionId);

    /**
     * 根据状态和过期时间查找会话
     */
    Flux<QrLoginSession> findByStatusAndExpiresAtBefore(QrLoginStatus status, Instant expireTime);

    /**
     * 根据用户ID和状态查找会话
     */
    Flux<QrLoginSession> findByUserIdAndStatus(Long userId, QrLoginStatus status);

    /**
     * 根据创建时间范围查找会话
     */
    Flux<QrLoginSession> findByCreatedAtBetween(Instant startTime, Instant endTime);

    /**
     * 删除过期的会话
     * 注意：Redis不支持Before查询派生，改用手动实现
     */
    // void deleteByExpiresAtBefore(Instant expireTime); // Redis不支持，已移除

    /**
     * 删除指定状态的会话
     */
    Mono<Void> deleteByStatus(QrLoginStatus status);

    /**
     * 统计指定状态的会话数量
     */
    Mono<Long> countByStatus(QrLoginStatus status);

    /**
     * 统计指定用户的会话数量
     */
    Mono<Long> countByUserId(Long userId);

    /**
     * 检查是否存在指定token的会话
     */
    Mono<Boolean> existsByQrToken(String qrToken);
}
