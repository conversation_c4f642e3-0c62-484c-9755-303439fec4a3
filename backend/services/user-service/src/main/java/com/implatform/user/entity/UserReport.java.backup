package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 用户举报实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_reports")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserReport {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "reporter_id", nullable = false)
    private Long reporterId;
    
    @Column(name = "target_user_id", nullable = false)
    private Long targetUserId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "report_type", nullable = false, length = 50)
    private ReportType reportType;
    
    @Column(name = "reason", columnDefinition = "TEXT")
    private String reason;
    
    @Column(name = "message_id")
    private Long messageId;
    
    @Column(name = "group_id")
    private Long groupId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ReportStatus status = ReportStatus.PENDING;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "handled_at")
    private LocalDateTime handledAt;
    
    @Column(name = "handled_by")
    private Long handledBy;

    @Column(name = "handle_note", columnDefinition = "TEXT")
    private String handleNote;

    @Column(name = "is_urgent", nullable = false)
    private Boolean isUrgent = false;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 举报者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reporter_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User reporter;
    
    /**
     * 被举报者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "target_user_id", referencedColumnName = "id", insertable = false, updatable = false)
    private User targetUser;
    
    /**
     * 处理者用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "handled_by", referencedColumnName = "id", insertable = false, updatable = false)
    private User handler;
    
    /**
     * 举报类型枚举
     */
    public enum ReportType {
        SPAM("垃圾信息"),
        HARASSMENT("骚扰"),
        INAPPROPRIATE_CONTENT("不当内容"),
        FAKE_ACCOUNT("虚假账户"),
        VIOLENCE("暴力威胁"),
        COPYRIGHT("版权侵犯"),
        OTHER("其他");
        
        private final String description;
        
        ReportType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 举报状态枚举
     */
    public enum ReportStatus {
        PENDING("待处理"),
        REVIEWING("审核中"),
        RESOLVED("已解决"),
        REJECTED("已拒绝");
        
        private final String description;
        
        ReportStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数
     */
    public UserReport(Long reporterId, Long targetUserId, ReportType reportType, String reason) {
        this.reporterId = reporterId;
        this.targetUserId = targetUserId;
        this.reportType = reportType;
        this.reason = reason;
        this.status = ReportStatus.PENDING;
        this.isUrgent = false;
    }
    
    /**
     * 标记为已处理
     */
    public void markAsHandled(Long handlerId, ReportStatus newStatus) {
        this.handledBy = handlerId;
        this.handledAt = LocalDateTime.now();
        this.status = newStatus;
    }

    /**
     * 标记为已处理（带备注）
     */
    public void markAsHandled(Long handlerId, ReportStatus newStatus, String handleNote) {
        this.handledBy = handlerId;
        this.handledAt = LocalDateTime.now();
        this.status = newStatus;
        this.handleNote = handleNote;
    }
    
    /**
     * 检查是否已处理
     */
    public boolean isHandled() {
        return handledAt != null && (status == ReportStatus.RESOLVED || status == ReportStatus.REJECTED);
    }
    
    /**
     * 检查是否为有效的举报
     */
    public boolean isValid() {
        return reporterId != null && targetUserId != null && 
               !reporterId.equals(targetUserId) && reportType != null;
    }
}
