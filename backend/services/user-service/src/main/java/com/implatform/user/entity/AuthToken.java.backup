package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 认证令牌实体类 - IM平台身份认证和授权管理实体
 *
 * <p><strong>业务用途</strong>：
 * 管理IM平台中所有用户的身份认证令牌，包括JWT访问令牌和刷新令牌的完整生命周期管理。
 * 支持多设备登录、令牌安全管理、会话跟踪、权限控制等核心认证功能。
 *
 * <p><strong>核心功能特性</strong>：
 * <ul>
 *   <li><strong>令牌生命周期管理</strong>：创建、刷新、撤销、过期处理</li>
 *   <li><strong>多设备支持</strong>：每个设备独立的令牌管理</li>
 *   <li><strong>安全存储</strong>：令牌哈希存储，防止明文泄露</li>
 *   <li><strong>会话跟踪</strong>：记录使用时间、IP地址、设备信息</li>
 *   <li><strong>权限作用域</strong>：支持细粒度的权限控制</li>
 *   <li><strong>记住登录</strong>：支持长期有效的认证状态</li>
 * </ul>
 *
 * <p><strong>使用场景</strong>：
 * <ul>
 *   <li>用户登录后生成访问令牌和刷新令牌</li>
 *   <li>API请求的身份验证和权限检查</li>
 *   <li>令牌刷新和自动续期</li>
 *   <li>多设备登录管理和会话控制</li>
 *   <li>安全审计和异常登录检测</li>
 *   <li>强制登出和令牌撤销</li>
 * </ul>
 *
 * <p><strong>业务上下文</strong>：
 * 作为IM平台的核心安全组件，AuthToken与用户管理、设备管理、权限控制、安全审计等
 * 多个业务域紧密集成。通过令牌机制实现无状态的身份认证和细粒度的权限控制。
 *
 * <p><strong>关键关系</strong>：
 * <ul>
 *   <li>多对一关系：AuthToken → User（令牌属于用户）</li>
 *   <li>多对一关系：AuthToken → Device（令牌绑定设备）</li>
 *   <li>一对多关系：AuthToken → LoginLog（令牌使用日志）</li>
 *   <li>一对多关系：AuthToken → ApiRequest（API调用记录）</li>
 * </ul>
 *
 * <p><strong>安全特性</strong>：
 * <ul>
 *   <li>令牌哈希存储，防止数据库泄露风险</li>
 *   <li>访问令牌和刷新令牌分离管理</li>
 *   <li>IP地址和设备信息绑定验证</li>
 *   <li>令牌作用域限制和权限控制</li>
 *   <li>异常行为检测和自动撤销</li>
 * </ul>
 *
 * <p><strong>实际应用示例</strong>：
 * <pre>
 * // 创建新令牌
 * AuthToken token = AuthToken.builder()
 *     .userId(userId)
 *     .deviceId(deviceId)
 *     .tokenType(TokenType.BEARER)
 *     .status(TokenStatus.ACTIVE)
 *     .expiresAt(Instant.now().plus(1, ChronoUnit.HOURS))
 *     .refreshExpiresAt(Instant.now().plus(30, ChronoUnit.DAYS))
 *     .clientIp(clientIp)
 *     .scopes("read,write")
 *     .build();
 *
 * // 验证令牌
 * if (token.isValid()) {
 *     // 执行业务逻辑
 *     token.updateLastUsed();
 * }
 *
 * // 撤销令牌
 * token.revoke();
 * </pre>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 auth_tokens 表，包含用户ID、设备ID、令牌哈希、过期时间等字段的索引，
 * 支持高效的令牌验证、查询和清理操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "auth_tokens", indexes = {
    @Index(name = "idx_auth_tokens_user_id", columnList = "user_id"),
    @Index(name = "idx_auth_tokens_device_id", columnList = "device_id"),
    @Index(name = "idx_auth_tokens_access_token", columnList = "access_token_hash"),
    @Index(name = "idx_auth_tokens_refresh_token", columnList = "refresh_token_hash"),
    @Index(name = "idx_auth_tokens_expires_at", columnList = "expires_at"),
    @Index(name = "idx_auth_tokens_status", columnList = "status")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    @NotNull(message = "User ID cannot be null")
    private Long userId;

    /**
     * 设备ID
     */
    @Column(name = "device_id", nullable = false, length = 64)
    @NotBlank(message = "Device ID cannot be blank")
    @Size(max = 64, message = "Device ID cannot exceed 64 characters")
    private String deviceId;

    /**
     * 访问令牌哈希值（存储哈希而非原始令牌）
     */
    @Column(name = "access_token_hash", nullable = false, length = 128)
    @NotBlank(message = "Access token hash cannot be blank")
    @Size(max = 128, message = "Access token hash cannot exceed 128 characters")
    private String accessTokenHash;

    /**
     * 刷新令牌哈希值
     */
    @Column(name = "refresh_token_hash", nullable = false, length = 128)
    @NotBlank(message = "Refresh token hash cannot be blank")
    @Size(max = 128, message = "Refresh token hash cannot exceed 128 characters")
    private String refreshTokenHash;

    /**
     * 令牌类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "token_type", nullable = false, length = 20)
    @NotNull(message = "Token type cannot be null")
    private TokenType tokenType;

    /**
     * 令牌状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    @NotNull(message = "Token status cannot be null")
    private TokenStatus status;

    /**
     * 访问令牌过期时间
     */
    @Column(name = "expires_at", nullable = false)
    @NotNull(message = "Expires at cannot be null")
    private Instant expiresAt;

    /**
     * 刷新令牌过期时间
     */
    @Column(name = "refresh_expires_at", nullable = false)
    @NotNull(message = "Refresh expires at cannot be null")
    private Instant refreshExpiresAt;

    /**
     * 最后使用时间
     */
    @Column(name = "last_used_at")
    private Instant lastUsedAt;

    /**
     * 客户端IP地址
     */
    @Column(name = "client_ip", length = 45)
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Column(name = "user_agent", length = 500)
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    private String userAgent;

    /**
     * 地理位置信息
     */
    @Column(name = "location", length = 100)
    @Size(max = 100, message = "Location cannot exceed 100 characters")
    private String location;

    /**
     * 令牌作用域
     */
    @Column(name = "scopes", length = 200)
    @Size(max = 200, message = "Scopes cannot exceed 200 characters")
    private String scopes;

    /**
     * 是否记住登录
     */
    @Column(name = "remember_me", nullable = false)
    @Builder.Default
    private Boolean rememberMe = false;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 令牌类型枚举
     */
    public enum TokenType {
        /**
         * Bearer令牌
         */
        BEARER,
        
        /**
         * API密钥
         */
        API_KEY,
        
        /**
         * 临时令牌
         */
        TEMPORARY
    }

    /**
     * 令牌状态枚举
     */
    public enum TokenStatus {
        /**
         * 活跃状态
         */
        ACTIVE,
        
        /**
         * 已过期
         */
        EXPIRED,
        
        /**
         * 已撤销
         */
        REVOKED,
        
        /**
         * 已刷新
         */
        REFRESHED,
        
        /**
         * 已暂停
         */
        SUSPENDED
    }

    /**
     * 检查令牌是否有效
     */
    public boolean isValid() {
        return status == TokenStatus.ACTIVE && 
               expiresAt.isAfter(Instant.now());
    }

    /**
     * 检查刷新令牌是否有效
     */
    public boolean isRefreshValid() {
        return status == TokenStatus.ACTIVE && 
               refreshExpiresAt.isAfter(Instant.now());
    }

    /**
     * 撤销令牌
     */
    public void revoke() {
        this.status = TokenStatus.REVOKED;
        this.updatedAt = Instant.now();
    }

    /**
     * 标记为已刷新
     */
    public void markAsRefreshed() {
        this.status = TokenStatus.REFRESHED;
        this.updatedAt = Instant.now();
    }

    /**
     * 更新最后使用时间
     */
    public void updateLastUsed() {
        this.lastUsedAt = Instant.now();
        this.updatedAt = Instant.now();
    }
}
