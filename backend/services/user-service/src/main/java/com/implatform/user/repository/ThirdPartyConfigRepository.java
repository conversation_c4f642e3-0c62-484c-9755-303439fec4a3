package com.implatform.user.repository;

import com.implatform.user.entity.ThirdPartyConfig;
import com.implatform.user.enums.ThirdPartyProvider;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 第三方登录配置数据访问层 - IM平台第三方登录配置数据库操作接口
 *
 * <p><strong>Repository概述</strong>：
 * 本接口提供第三方登录平台配置信息的数据访问功能，负责配置信息的CRUD操作、
 * 启用状态管理、配置验证等数据库交互。支持动态配置管理和平台启用控制。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface ThirdPartyConfigRepository extends ReactiveCrudRepository<ThirdPartyConfig, Long> {

    /**
     * 根据平台类型查询配置
     */
    Mono<ThirdPartyConfig> findByProviderType(ThirdPartyProvider providerType);

    /**
     * 根据平台类型查询启用的配置
     */
    Mono<ThirdPartyConfig> findByProviderTypeAndIsEnabledTrue(ThirdPartyProvider providerType);

    /**
     * 查询所有启用的配置
     */
    Flux<ThirdPartyConfig> findByIsEnabledTrueOrderByProviderType();
    
    /**
     * 查询所有配置按平台类型排序
     */
    Flux<ThirdPartyConfig> findAllByOrderByProviderType();

    /**
     * 检查平台是否已配置
     */
    Mono<Boolean> existsByProviderType(ThirdPartyProvider providerType);

    /**
     * 检查平台是否已启用
     */
    Mono<Boolean> existsByProviderTypeAndIsEnabledTrue(ThirdPartyProvider providerType);

    /**
     * 统计启用的平台数量
     */
    Mono<Long> countByIsEnabledTrue();

    /**
     * 根据应用ID查询配置
     */
    Mono<ThirdPartyConfig> findByAppId(String appId);

    /**
     * 查询指定平台类型列表的配置
     */
    @Query("SELECT * FROM third_party_configs WHERE provider_type = ANY(:providerTypes) ORDER BY provider_type")
    Flux<ThirdPartyConfig> findByProviderTypeIn(ThirdPartyProvider[] providerTypes);

    /**
     * 查询指定平台类型列表的启用配置
     */
    @Query("SELECT * FROM third_party_configs WHERE provider_type = ANY(:providerTypes) AND is_enabled = true ORDER BY provider_type")
    Flux<ThirdPartyConfig> findEnabledByProviderTypeIn(ThirdPartyProvider[] providerTypes);
}
