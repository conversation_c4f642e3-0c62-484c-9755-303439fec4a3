package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 帮助反馈实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("help_feedback"),
    @Index(name = "idx_help_feedback_status", columnList = "status"),
    @Index(name = "idx_help_feedback_type", columnList = "feedback_type"),
    @Index(name = "idx_help_feedback_priority", columnList = "priority"),
    @Index(name = "idx_help_feedback_number", columnList = "feedback_number", unique = true),
    @Index(name = "idx_help_feedback_created_at", columnList = "created_at")
})
public class HelpFeedback {

    /**
     * 反馈ID
     */
    @Id
        private Long id;

    /**
     * 反馈编号
     */
    @Column("feedback_number")
    private String feedbackNumber;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 反馈类型
     */
        @Column("feedback_type")
    private FeedbackType feedbackType;

    /**
     * 反馈标题
     */
    @Column("title")
    private String title;

    /**
     * 反馈内容
     */
    @Column("content")
    private String content;

    /**
     * 联系方式
     */
    @Column("contact_info")
    private String contactInfo;

    /**
     * 附件URL列表（JSON格式）
     */
    @Column("attachments")
    private java.util.List<String> attachments;

    /**
     * 反馈状态
     */
        @Column("status")
    private FeedbackStatus status = FeedbackStatus.PENDING;

    /**
     * 优先级
     */
        @Column("priority")
    private Priority priority = Priority.MEDIUM;

    /**
     * 处理人员ID
     */
    @Column("handler_id")
    private Long handlerId;

    /**
     * 处理备注
     */
    @Column("handler_notes")
    private String handlerNotes;

    /**
     * 回复内容
     */
    @Column("reply_content")
    private String replyContent;

    /**
     * 用户评分（1-5星）
     */
    @Column("user_rating")
    private Integer userRating;

    /**
     * 用户评价
     */
    @Column("user_comment")
    private String userComment;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 处理时间
     */
    @Column("handled_at")
    private Instant handledAt;

    /**
     * 关闭时间
     */
    @Column("closed_at")
    private Instant closedAt;

    /**
     * 是否匿名
     */
    @Column("is_anonymous")
    private Boolean isAnonymous = false;

    /**
     * 是否已解决
     */
    @Column("is_resolved")
    private Boolean isResolved = false;

    /**
     * 评分
     */
    @Column("rating")
    private Integer rating;

    /**
     * 响应截止时间
     */
    @Column("response_due_at")
    private Instant responseDueAt;

    /**
     * 处理人员
     */
    @Column("handled_by")
    private Long handledBy;

    /**
     * 管理员回复
     */
    @Column("admin_response")
    private String adminResponse;

    /**
     * 回复时间
     */
    @Column("response_at")
    private Instant responseAt;

    /**
     * 解决时间
     */
    @Column("resolved_at")
    private Instant resolvedAt;

    /**
     * 分配给
     */
    @Column("assigned_to")
    private Long assignedTo;

    /**
     * 分配人
     */
    @Column("assigned_by")
    private Long assignedBy;

    /**
     * 分配时间
     */
    @Column("assigned_at")
    private Instant assignedAt;

    /**
     * 内部备注
     */
    @Column("internal_notes")
    private String internalNotes;

    /**
     * 反馈类型枚举
     */
    public enum FeedbackType {
        BUG_REPORT,     // 错误报告
        FEATURE_REQUEST, // 功能请求
        IMPROVEMENT,    // 改进建议
        QUESTION,       // 问题咨询
        COMPLAINT,      // 投诉建议
        OTHER          // 其他
    }

    /**
     * 反馈状态枚举
     */
    public enum FeedbackStatus {
        PENDING,        // 待处理
        IN_PROGRESS,    // 处理中
        RESOLVED,       // 已解决
        CLOSED,         // 已关闭
        REJECTED        // 已拒绝
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW,            // 低
        MEDIUM,         // 中
        HIGH,           // 高
        URGENT          // 紧急
    }

    /**
     * 检查反馈是否已处理
     */
    public boolean isHandled() {
        return status == FeedbackStatus.RESOLVED || 
               status == FeedbackStatus.CLOSED || 
               status == FeedbackStatus.REJECTED;
    }

    /**
     * 检查反馈是否已关闭
     */
    public boolean isClosed() {
        return status == FeedbackStatus.CLOSED || status == FeedbackStatus.REJECTED;
    }

    /**
     * 开始处理反馈
     */
    public void startProcessing(Long handlerId) {
        this.status = FeedbackStatus.IN_PROGRESS;
        this.handlerId = handlerId;
        this.handledAt = Instant.now();
    }

    /**
     * 解决反馈
     */
    public void resolve(String replyContent, String handlerNotes) {
        this.status = FeedbackStatus.RESOLVED;
        this.replyContent = replyContent;
        this.handlerNotes = handlerNotes;
        this.handledAt = Instant.now();
    }

    /**
     * 关闭反馈
     */
    public void close() {
        this.status = FeedbackStatus.CLOSED;
        this.closedAt = Instant.now();
    }

    /**
     * 拒绝反馈
     */
    public void reject(String reason) {
        this.status = FeedbackStatus.REJECTED;
        this.handlerNotes = reason;
        this.handledAt = Instant.now();
        this.closedAt = Instant.now();
    }

    /**
     * 用户评价
     */
    public void rate(Integer rating, String comment) {
        this.userRating = rating;
        this.userComment = comment;
    }

    /**
     * 获取是否匿名
     */
    public Boolean getIsAnonymous() {
        return isAnonymous;
    }

    /**
     * 设置是否匿名
     */
    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    /**
     * 获取是否已解决
     */
    public Boolean getIsResolved() {
        return isResolved;
    }

    /**
     * 设置是否已解决
     */
    public void setIsResolved(Boolean isResolved) {
        this.isResolved = isResolved;
    }

    /**
     * 获取评分
     */
    public Integer getRating() {
        return rating;
    }

    /**
     * 设置评分
     */
    public void setRating(Integer rating) {
        this.rating = rating;
    }

    /**
     * 获取响应截止时间
     */
    public Instant getResponseDueAt() {
        return responseDueAt;
    }

    /**
     * 设置响应截止时间
     */
    public void setResponseDueAt(Instant responseDueAt) {
        this.responseDueAt = responseDueAt;
    }

    /**
     * 获取处理人员
     */
    public Long getHandledBy() {
        return handledBy;
    }

    /**
     * 设置处理人员
     */
    public void setHandledBy(Long handledBy) {
        this.handledBy = handledBy;
    }

    /**
     * 获取管理员回复
     */
    public String getAdminResponse() {
        return adminResponse;
    }

    /**
     * 设置管理员回复
     */
    public void setAdminResponse(String adminResponse) {
        this.adminResponse = adminResponse;
    }

    /**
     * 获取回复时间
     */
    public Instant getResponseAt() {
        return responseAt;
    }

    /**
     * 设置回复时间
     */
    public void setResponseAt(Instant responseAt) {
        this.responseAt = responseAt;
    }

    /**
     * 获取解决时间
     */
    public Instant getResolvedAt() {
        return resolvedAt;
    }

    /**
     * 设置解决时间
     */
    public void setResolvedAt(Instant resolvedAt) {
        this.resolvedAt = resolvedAt;
    }

    /**
     * 获取分配给
     */
    public Long getAssignedTo() {
        return assignedTo;
    }

    /**
     * 设置分配给
     */
    public void setAssignedTo(Long assignedTo) {
        this.assignedTo = assignedTo;
    }

    /**
     * 获取分配人
     */
    public Long getAssignedBy() {
        return assignedBy;
    }

    /**
     * 设置分配人
     */
    public void setAssignedBy(Long assignedBy) {
        this.assignedBy = assignedBy;
    }

    /**
     * 获取分配时间
     */
    public Instant getAssignedAt() {
        return assignedAt;
    }

    /**
     * 设置分配时间
     */
    public void setAssignedAt(Instant assignedAt) {
        this.assignedAt = assignedAt;
    }

    /**
     * 获取内部备注
     */
    public String getInternalNotes() {
        return internalNotes;
    }

    /**
     * 设置内部备注
     */
    public void setInternalNotes(String internalNotes) {
        this.internalNotes = internalNotes;
    }
}
