package com.implatform.user.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.CreatedDate;

import lombok.Data;
import lombok.EqualsAndHashCode;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 用户名变更历史实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("username_history"),
    @Index(name = "idx_username_history_old_username", columnList = "old_username"),
    @Index(name = "idx_username_history_new_username", columnList = "new_username"),
    @Index(name = "idx_username_history_created_at", columnList = "created_at"),
    @Index(name = "idx_username_history_changed_by", columnList = "changed_by")
})
@EqualsAndHashCode(callSuper = false)
public class UsernameHistory {

    /**
     * 用户名变更历史ID
     */
    @Id
        private Long id;

    /**
     * 用户ID
     */
    @Column
    private Long userId;

    /**
     * 旧用户名
     */
    @Column
    private String oldUsername;

    /**
     * 新用户名
     */
    @Column
    private String newUsername;

    /**
     * 变更原因
     */
        @Column
    private ChangeReason changeReason = ChangeReason.USER_REQUEST;

    /**
     * 变更操作者ID
     */
    @Column("changed_by")
    private Long changedBy;

    /**
     * IP地址
     */
    @Column
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column("user_agent")
    private String userAgent;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column
    private LocalDateTime createdAt;

    /**
     * 变更原因枚举
     */
    public enum ChangeReason {
        /**
         * 用户请求
         */
        USER_REQUEST,
        /**
         * 管理员变更
         */
        ADMIN_CHANGE,
        /**
         * 政策违规
         */
        POLICY_VIOLATION,
        /**
         * 系统迁移
         */
        SYSTEM_MIGRATION,
        /**
         * 高级用户升级
         */
        PREMIUM_UPGRADE
    }

    /**
     * 检查是否为首次设置用户名
     */
    public boolean isFirstTimeSetup() {
        return oldUsername == null || oldUsername.trim().isEmpty();
    }

    /**
     * 检查是否为用户主动变更
     */
    public boolean isUserInitiated() {
        return changeReason == ChangeReason.USER_REQUEST;
    }

    /**
     * 检查是否为管理员操作
     */
    public boolean isAdminAction() {
        return changeReason == ChangeReason.ADMIN_CHANGE || 
               changeReason == ChangeReason.POLICY_VIOLATION;
    }

    /**
     * 检查是否为系统操作
     */
    public boolean isSystemAction() {
        return changeReason == ChangeReason.SYSTEM_MIGRATION;
    }

    /**
     * 检查是否为高级功能相关
     */
    public boolean isPremiumRelated() {
        return changeReason == ChangeReason.PREMIUM_UPGRADE;
    }

    /**
     * 获取变更原因描述
     */
    public String getChangeReasonDescription() {
        switch (changeReason) {
            case USER_REQUEST:
                return "用户请求";
            case ADMIN_CHANGE:
                return "管理员变更";
            case POLICY_VIOLATION:
                return "政策违规";
            case SYSTEM_MIGRATION:
                return "系统迁移";
            case PREMIUM_UPGRADE:
                return "高级用户升级";
            default:
                return "未知原因";
        }
    }

    /**
     * 获取变更类型
     */
    public String getChangeType() {
        if (isFirstTimeSetup()) {
            return "首次设置";
        } else {
            return "用户名变更";
        }
    }

    /**
     * 获取操作者描述
     */
    public String getOperatorDescription() {
        if (changedBy == null) {
            return "系统";
        } else if (changedBy.equals(userId)) {
            return "用户本人";
        } else {
            return "管理员 (ID: " + changedBy + ")";
        }
    }

    /**
     * 获取变更时间描述
     */
    public String getChangeTimeDescription() {
        if (createdAt == null) {
            return "未知时间";
        }
        
        LocalDateTime now = LocalDateTime.now();
        java.time.Duration duration = java.time.Duration.between(createdAt, now);
        
        long seconds = duration.getSeconds();
        if (seconds < 60) {
            return seconds + "秒前";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分钟前";
        } else if (seconds < 86400) {
            return (seconds / 3600) + "小时前";
        } else {
            return (seconds / 86400) + "天前";
        }
    }

    /**
     * 获取变更摘要
     */
    public String getChangeSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (isFirstTimeSetup()) {
            summary.append("设置用户名为 @").append(newUsername);
        } else {
            summary.append("从 @").append(oldUsername).append(" 变更为 @").append(newUsername);
        }
        
        summary.append(" (").append(getChangeReasonDescription()).append(")");
        summary.append(" - ").append(getChangeTimeDescription());
        
        return summary.toString();
    }

    /**
     * 获取详细变更信息
     */
    public String getDetailedChangeInfo() {
        StringBuilder info = new StringBuilder();
        info.append("变更类型: ").append(getChangeType());
        
        if (!isFirstTimeSetup()) {
            info.append("\n旧用户名: @").append(oldUsername);
        }
        
        info.append("\n新用户名: @").append(newUsername);
        info.append("\n变更原因: ").append(getChangeReasonDescription());
        info.append("\n操作者: ").append(getOperatorDescription());
        info.append("\n变更时间: ").append(createdAt);
        
        if (ipAddress != null && !ipAddress.trim().isEmpty()) {
            info.append("\nIP地址: ").append(ipAddress);
        }
        
        return info.toString();
    }

    /**
     * 检查变更是否在指定时间内
     */
    public boolean isWithinTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (createdAt == null) {
            return false;
        }
        
        if (startTime != null && createdAt.isBefore(startTime)) {
            return false;
        }
        
        if (endTime != null && createdAt.isAfter(endTime)) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查是否为最近的变更
     */
    public boolean isRecentChange(int hours) {
        if (createdAt == null) {
            return false;
        }
        
        LocalDateTime cutoff = LocalDateTime.now().minusHours(hours);
        return createdAt.isAfter(cutoff);
    }

    /**
     * 获取变更风险级别
     */
    public String getRiskLevel() {
        switch (changeReason) {
            case POLICY_VIOLATION:
                return "高风险";
            case ADMIN_CHANGE:
                return "中风险";
            case USER_REQUEST:
            case PREMIUM_UPGRADE:
                return "低风险";
            case SYSTEM_MIGRATION:
                return "无风险";
            default:
                return "未知风险";
        }
    }

    /**
     * 检查是否需要审核
     */
    public boolean requiresReview() {
        return changeReason == ChangeReason.POLICY_VIOLATION ||
               (changeReason == ChangeReason.ADMIN_CHANGE && !changedBy.equals(userId));
    }

    /**
     * 获取变更统计信息
     */
    public String getChangeStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("用户ID: ").append(userId);
        stats.append("\n变更次数: 1"); // 这里可以扩展为统计总变更次数
        stats.append("\n风险级别: ").append(getRiskLevel());
        
        if (requiresReview()) {
            stats.append("\n需要审核: 是");
        }
        
        return stats.toString();
    }
}
