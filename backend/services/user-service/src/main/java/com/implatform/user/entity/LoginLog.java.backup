package com.implatform.user.entity;

import java.time.Instant;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 登录日志实体类 - IM平台安全审计和行为分析实体
 *
 * <p><strong>业务用途</strong>：
 * 记录IM平台中所有用户的登录、登出和认证相关操作的详细日志，支持安全审计、异常检测、
 * 行为分析和合规要求。提供完整的用户认证活动追踪和风险评估能力。
 *
 * <p><strong>核心功能特性</strong>：
 * <ul>
 *   <li><strong>全面日志记录</strong>：登录成功/失败、登出、令牌刷新等所有认证活动</li>
 *   <li><strong>多维度信息</strong>：IP地址、设备信息、地理位置、用户代理等</li>
 *   <li><strong>安全分析</strong>：可疑登录检测、风险评分、异常行为识别</li>
 *   <li><strong>会话跟踪</strong>：完整的会话生命周期管理和持续时间统计</li>
 *   <li><strong>多种登录方式</strong>：支持密码、验证码、QR码、第三方等登录类型</li>
 *   <li><strong>失败原因分析</strong>：详细的登录失败原因记录和统计</li>
 * </ul>
 *
 * <p><strong>使用场景</strong>：
 * <ul>
 *   <li>用户登录行为审计和安全监控</li>
 *   <li>异常登录检测和风险预警</li>
 *   <li>用户活跃度分析和使用统计</li>
 *   <li>安全事件调查和取证分析</li>
 *   <li>合规报告生成和监管要求</li>
 *   <li>用户体验优化和问题诊断</li>
 * </ul>
 *
 * <p><strong>业务上下文</strong>：
 * 作为IM平台的安全基础设施，LoginLog与用户管理、设备管理、风控系统、监控告警等
 * 多个业务域协同工作。通过详细的日志记录，支持平台的安全运营和业务决策。
 *
 * <p><strong>关键关系</strong>：
 * <ul>
 *   <li>多对一关系：LoginLog → User（日志属于用户）</li>
 *   <li>多对一关系：LoginLog → Device（日志关联设备）</li>
 *   <li>一对一关系：LoginLog → AuthToken（登录成功时关联令牌）</li>
 *   <li>一对多关系：LoginLog → SecurityEvent（触发安全事件）</li>
 * </ul>
 *
 * <p><strong>安全特性</strong>：
 * <ul>
 *   <li>可疑登录自动检测和标记</li>
 *   <li>基于多维度信息的风险评分</li>
 *   <li>地理位置异常检测</li>
 *   <li>设备指纹识别和验证</li>
 *   <li>登录频率和模式分析</li>
 * </ul>
 *
 * <p><strong>实际应用示例</strong>：
 * <pre>
 * // 记录成功登录
 * LoginLog successLog = LoginLog.builder()
 *     .userId(userId)
 *     .username(username)
 *     .deviceId(deviceId)
 *     .loginType(LoginType.USERNAME_PASSWORD)
 *     .status(LoginStatus.SUCCESS)
 *     .clientIp(clientIp)
 *     .userAgent(userAgent)
 *     .location(location)
 *     .sessionId(sessionId)
 *     .build();
 *
 * // 记录失败登录
 * LoginLog failLog = LoginLog.builder()
 *     .username(username)
 *     .loginType(LoginType.USERNAME_PASSWORD)
 *     .status(LoginStatus.WRONG_PASSWORD)
 *     .failureReason("密码错误")
 *     .clientIp(clientIp)
 *     .isSuspicious(true)
 *     .riskScore(75)
 *     .build();
 *
 * // 标记登出
 * log.markAsLoggedOut(LogoutType.MANUAL);
 * </pre>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 login_logs 表，包含用户ID、设备ID、IP地址、创建时间等字段的索引，
 * 支持高效的日志查询、统计分析和安全监控操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "login_logs", indexes = {
    @Index(name = "idx_login_logs_user_id", columnList = "user_id"),
    @Index(name = "idx_login_logs_device_id", columnList = "device_id"),
    @Index(name = "idx_login_logs_login_type", columnList = "login_type"),
    @Index(name = "idx_login_logs_status", columnList = "status"),
    @Index(name = "idx_login_logs_created_at", columnList = "created_at"),
    @Index(name = "idx_login_logs_client_ip", columnList = "client_ip")
})
@EntityListeners(AuditingEntityListener.class)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户ID（可为空，登录失败时可能没有用户ID）
     */
    @Column(name = "user_id")
    private Long userId;

    /**
     * 用户名或手机号（用于记录登录尝试）
     */
    @Column(name = "username", length = 100)
    @Size(max = 100, message = "Username cannot exceed 100 characters")
    private String username;

    /**
     * 设备ID
     */
    @Column(name = "device_id", length = 64)
    @Size(max = 64, message = "Device ID cannot exceed 64 characters")
    private String deviceId;

    /**
     * 登录类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "login_type", nullable = false, length = 30)
    @NotNull(message = "Login type cannot be null")
    private LoginType loginType;

    /**
     * 登录状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 30)
    @NotNull(message = "Login status cannot be null")
    private LoginStatus status;

    /**
     * 客户端IP地址
     */
    @Column(name = "client_ip", nullable = false, length = 45)
    @NotBlank(message = "Client IP cannot be blank")
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Column(name = "user_agent", length = 500)
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    private String userAgent;

    /**
     * 设备信息
     */
    @Column(name = "device_info", length = 200)
    @Size(max = 200, message = "Device info cannot exceed 200 characters")
    private String deviceInfo;

    /**
     * 地理位置信息
     */
    @Column(name = "location", length = 100)
    @Size(max = 100, message = "Location cannot exceed 100 characters")
    private String location;

    /**
     * 国家代码
     */
    @Column(name = "country_code", length = 2)
    @Size(max = 2, message = "Country code cannot exceed 2 characters")
    private String countryCode;

    /**
     * 城市
     */
    @Column(name = "city", length = 50)
    @Size(max = 50, message = "City cannot exceed 50 characters")
    private String city;

    /**
     * 失败原因（登录失败时记录）
     */
    @Column(name = "failure_reason", length = 200)
    @Size(max = 200, message = "Failure reason cannot exceed 200 characters")
    private String failureReason;

    /**
     * 登录持续时间（毫秒）
     */
    @Column(name = "duration_ms")
    private Long durationMs;

    /**
     * 是否为可疑登录
     */
    @Column(name = "is_suspicious", nullable = false)
    @Builder.Default
    private Boolean isSuspicious = false;

    /**
     * 风险评分（0-100）
     */
    @Column(name = "risk_score")
    private Integer riskScore;

    /**
     * 登出时间
     */
    @Column(name = "logout_at")
    private Instant logoutAt;

    /**
     * 登出类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "logout_type", length = 20)
    private LogoutType logoutType;

    /**
     * 会话ID
     */
    @Column(name = "session_id", length = 64)
    @Size(max = 64, message = "Session ID cannot exceed 64 characters")
    private String sessionId;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 登录类型枚举
     */
    public enum LoginType {
        /**
         * 用户名密码登录
         */
        USERNAME_PASSWORD,
        
        /**
         * 手机号密码登录
         */
        PHONE_PASSWORD,
        
        /**
         * 手机号验证码登录
         */
        PHONE_SMS,
        
        /**
         * 邮箱密码登录
         */
        EMAIL_PASSWORD,
        
        /**
         * 邮箱验证码登录
         */
        EMAIL_CODE,
        
        /**
         * QR码登录
         */
        QR_CODE,
        
        /**
         * 第三方登录
         */
        THIRD_PARTY,
        
        /**
         * 令牌刷新
         */
        TOKEN_REFRESH,
        
        /**
         * 自动登录
         */
        AUTO_LOGIN
    }

    /**
     * 登录状态枚举
     */
    public enum LoginStatus {
        /**
         * 登录成功
         */
        SUCCESS,
        
        /**
         * 登录失败
         */
        FAILED,
        
        /**
         * 账户被锁定
         */
        ACCOUNT_LOCKED,
        
        /**
         * 账户被禁用
         */
        ACCOUNT_DISABLED,
        
        /**
         * 密码错误
         */
        WRONG_PASSWORD,
        
        /**
         * 验证码错误
         */
        WRONG_VERIFICATION_CODE,
        
        /**
         * 账户不存在
         */
        ACCOUNT_NOT_FOUND,
        
        /**
         * 需要验证
         */
        VERIFICATION_REQUIRED,
        
        /**
         * 被风控拦截
         */
        RISK_BLOCKED
    }

    /**
     * 登出类型枚举
     */
    public enum LogoutType {
        /**
         * 主动登出
         */
        MANUAL,
        
        /**
         * 自动登出（超时）
         */
        AUTO_TIMEOUT,
        
        /**
         * 强制登出
         */
        FORCED,
        
        /**
         * 令牌过期
         */
        TOKEN_EXPIRED,
        
        /**
         * 设备被移除
         */
        DEVICE_REMOVED,
        
        /**
         * 账户被禁用
         */
        ACCOUNT_DISABLED
    }

    /**
     * 标记为登出
     */
    public void markAsLoggedOut(LogoutType logoutType) {
        this.logoutAt = Instant.now();
        this.logoutType = logoutType;
        if (this.createdAt != null) {
            this.durationMs = logoutAt.toEpochMilli() - createdAt.toEpochMilli();
        }
    }

    /**
     * 检查是否为成功登录
     */
    public boolean isSuccessfulLogin() {
        return status == LoginStatus.SUCCESS;
    }

    /**
     * 检查是否为失败登录
     */
    public boolean isFailedLogin() {
        return status != LoginStatus.SUCCESS;
    }
}
