package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 两步验证实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_two_factor_auth")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TwoFactorAuth {
    
    @Id
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "secret_key", nullable = false, length = 32)
    private String secretKey;
    
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = false;
    
    @Column(name = "backup_codes", columnDefinition = "text[]")
    private String[] backupCodes;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    /**
     * 构造函数
     */
    public TwoFactorAuth(Long userId, String secretKey) {
        this.userId = userId;
        this.secretKey = secretKey;
        this.enabled = false;
    }
    
    /**
     * 启用两步验证
     */
    public void enable() {
        this.enabled = true;
    }
    
    /**
     * 禁用两步验证
     */
    public void disable() {
        this.enabled = false;
    }
    
    /**
     * 检查是否已启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.enabled);
    }
    
    /**
     * 设置备份码
     */
    public void setBackupCodes(String[] backupCodes) {
        this.backupCodes = backupCodes != null ? backupCodes.clone() : null;
    }
    
    /**
     * 获取备份码
     */
    public String[] getBackupCodes() {
        return backupCodes != null ? backupCodes.clone() : null;
    }
}
