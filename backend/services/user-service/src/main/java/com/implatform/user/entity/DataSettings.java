package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.Instant;

/**
 * 用户数据使用设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_data_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataSettings {

    @Id
    @Column("user_id")
    private Long userId;
    
    // ==================== 自动下载设置 ====================
    
    /**
     * 自动下载照片
     */
    @Column("auto_download_photos")
    private Boolean autoDownloadPhotos = true;

    /**
     * 自动下载视频
     */
    @Column("auto_download_videos")
    private Boolean autoDownloadVideos = false;

    /**
     * 自动下载文档
     */
    @Column("auto_download_documents")
    private Boolean autoDownloadDocuments = false;

    /**
     * 自动下载语音消息
     */
    @Column("auto_download_voice_messages")
    private Boolean autoDownloadVoiceMessages = true;

    /**
     * 自动下载贴纸
     */
    @Column("auto_download_stickers")
    private Boolean autoDownloadStickers = true;

    /**
     * 自动下载GIF
     */
    @Column("auto_download_gifs")
    private Boolean autoDownloadGifs = true;

    // ==================== 网络类型限制 ====================

    /**
     * 仅WiFi下载
     */
    @Column("wifi_only_download")
    private Boolean wifiOnlyDownload = false;

    /**
     * 漫游时下载
     */
    @Column("roaming_download")
    private Boolean roamingDownload = false;
    
    /**
     * 低电量时下载
     */
    @Column("low_battery_download")
    private Boolean lowBatteryDownload = true;
    
    /**
     * 最大文件大小限制（MB）
     */
    @Column("max_file_size_mb")
    private Integer maxFileSizeMb = 100;
    
    // ==================== 缓存设置 ====================
    
    /**
     * 最大缓存大小（MB）
     */
    @Column("max_cache_size_mb")
    private Integer maxCacheSizeMb = 1024;
    
    /**
     * 缓存保留天数
     */
    @Column("cache_retention_days")
    private Integer cacheRetentionDays = 30;
    
    /**
     * 自动清理缓存
     */
    @Column("auto_cleanup_enabled")
    private Boolean autoCleanupEnabled = true;
    
    /**
     * 清理间隔天数
     */
    @Column("cleanup_interval_days")
    private Integer cleanupIntervalDays = 7;
    
    /**
     * 保留收藏的媒体
     */
    @Column("keep_favorite_media")
    private Boolean keepFavoriteMedia = true;
    
    // ==================== 数据压缩设置 ====================
    
    /**
     * 压缩图片
     */
    @Column("compress_images")
    private Boolean compressImages = true;
    
    /**
     * 图片压缩质量（1-100）
     */
    @Column("image_compression_quality")
    private Integer imageCompressionQuality = 80;
    
    /**
     * 压缩视频
     */
    @Column("compress_videos")
    private Boolean compressVideos = true;
    
    /**
     * 视频压缩质量
     */
        @Column("video_compression_quality")
    private VideoQuality videoCompressionQuality = VideoQuality.MEDIUM;
    
    /**
     * 压缩语音消息
     */
    @Column("compress_voice_messages")
    private Boolean compressVoiceMessages = true;
    
    // ==================== 数据统计设置 ====================
    
    /**
     * 启用数据统计
     */
    @Column("data_statistics_enabled")
    private Boolean dataStatisticsEnabled = true;
    
    /**
     * 统计重置周期
     */
        @Column("statistics_reset_period")
    private StatisticsPeriod statisticsResetPeriod = StatisticsPeriod.MONTHLY;
    
    /**
     * 数据使用警告阈值（MB）
     */
    @Column("data_usage_warning_threshold_mb")
    private Integer dataUsageWarningThresholdMb = 500;
    
    /**
     * 启用数据使用警告
     */
    @Column("data_usage_warning_enabled")
    private Boolean dataUsageWarningEnabled = true;
    
    // ==================== 备份设置 ====================
    
    /**
     * 自动备份聊天记录
     */
    @Column("auto_backup_chats")
    private Boolean autoBackupChats = true;
    
    /**
     * 备份包含媒体
     */
    @Column("backup_include_media")
    private Boolean backupIncludeMedia = false;
    
    /**
     * 备份频率
     */
        @Column("backup_frequency")
    private BackupFrequency backupFrequency = BackupFrequency.WEEKLY;
    
    /**
     * 仅WiFi备份
     */
    @Column("backup_wifi_only")
    private Boolean backupWifiOnly = true;
    
    // ==================== 时间戳 ====================
    
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;
    
    // ==================== 关联关系 ====================
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    // ==================== 枚举定义 ====================
    
    /**
     * 视频质量枚举
     */
    public enum VideoQuality {
        LOW("低质量"),
        MEDIUM("中等质量"),
        HIGH("高质量"),
        ORIGINAL("原始质量");
        
        private final String description;
        
        VideoQuality(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 统计周期枚举
     */
    public enum StatisticsPeriod {
        DAILY("每日"),
        WEEKLY("每周"),
        MONTHLY("每月"),
        NEVER("从不重置");
        
        private final String description;
        
        StatisticsPeriod(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 备份频率枚举
     */
    public enum BackupFrequency {
        DAILY("每日"),
        WEEKLY("每周"),
        MONTHLY("每月"),
        MANUAL("手动");
        
        private final String description;
        
        BackupFrequency(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数 - 创建默认数据设置
     */
    public DataSettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查是否允许自动下载指定类型的文件
     */
    public boolean shouldAutoDownload(String fileType, boolean isWifi, boolean isRoaming, boolean isLowBattery) {
        // 检查网络条件
        if (wifiOnlyDownload && !isWifi) {
            return false;
        }
        
        if (isRoaming && !roamingDownload) {
            return false;
        }
        
        if (isLowBattery && !lowBatteryDownload) {
            return false;
        }
        
        // 检查文件类型
        return switch (fileType.toLowerCase()) {
            case "photo", "image" -> autoDownloadPhotos;
            case "video" -> autoDownloadVideos;
            case "document", "file" -> autoDownloadDocuments;
            case "voice", "audio" -> autoDownloadVoiceMessages;
            case "sticker" -> autoDownloadStickers;
            case "gif" -> autoDownloadGifs;
            default -> false;
        };
    }
    
    /**
     * 检查文件大小是否超过限制
     */
    public boolean isFileSizeAllowed(long fileSizeBytes) {
        if (maxFileSizeMb == null || maxFileSizeMb <= 0) {
            return true;
        }
        
        long maxSizeBytes = maxFileSizeMb * 1024L * 1024L;
        return fileSizeBytes <= maxSizeBytes;
    }
    
    /**
     * 获取图片压缩质量（0.0-1.0）
     */
    public float getImageCompressionQualityFloat() {
        if (imageCompressionQuality == null) {
            return 0.8f;
        }
        return Math.max(0.1f, Math.min(1.0f, imageCompressionQuality / 100.0f));
    }
    
    /**
     * 检查是否需要清理缓存
     */
    public boolean shouldCleanupCache() {
        return autoCleanupEnabled && cleanupIntervalDays != null && cleanupIntervalDays > 0;
    }
}
