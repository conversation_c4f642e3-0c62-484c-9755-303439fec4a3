package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * 语言包版本管理实体类
 * 
 * <p>该实体类用于管理语言包的版本信息，支持多版本并存、版本发布控制等功能。
 * 每个语言包可以有多个版本，但只能有一个当前激活版本。</p>
 * 
 * <p><strong>主要功能</strong>：
 * <ul>
 *   <li>语言包版本管理</li>
 *   <li>翻译完整度统计</li>
 *   <li>版本发布控制</li>
 *   <li>版本状态管理</li>
 *   <li>创建者追踪</li>
 * </ul>
 * 
 * <p><strong>数据库映射</strong>：
 * 映射到 language_pack_versions 表，包含语言包ID和版本号的复合唯一索引。
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "language_pack_versions", 
       indexes = {
           @Index(name = "idx_language_pack_versions_language_pack_id", columnList = "language_pack_id"),
           @Index(name = "idx_language_pack_versions_status", columnList = "status"),
           @Index(name = "idx_language_pack_versions_is_current", columnList = "is_current"),
           @Index(name = "idx_language_pack_versions_published_at", columnList = "published_at")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "unique_version", columnNames = {"language_pack_id", "version"})
       })
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class LanguagePackVersion {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的语言包
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "language_pack_id", nullable = false, 
                foreignKey = @ForeignKey(name = "fk_language_pack_versions_language_pack_id"))
    private LanguagePack languagePack;
    
    /**
     * 版本号
     * 格式：1.0.0、1.1.0、2.0.0 等
     */
    @NotBlank(message = "版本号不能为空")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    @Column(name = "version", nullable = false, length = 20)
    private String version;
    
    /**
     * 版本描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 总条目数
     */
    @Builder.Default
    @Min(value = 0, message = "总条目数不能小于0")
    @Column(name = "total_entries", nullable = false)
    private Integer totalEntries = 0;
    
    /**
     * 已翻译条目数
     */
    @Builder.Default
    @Min(value = 0, message = "已翻译条目数不能小于0")
    @Column(name = "translated_entries", nullable = false)
    private Integer translatedEntries = 0;
    
    /**
     * 翻译完整度百分比
     * 范围：0.00 - 100.00
     */
    @Builder.Default
    @DecimalMin(value = "0.00", message = "完整度不能小于0")
    @DecimalMax(value = "100.00", message = "完整度不能大于100")
    @Column(name = "completeness", precision = 5, scale = 2, nullable = false)
    private BigDecimal completeness = BigDecimal.ZERO;
    
    /**
     * 版本状态
     */
    @Builder.Default
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private VersionStatus status = VersionStatus.DRAFT;
    
    /**
     * 是否为当前版本
     * 每个语言包只能有一个当前版本
     */
    @Builder.Default
    @Column(name = "is_current", nullable = false)
    private Boolean isCurrent = false;
    
    /**
     * 发布时间
     */
    @Column(name = "published_at")
    private Instant publishedAt;
    
    /**
     * 创建者用户ID
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", 
                foreignKey = @ForeignKey(name = "fk_language_pack_versions_created_by"))
    private User createdBy;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(nullable = false, updatable = false, name = "created_at")
    private Instant createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(nullable = false, name = "updated_at")
    private Instant updatedAt;
    
    /**
     * 翻译条目列表（一对多关系）
     */
    @OneToMany(mappedBy = "languagePackVersion", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LanguagePackEntry> entries;
    
    /**
     * 版本状态枚举
     */
    public enum VersionStatus {
        /**
         * 草稿状态 - 正在编辑中
         */
        DRAFT,
        /**
         * 已发布状态 - 可供使用
         */
        PUBLISHED,
        /**
         * 已弃用状态 - 不再推荐使用
         */
        DEPRECATED
    }
    
    /**
     * 计算并更新翻译完整度
     */
    public void updateCompleteness() {
        if (totalEntries == 0) {
            this.completeness = BigDecimal.ZERO;
        } else {
            BigDecimal percentage = BigDecimal.valueOf(translatedEntries)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(totalEntries), 2, BigDecimal.ROUND_HALF_UP);
            this.completeness = percentage;
        }
    }
    
    /**
     * 发布版本
     */
    public void publish() {
        this.status = VersionStatus.PUBLISHED;
        this.publishedAt = Instant.now();
    }
    
    /**
     * 设置为当前版本
     */
    public void setAsCurrent() {
        this.isCurrent = true;
    }
    
    /**
     * 取消当前版本状态
     */
    public void unsetAsCurrent() {
        this.isCurrent = false;
    }
    
    /**
     * 弃用版本
     */
    public void deprecate() {
        this.status = VersionStatus.DEPRECATED;
        this.isCurrent = false;
    }
    
    /**
     * 检查是否已发布
     * 
     * @return true如果状态为PUBLISHED
     */
    public boolean isPublished() {
        return status == VersionStatus.PUBLISHED;
    }
    
    /**
     * 检查是否为草稿状态
     * 
     * @return true如果状态为DRAFT
     */
    public boolean isDraft() {
        return status == VersionStatus.DRAFT;
    }
    
    /**
     * 检查是否已弃用
     * 
     * @return true如果状态为DEPRECATED
     */
    public boolean isDeprecated() {
        return status == VersionStatus.DEPRECATED;
    }
    
    /**
     * 获取完整度百分比（double类型）
     * 
     * @return 完整度百分比
     */
    public double getCompletenessPercentage() {
        return completeness != null ? completeness.doubleValue() : 0.0;
    }
    
    /**
     * 获取版本显示名称
     * 格式：版本号 (状态)
     * 
     * @return 版本显示名称
     */
    public String getDisplayName() {
        String statusText = switch (status) {
            case DRAFT -> "草稿";
            case PUBLISHED -> "已发布";
            case DEPRECATED -> "已弃用";
        };
        
        String currentText = Boolean.TRUE.equals(isCurrent) ? " [当前]" : "";
        return String.format("%s (%s)%s", version, statusText, currentText);
    }
    
    /**
     * 增加翻译条目数量
     * 
     * @param count 增加的数量
     */
    public void addTranslatedEntries(int count) {
        this.translatedEntries += count;
        updateCompleteness();
    }
    
    /**
     * 设置总条目数并更新完整度
     * 
     * @param totalEntries 总条目数
     */
    public void setTotalEntriesAndUpdate(int totalEntries) {
        this.totalEntries = totalEntries;
        updateCompleteness();
    }
}
