package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import java.util.Objects;

/**
 * 用户等级配置实体
 * 定义各等级的经验要求、称号和特权
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_level_config"),
    @Index(name = "idx_level_config_required_exp", columnList = "required_exp"),
    @Index(name = "idx_level_config_active", columnList = "is_active"),
    @Index(name = "idx_level_config_level_active", columnList = "level, is_active")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserLevelConfig {

    /**
     * 主键ID
     */
    @Id
        @Column("id")
    private Long id;

    /**
     * 等级
     */
    @NotNull(message = "Level cannot be null")
    @Min(value = 1, message = "Level must be at least 1")
    @Column("level")
    private Integer level;

    /**
     * 升级所需经验值
     */
    @NotNull(message = "Required experience cannot be null")
    @Min(value = 0, message = "Required experience cannot be negative")
    @Column("required_exp")
    private Long requiredExp;

    /**
     * 等级名称
     */
    @NotBlank(message = "Level name cannot be blank")
    @Size(max = 100, message = "Level name cannot exceed 100 characters")
    @Column("level_name")
    private String levelName;

    /**
     * 等级图标URL
     */
    @Size(max = 500, message = "Icon URL cannot exceed 500 characters")
    @Column("icon_url")
    private String iconUrl;

    /**
     * 等级特权（JSON格式）
     */
    @Size(max = 2000, message = "Privileges cannot exceed 2000 characters")
    @Column("privileges")
    private String privileges;

    /**
     * 等级描述
     */
    @Size(max = 500, message = "Description cannot exceed 500 characters")
    @Column("description")
    private String description;

    /**
     * 等级颜色（十六进制）
     */
    @Size(max = 7, message = "Level color must be a valid hex color")
    @Column("level_color")
    private String levelColor;

    /**
     * 等级边框样式
     */
    @Size(max = 100, message = "Border style cannot exceed 100 characters")
    @Column("border_style")
    private String borderStyle;

    /**
     * 经验加成倍率（百分比）
     */
    @Builder.Default
    @Column("exp_bonus")
    private Integer expBonus = 0;

    /**
     * 每日经验上限加成
     */
    @Builder.Default
    @Column("daily_exp_bonus")
    private Integer dailyExpBonus = 0;

    /**
     * 群组数量上限
     */
    @Builder.Default
    @Column("max_groups")
    private Integer maxGroups = 10;

    /**
     * 好友数量上限
     */
    @Builder.Default
    @Column("max_friends")
    private Integer maxFriends = 100;

    /**
     * 文件上传大小限制（MB）
     */
    @Builder.Default
    @Column("max_file_size_mb")
    private Integer maxFileSizeMb = 10;

    /**
     * 是否可以创建群组
     */
    @Builder.Default
    @Column("can_create_groups")
    private Boolean canCreateGroups = true;

    /**
     * 是否可以使用高级功能
     */
    @Builder.Default
    @Column("can_use_advanced_features")
    private Boolean canUseAdvancedFeatures = false;

    /**
     * 是否显示等级标识
     */
    @Builder.Default
    @Column("show_level_badge")
    private Boolean showLevelBadge = true;

    /**
     * 排序权重
     */
    @Builder.Default
    @Column("sort_order")
    private Integer sortOrder = 0;

    /**
     * 是否激活
     */
    @NotNull(message = "Is active cannot be null")
    @Builder.Default
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 创建人ID
     */
    @Column("created_by")
    private Long createdBy;

    /**
     * 最后修改人ID
     */
    @Column("last_modified_by")
    private Long lastModifiedBy;

    /**
     * 修改备注
     */
    @Size(max = 500, message = "Modification notes cannot exceed 500 characters")
    @Column("modification_notes")
    private String modificationNotes;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 创建人实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", insertable = false, updatable = false)
    private User creator;

    /**
     * 最后修改人实体关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "last_modified_by", insertable = false, updatable = false)
    private User lastModifier;

    /**
     * 检查是否为VIP等级
     * 
     * @return 是否为VIP等级
     */
    public boolean isVipLevel() {
        return level != null && level >= 10;
    }

    /**
     * 检查是否为高级等级
     * 
     * @return 是否为高级等级
     */
    public boolean isAdvancedLevel() {
        return level != null && level >= 5;
    }

    /**
     * 获取总经验加成
     * 
     * @return 总经验加成百分比
     */
    public Integer getTotalExpBonus() {
        return expBonus + dailyExpBonus;
    }

    /**
     * 检查是否有特定特权
     * 
     * @param privilege 特权名称
     * @return 是否拥有特权
     */
    public boolean hasPrivilege(String privilege) {
        if (privileges == null || privilege == null) {
            return false;
        }
        return privileges.contains(privilege);
    }

    /**
     * 获取等级显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        if (levelName != null && !levelName.trim().isEmpty()) {
            return String.format("Lv.%d %s", level, levelName);
        }
        return String.format("Lv.%d", level);
    }

    /**
     * 检查经验值是否足够升级
     * 
     * @param currentExp 当前经验值
     * @return 是否可以升级
     */
    public boolean canLevelUp(Long currentExp) {
        return currentExp != null && requiredExp != null && currentExp >= requiredExp;
    }

    /**
     * 获取升级进度百分比
     * 
     * @param currentExp 当前经验值
     * @param previousLevelExp 上一级经验要求
     * @return 进度百分比
     */
    public Double getLevelUpProgress(Long currentExp, Long previousLevelExp) {
        if (currentExp == null || requiredExp == null) {
            return 0.0;
        }
        
        Long baseExp = previousLevelExp != null ? previousLevelExp : 0L;
        Long expRange = requiredExp - baseExp;
        Long currentProgress = currentExp - baseExp;
        
        if (expRange <= 0) {
            return 100.0;
        }
        
        return Math.min(100.0, Math.max(0.0, (currentProgress.doubleValue() / expRange.doubleValue()) * 100));
    }

    /**
     * 更新配置
     * 
     * @param modifierId 修改人ID
     * @param notes 修改备注
     */
    public void updateConfiguration(Long modifierId, String notes) {
        this.lastModifiedBy = modifierId;
        this.modificationNotes = notes;
    }

    /**
     * 激活配置
     * 
     * @param modifierId 修改人ID
     */
    public void activate(Long modifierId) {
        this.isActive = true;
        updateConfiguration(modifierId, "Activated level configuration");
    }

    /**
     * 停用配置
     * 
     * @param modifierId 修改人ID
     */
    public void deactivate(Long modifierId) {
        this.isActive = false;
        updateConfiguration(modifierId, "Deactivated level configuration");
    }

    /**
     * 验证配置的合理性
     * 
     * @return 验证错误信息，null表示验证通过
     */
    public String validateConfiguration() {
        if (level == null || level < 1) {
            return "等级必须大于等于1";
        }
        
        if (requiredExp == null || requiredExp < 0) {
            return "所需经验值不能为负数";
        }
        
        if (levelName == null || levelName.trim().isEmpty()) {
            return "等级名称不能为空";
        }
        
        if (maxGroups != null && maxGroups < 0) {
            return "群组数量上限不能为负数";
        }
        
        if (maxFriends != null && maxFriends < 0) {
            return "好友数量上限不能为负数";
        }
        
        if (maxFileSizeMb != null && maxFileSizeMb < 0) {
            return "文件大小限制不能为负数";
        }
        
        if (levelColor != null && !levelColor.matches("^#[0-9A-Fa-f]{6}$")) {
            return "等级颜色必须是有效的十六进制颜色值";
        }
        
        return null; // 验证通过
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserLevelConfig that = (UserLevelConfig) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserLevelConfig{" +
                "id=" + id +
                ", level=" + level +
                ", levelName='" + levelName + '\'' +
                ", requiredExp=" + requiredExp +
                ", isActive=" + isActive +
                '}';
    }
}
