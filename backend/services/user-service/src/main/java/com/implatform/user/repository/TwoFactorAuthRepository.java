package com.implatform.user.repository;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.TwoFactorAuth;

/**
 * 两步验证Repository
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface TwoFactorAuthRepository extends ReactiveCrudRepository<TwoFactorAuth, Long> {

    /**
     * 根据用户ID查找两步验证信息
     */
    Mono<TwoFactorAuth> findByUserId(Long userId);

    /**
     * 检查用户是否启用了两步验证
     */
    @Query("SELECT COUNT(*) > 0 FROM two_factor_auth WHERE user_id = :userId AND enabled = true")
    Mono<Boolean> existsByUserIdAndEnabled(Long userId);

    /**
     * 根据用户ID和启用状态查找
     */
    Mono<TwoFactorAuth> findByUserIdAndEnabled(Long userId, Boolean enabled);

    /**
     * 删除用户的两步验证信息
     */
    Mono<Void> deleteByUserId(Long userId);

    /**
     * 检查用户是否存在两步验证记录
     */
    Mono<Boolean> existsByUserId(Long userId);

    /**
     * 统计启用两步验证的用户数量
     */
    @Query("SELECT COUNT(*) FROM two_factor_auth WHERE enabled = true")
    Mono<Long> countEnabledTwoFactorAuth();
}
