package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备会话实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("device_sessions"),
        @Index(name = "idx_device_sessions_device_id", columnList = "device_id"),
        @Index(name = "idx_device_sessions_session_token", columnList = "session_token"),
        @Index(name = "idx_device_sessions_status", columnList = "session_status"),
        @Index(name = "idx_device_sessions_last_active", columnList = "last_active_time")
    },
    uniqueConstraints = {
        @UniqueConstraint(name = "uk_device_sessions_token", columnNames = {"session_token"})
    }
)
@EqualsAndHashCode(callSuper = false)
public class DeviceSession {

    /**
     * 会话ID
     */
    @Id
        private Long id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 设备ID
     */
    @Column("device_id")
    private Long deviceId;

    /**
     * 会话令牌
     */
    @Column("session_token")
    private String sessionToken;

    /**
     * 会话状态
     */
        @Column("session_status")
    private SessionStatus sessionStatus = SessionStatus.ACTIVE;

    /**
     * IP地址
     */
    @Column("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column("user_agent")
    private String userAgent;

    /**
     * 登录时间
     */
    @Column("login_time")
    private LocalDateTime loginTime;

    /**
     * 最后活跃时间
     */
    @Column("last_active_time")
    private LocalDateTime lastActiveTime;

    /**
     * 会话过期时间
     */
    @Column("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 地理位置
     */
    @Column("location")
    private String location;

    /**
     * 国家
     */
    @Column("country")
    private String country;

    /**
     * 城市
     */
    @Column("city")
    private String city;

    /**
     * 时区
     */
    @Column("timezone")
    private String timezone;

    /**
     * 会话持续时间（秒）
     */
    @Column("session_duration")
    private Long sessionDuration;

    /**
     * 是否为当前会话
     */
    @Column("is_current")
    private Boolean isCurrent = false;

    /**
     * 注销时间
     */
    @Column("logout_time")
    private LocalDateTime logoutTime;

    /**
     * 注销原因
     */
    @Column("logout_reason")
    private String logoutReason;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;

    // ==================== 枚举定义 ====================

    /**
     * 会话状态枚举
     */
    public enum SessionStatus {
        ACTIVE("活跃"), INACTIVE("非活跃"), EXPIRED("已过期"), LOGGED_OUT("已注销"),
        KICKED_OUT("被踢出"), LOCKED("已锁定"), ABNORMAL("异常");

        private final String description;
        SessionStatus(String description) { this.description = description; }
        public String getDescription() { return description; }
    }

    // ==================== 业务方法 ====================

    /**
     * 获取位置显示名称
     */
    public String getLocationDisplayName() {
        if (city != null && country != null) {
            return city + ", " + country;
        } else if (country != null) {
            return country;
        } else if (location != null) {
            return location;
        }
        return "未知位置";
    }

    /**
     * 获取会话持续时间描述
     */
    public String getSessionDurationDescription() {
        if (loginTime == null) {
            return "未知";
        }
        
        LocalDateTime endTime = logoutTime != null ? logoutTime : LocalDateTime.now();
        long durationMinutes = java.time.Duration.between(loginTime, endTime).toMinutes();
        
        if (durationMinutes < 1) {
            return "不到1分钟";
        } else if (durationMinutes < 60) {
            return durationMinutes + "分钟";
        } else if (durationMinutes < 1440) { // 24小时
            long hours = durationMinutes / 60;
            long minutes = durationMinutes % 60;
            return hours + "小时" + (minutes > 0 ? minutes + "分钟" : "");
        } else {
            long days = durationMinutes / 1440;
            long hours = (durationMinutes % 1440) / 60;
            return days + "天" + (hours > 0 ? hours + "小时" : "");
        }
    }

    /**
     * 检查会话是否过期
     */
    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 检查会话是否活跃
     */
    public boolean isActive() {
        return sessionStatus == SessionStatus.ACTIVE && !isExpired();
    }
}
