package com.implatform.user.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户统计信息实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_statistics")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserStatistics {
    
    @Id
    @Column("user_id")
    private Long userId;
    
    // ==================== 消息统计 ====================
    
    /**
     * 总发送消息数
     */
    @Column("total_messages_sent")
    private Long totalMessagesSent = 0L;
    
    /**
     * 总接收消息数
     */
    @Column("total_messages_received")
    private Long totalMessagesReceived = 0L;
    
    /**
     * 今日发送消息数
     */
    @Column("today_messages_sent")
    private Long todayMessagesSent = 0L;
    
    /**
     * 今日接收消息数
     */
    @Column("today_messages_received")
    private Long todayMessagesReceived = 0L;
    
    /**
     * 本周发送消息数
     */
    @Column("week_messages_sent")
    private Long weekMessagesSent = 0L;
    
    /**
     * 本月发送消息数
     */
    @Column("month_messages_sent")
    private Long monthMessagesSent = 0L;
    
    // ==================== 通话统计 ====================
    
    /**
     * 总通话次数
     */
    @Column("total_calls")
    private Long totalCalls = 0L;
    
    /**
     * 总通话时长（秒）
     */
    @Column("total_call_duration_seconds")
    private Long totalCallDurationSeconds = 0L;
    
    /**
     * 语音通话次数
     */
    @Column("voice_calls_count")
    private Long voiceCallsCount = 0L;
    
    /**
     * 视频通话次数
     */
    @Column("video_calls_count")
    private Long videoCallsCount = 0L;
    
    /**
     * 今日通话次数
     */
    @Column("today_calls")
    private Long todayCalls = 0L;
    
    /**
     * 今日通话时长（秒）
     */
    @Column("today_call_duration_seconds")
    private Long todayCallDurationSeconds = 0L;
    
    // ==================== 数据使用统计 ====================
    
    /**
     * 总数据使用量（字节）
     */
    @Column("total_data_used_bytes")
    private Long totalDataUsedBytes = 0L;
    
    /**
     * 今日数据使用量（字节）
     */
    @Column("today_data_used_bytes")
    private Long todayDataUsedBytes = 0L;
    
    /**
     * 本周数据使用量（字节）
     */
    @Column("week_data_used_bytes")
    private Long weekDataUsedBytes = 0L;
    
    /**
     * 本月数据使用量（字节）
     */
    @Column("month_data_used_bytes")
    private Long monthDataUsedBytes = 0L;
    
    /**
     * 上传数据量（字节）
     */
    @Column("upload_data_bytes")
    private Long uploadDataBytes = 0L;
    
    /**
     * 下载数据量（字节）
     */
    @Column("download_data_bytes")
    private Long downloadDataBytes = 0L;
    
    // ==================== 活跃度统计 ====================
    
    /**
     * 连续活跃天数
     */
    @Column("daily_active_streak")
    private Integer dailyActiveStreak = 0;
    
    /**
     * 最大连续活跃天数
     */
    @Column("max_daily_active_streak")
    private Integer maxDailyActiveStreak = 0;
    
    /**
     * 最后活跃日期
     */
    @Column("last_active_date")
    private LocalDate lastActiveDate;
    
    /**
     * 总在线时长（秒）
     */
    @Column("total_online_duration_seconds")
    private Long totalOnlineDurationSeconds = 0L;
    
    /**
     * 今日在线时长（秒）
     */
    @Column("today_online_duration_seconds")
    private Long todayOnlineDurationSeconds = 0L;
    
    /**
     * 平均每日在线时长（秒）
     */
    @Column("avg_daily_online_duration_seconds")
    private Long avgDailyOnlineDurationSeconds = 0L;
    
    // ==================== 社交统计 ====================
    
    /**
     * 好友数量
     */
    @Column("friends_count")
    private Integer friendsCount = 0;
    
    /**
     * 群组数量
     */
    @Column("groups_count")
    private Integer groupsCount = 0;
    
    /**
     * 频道数量
     */
    @Column("channels_count")
    private Integer channelsCount = 0;
    
    /**
     * 创建的群组数量
     */
    @Column("created_groups_count")
    private Integer createdGroupsCount = 0;
    
    /**
     * 管理的群组数量
     */
    @Column("admin_groups_count")
    private Integer adminGroupsCount = 0;
    
    // ==================== 媒体统计 ====================
    
    /**
     * 发送的图片数量
     */
    @Column("photos_sent")
    private Long photosSent = 0L;
    
    /**
     * 发送的视频数量
     */
    @Column("videos_sent")
    private Long videosSent = 0L;
    
    /**
     * 发送的文档数量
     */
    @Column("documents_sent")
    private Long documentsSent = 0L;
    
    /**
     * 发送的语音消息数量
     */
    @Column("voice_messages_sent")
    private Long voiceMessagesSent = 0L;
    
    /**
     * 发送的贴纸数量
     */
    @Column("stickers_sent")
    private Long stickersSent = 0L;
    
    // ==================== 成就统计 ====================
    
    /**
     * 获得的徽章数量
     */
    @Column("badges_earned")
    private Integer badgesEarned = 0;
    
    /**
     * 完成的成就数量
     */
    @Column("achievements_completed")
    private Integer achievementsCompleted = 0;
    
    /**
     * 用户等级
     */
    @Column("level")
    private Integer level = 1;

    /**
     * 经验值
     */
    @Column("experience_points")
    private Long experiencePoints = 0L;

    /**
     * 等级进度百分比
     */
    @Column("level_progress_percentage")
    private BigDecimal levelProgressPercentage = BigDecimal.ZERO;
    
    // ==================== 时间戳 ====================
    
    /**
     * 统计开始日期
     */
    @Column("statistics_start_date")
    private LocalDate statisticsStartDate;
    
    /**
     * 最后重置日期
     */
    @Column("last_reset_date")
    private LocalDate lastResetDate;
    
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;
    
    // ==================== 关联关系 ====================
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数 - 创建默认统计信息
     */
    public UserStatistics(Long userId) {
        this.userId = userId;
        this.statisticsStartDate = LocalDate.now();
        this.lastActiveDate = LocalDate.now();
    }
    
    // ==================== 业务方法 ====================
    
    /**
     * 增加消息发送统计
     */
    public void incrementMessagesSent(long count) {
        this.totalMessagesSent += count;
        this.todayMessagesSent += count;
        this.weekMessagesSent += count;
        this.monthMessagesSent += count;
        updateActiveStreak();
    }
    
    /**
     * 增加消息接收统计
     */
    public void incrementMessagesReceived(long count) {
        this.totalMessagesReceived += count;
        this.todayMessagesReceived += count;
    }
    
    /**
     * 增加通话统计
     */
    public void incrementCallStats(long durationSeconds, boolean isVideo) {
        this.totalCalls++;
        this.totalCallDurationSeconds += durationSeconds;
        this.todayCalls++;
        this.todayCallDurationSeconds += durationSeconds;
        
        if (isVideo) {
            this.videoCallsCount++;
        } else {
            this.voiceCallsCount++;
        }
        
        updateActiveStreak();
    }
    
    /**
     * 增加数据使用统计
     */
    public void incrementDataUsage(long bytes, boolean isUpload) {
        this.totalDataUsedBytes += bytes;
        this.todayDataUsedBytes += bytes;
        this.weekDataUsedBytes += bytes;
        this.monthDataUsedBytes += bytes;
        
        if (isUpload) {
            this.uploadDataBytes += bytes;
        } else {
            this.downloadDataBytes += bytes;
        }
    }
    
    /**
     * 增加在线时长
     */
    public void incrementOnlineTime(long seconds) {
        this.totalOnlineDurationSeconds += seconds;
        this.todayOnlineDurationSeconds += seconds;
        updateAverageOnlineTime();
        updateActiveStreak();
    }
    
    /**
     * 增加媒体发送统计
     */
    public void incrementMediaSent(String mediaType) {
        switch (mediaType.toLowerCase()) {
            case "photo", "image" -> this.photosSent++;
            case "video" -> this.videosSent++;
            case "document", "file" -> this.documentsSent++;
            case "voice", "audio" -> this.voiceMessagesSent++;
            case "sticker" -> this.stickersSent++;
        }
    }
    
    /**
     * 更新活跃连击
     */
    private void updateActiveStreak() {
        LocalDate today = LocalDate.now();
        
        if (lastActiveDate == null || lastActiveDate.isBefore(today)) {
            if (lastActiveDate != null && lastActiveDate.equals(today.minusDays(1))) {
                // 连续活跃
                dailyActiveStreak++;
            } else if (lastActiveDate == null || lastActiveDate.isBefore(today.minusDays(1))) {
                // 中断后重新开始
                dailyActiveStreak = 1;
            }
            
            lastActiveDate = today;
            
            if (dailyActiveStreak > maxDailyActiveStreak) {
                maxDailyActiveStreak = dailyActiveStreak;
            }
        }
    }
    
    /**
     * 更新平均在线时长
     */
    private void updateAverageOnlineTime() {
        if (statisticsStartDate != null) {
            long daysSinceStart = java.time.temporal.ChronoUnit.DAYS.between(statisticsStartDate, LocalDate.now()) + 1;
            if (daysSinceStart > 0) {
                avgDailyOnlineDurationSeconds = totalOnlineDurationSeconds / daysSinceStart;
            }
        }
    }
    
    /**
     * 重置每日统计
     */
    public void resetDailyStats() {
        this.todayMessagesSent = 0L;
        this.todayMessagesReceived = 0L;
        this.todayCalls = 0L;
        this.todayCallDurationSeconds = 0L;
        this.todayDataUsedBytes = 0L;
        this.todayOnlineDurationSeconds = 0L;
    }
    
    /**
     * 重置每周统计
     */
    public void resetWeeklyStats() {
        this.weekMessagesSent = 0L;
        this.weekDataUsedBytes = 0L;
    }
    
    /**
     * 重置每月统计
     */
    public void resetMonthlyStats() {
        this.monthMessagesSent = 0L;
        this.monthDataUsedBytes = 0L;
    }
    
    /**
     * 获取数据使用量（MB）
     */
    public double getTotalDataUsedMB() {
        return totalDataUsedBytes / (1024.0 * 1024.0);
    }
    
    /**
     * 获取今日数据使用量（MB）
     */
    public double getTodayDataUsedMB() {
        return todayDataUsedBytes / (1024.0 * 1024.0);
    }
    
    /**
     * 获取平均每日消息数
     */
    public double getAverageDailyMessages() {
        if (statisticsStartDate != null) {
            long daysSinceStart = java.time.temporal.ChronoUnit.DAYS.between(statisticsStartDate, LocalDate.now()) + 1;
            if (daysSinceStart > 0) {
                return (double) totalMessagesSent / daysSinceStart;
            }
        }
        return 0.0;
    }

    /**
     * 设置用户等级
     */
    public void setLevel(int level) {
        this.level = level;
    }

    /**
     * 设置经验值
     */
    public void setExperiencePoints(long experiencePoints) {
        this.experiencePoints = experiencePoints;
    }

    /**
     * 增加经验值
     */
    public void addExperiencePoints(long points) {
        this.experiencePoints = (this.experiencePoints == null ? 0 : this.experiencePoints) + points;
        updateLevelProgress();
    }

    /**
     * 更新等级进度
     */
    private void updateLevelProgress() {
        if (this.experiencePoints != null && this.level != null) {
            long currentLevelExp = calculateLevelExperience(this.level);
            long nextLevelExp = calculateLevelExperience(this.level + 1);
            long expInCurrentLevel = this.experiencePoints - currentLevelExp;
            long expNeededForNextLevel = nextLevelExp - currentLevelExp;

            if (expNeededForNextLevel > 0) {
                this.levelProgressPercentage = BigDecimal.valueOf(expInCurrentLevel)
                    .divide(BigDecimal.valueOf(expNeededForNextLevel), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP);
            } else {
                this.levelProgressPercentage = BigDecimal.valueOf(100.0);
            }
        }
    }

    /**
     * 计算指定等级所需的经验值
     */
    private long calculateLevelExperience(int level) {
        // 简单的等级经验计算公式：level * 1000
        return level * 1000L;
    }
}
