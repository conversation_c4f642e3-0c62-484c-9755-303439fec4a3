package com.implatform.user.repository;

import com.implatform.user.entity.UserDevice;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 用户设备Repository
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Repository
public interface UserDeviceRepository extends ReactiveCrudRepository<UserDevice, Long> {

    // ==================== 基础查询 ====================

    /**
     * 根据用户ID查找设备
     */
    Flux<UserDevice> findByUserId(Long userId);

    /**
     * 根据用户ID和设备令牌查找设备
     */
    Mono<UserDevice> findByUserIdAndDeviceToken(Long userId, String deviceToken);

    /**
     * 根据设备令牌查找设备
     */
    Mono<UserDevice> findByDeviceToken(String deviceToken);

    /**
     * 检查设备是否存在
     */
    Mono<Boolean> existsByUserIdAndDeviceToken(Long userId, String deviceToken);

    /**
     * 检查设备令牌是否存在
     */
    Mono<Boolean> existsByDeviceToken(String deviceToken);
    
    // ==================== 状态查询 ====================
    
    /**
     * 根据用户ID和状态查找设备
     */
    Flux<UserDevice> findByUserIdAndDeviceStatus(Long userId, UserDevice.DeviceStatus deviceStatus);

    /**
     * 查找用户的活跃设备（按最后活跃时间排序）
     */
    Flux<UserDevice> findByUserIdAndDeviceStatusOrderByLastActiveTimeDesc(Long userId, UserDevice.DeviceStatus deviceStatus);

    /**
     * 查找用户的信任设备
     */
    Flux<UserDevice> findByUserIdAndIsTrustedTrueAndDeviceStatus(Long userId, UserDevice.DeviceStatus deviceStatus);

    /**
     * 查找用户的在线设备（注释掉，因为UserDevice没有isOnline字段）
     */
    // @Query("SELECT * FROM user_devices WHERE user_id = :userId AND device_status = :deviceStatus AND is_online = true")
    // Flux<UserDevice> findOnlineDevices(Long userId, UserDevice.DeviceStatus deviceStatus);

    // ==================== 时间范围查询 ====================

    /**
     * 查找指定时间后活跃的设备
     */
    Flux<UserDevice> findByUserIdAndLastActiveTimeAfter(Long userId, LocalDateTime after);

    /**
     * 查找指定时间前注册的设备
     */
    List<UserDevice> findByUserIdAndCreatedAtBefore(Long userId, LocalDateTime before);

    /**
     * 查找长时间未活跃的设备
     */
    @Query("SELECT d FROM UserDevice d WHERE d.userId = :userId AND d.lastActiveTime < :threshold AND d.deviceStatus = :deviceStatus")
    List<UserDevice> findInactiveDevices(@Param("userId") Long userId, @Param("threshold") LocalDateTime threshold, @Param("deviceStatus") UserDevice.DeviceStatus deviceStatus);
    
    // ==================== 设备类型查询 ====================
    
    /**
     * 根据设备类型查找设备
     */
    List<UserDevice> findByUserIdAndDeviceType(Long userId, UserDevice.DeviceType deviceType);
    
    /**
     * 统计用户各类型设备数量
     */
    @Query("SELECT d.deviceType, COUNT(d) FROM UserDevice d WHERE d.userId = :userId AND d.deviceStatus = :deviceStatus GROUP BY d.deviceType")
    List<Object[]> countDevicesByType(@Param("userId") Long userId, @Param("deviceStatus") UserDevice.DeviceStatus deviceStatus);
    
    // ==================== 统计查询 ====================
    
    /**
     * 统计用户设备总数
     */
    long countByUserId(Long userId);
    
    /**
     * 统计用户活跃设备数
     */
    long countByUserIdAndDeviceStatus(Long userId, UserDevice.DeviceStatus deviceStatus);

    /**
     * 统计指定状态的设备数量
     */
    long countByDeviceStatus(UserDevice.DeviceStatus deviceStatus);

    /**
     * 统计用户在线设备数（注释掉，因为UserDevice没有isOnline字段）
     */
    // @Query("SELECT COUNT(d) FROM UserDevice d WHERE d.userId = :userId AND d.isOnline = true AND d.deviceStatus = :deviceStatus")
    // long countOnlineDevices(@Param("userId") Long userId, @Param("deviceStatus") UserDevice.DeviceStatus deviceStatus);

    /**
     * 统计用户信任设备数
     */
    long countByUserIdAndIsTrustedTrueAndDeviceStatus(Long userId, UserDevice.DeviceStatus deviceStatus);
    
    // ==================== 安全查询 ====================
    
    /**
     * 查找可疑设备（多个用户使用同一设备令牌）
     */
    @Query("SELECT d.deviceToken FROM UserDevice d GROUP BY d.deviceToken HAVING COUNT(DISTINCT d.userId) > 1")
    List<String> findSuspiciousDeviceTokens();
    
    /**
     * 查找用户在指定IP地址的设备
     */
    List<UserDevice> findByUserIdAndLastIpAddress(Long userId, String ipAddress);
    
    /**
     * 查找指定地理位置附近的设备（注释掉，因为UserDevice没有地理位置字段）
     */
    // @Query("SELECT d FROM UserDevice d WHERE d.userId = :userId AND " +
    //        "ABS(d.lastLatitude - :latitude) < :range AND ABS(d.lastLongitude - :longitude) < :range")
    // List<UserDevice> findDevicesNearLocation(@Param("userId") Long userId,
    //                                        @Param("latitude") Double latitude,
    //                                        @Param("longitude") Double longitude,
    //                                        @Param("range") Double range);
    
    // ==================== 清理查询 ====================
    
    /**
     * 查找需要清理的非活跃设备
     */
    @Query("SELECT d FROM UserDevice d WHERE d.deviceStatus = :deviceStatus AND d.lastActiveTime < :threshold")
    List<UserDevice> findDevicesForCleanup(@Param("deviceStatus") UserDevice.DeviceStatus deviceStatus, @Param("threshold") LocalDateTime threshold);

    /**
     * 查找已注销的设备
     */
    List<UserDevice> findByDeviceStatusAndUpdatedAtBefore(UserDevice.DeviceStatus deviceStatus, LocalDateTime before);
    
    /**
     * 删除用户的所有设备
     */
    void deleteByUserId(Long userId);
    
    /**
     * 删除指定状态的设备
     */
    void deleteByDeviceStatus(UserDevice.DeviceStatus deviceStatus);
    
    // ==================== 排序查询 ====================
    
    /**
     * 获取用户设备列表（按最后活跃时间排序）
     */
    List<UserDevice> findByUserIdOrderByLastActiveTimeDesc(Long userId);

    /**
     * 获取用户设备列表（按创建时间排序）
     */
    List<UserDevice> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * 获取用户最旧的设备
     */
    Optional<UserDevice> findFirstByUserIdAndDeviceStatusOrderByCreatedAtAsc(Long userId, UserDevice.DeviceStatus deviceStatus);

    /**
     * 获取用户最新的设备
     */
    Optional<UserDevice> findFirstByUserIdAndDeviceStatusOrderByCreatedAtDesc(Long userId, UserDevice.DeviceStatus deviceStatus);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量更新设备状态
     */
    @Query("UPDATE UserDevice d SET d.deviceStatus = :newStatus WHERE d.userId = :userId AND d.deviceStatus = :oldStatus")
    int updateDeviceStatus(@Param("userId") Long userId, @Param("oldStatus") UserDevice.DeviceStatus oldStatus, @Param("newStatus") UserDevice.DeviceStatus newStatus);

    /**
     * 批量更新设备在线状态（注释掉，因为UserDevice没有isOnline字段）
     */
    // @Query("UPDATE UserDevice d SET d.isOnline = :isOnline WHERE d.userId = :userId")
    // int updateOnlineStatus(@Param("userId") Long userId, @Param("isOnline") boolean isOnline);
    
    /**
     * 批量更新最后活跃时间
     */
    @Query("UPDATE UserDevice d SET d.lastActiveTime = :lastActiveTime WHERE d.userId = :userId AND d.deviceToken IN :deviceTokens")
    int updateLastActiveTime(@Param("userId") Long userId, @Param("deviceTokens") List<String> deviceTokens, @Param("lastActiveTime") LocalDateTime lastActiveTime);
    
    // ==================== 自定义查询 ====================
    
    /**
     * 查找用户的主要设备（最常用的设备）
     */
    @Query("SELECT d FROM UserDevice d WHERE d.userId = :userId AND d.deviceStatus = :deviceStatus ORDER BY d.loginCount DESC, d.lastActiveTime DESC")
    List<UserDevice> findPrimaryDevices(@Param("userId") Long userId, @Param("deviceStatus") UserDevice.DeviceStatus deviceStatus);
    
    /**
     * 查找设备指纹匹配的设备（注释掉，因为UserDevice没有deviceFingerprint字段）
     */
    // List<UserDevice> findByDeviceFingerprint(String deviceFingerprint);
    
    /**
     * 查找推送令牌匹配的设备
     */
    Optional<UserDevice> findByPushToken(String pushToken);
    
    /**
     * 查找用户在指定时间段内活跃的设备
     */
    @Query("SELECT d FROM UserDevice d WHERE d.userId = :userId AND d.lastActiveTime BETWEEN :startTime AND :endTime")
    List<UserDevice> findActiveDevicesInPeriod(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
