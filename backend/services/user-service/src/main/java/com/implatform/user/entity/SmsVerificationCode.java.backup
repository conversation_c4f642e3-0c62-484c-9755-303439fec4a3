package com.implatform.user.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 短信验证码实体类 - IM平台短信验证码管理
 *
 * <p><strong>实体概述</strong>：
 * 本实体类用于存储和管理短信验证码，支持注册、登录、密码重置、手机号绑定等多种场景。
 * 提供完整的验证码生命周期管理，包括生成、验证、过期处理等功能。
 *
 * <p><strong>核心功能</strong>：
 * <ul>
 *   <li><strong>多场景支持</strong>：支持注册、登录、重置密码、绑定手机等场景</li>
 *   <li><strong>安全控制</strong>：验证码有效期控制、使用状态跟踪</li>
 *   <li><strong>防刷机制</strong>：IP地址记录、用户代理跟踪</li>
 *   <li><strong>审计日志</strong>：完整的验证码使用记录</li>
 * </ul>
 *
 * <p><strong>安全特性</strong>：
 * <ul>
 *   <li><strong>时效控制</strong>：验证码具有明确的过期时间</li>
 *   <li><strong>一次性使用</strong>：验证码使用后立即失效</li>
 *   <li><strong>IP限制</strong>：记录请求IP，支持防刷策略</li>
 *   <li><strong>类型隔离</strong>：不同类型的验证码相互独立</li>
 * </ul>
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "sms_verification_codes", indexes = {
    @Index(name = "idx_sms_verification_codes_phone_type", columnList = "phone, code_type"),
    @Index(name = "idx_sms_verification_codes__expires_at", columnList = "expires_at"),
    @Index(name = "idx_sms_verification_codes__user_id", columnList = "user_id"),
    @Index(name = "idx_sms_verification_codes__created_at", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class SmsVerificationCode {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    @Column(nullable = false, length = 20)
    private String phone;
    
    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    @Size(max = 10, message = "验证码长度不能超过10个字符")
    @Column(nullable = false, length = 10)
    private String code;
    
    /**
     * 验证码类型
     */
    @NotNull(message = "验证码类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "code_type", nullable = false, length = 20)
    private CodeType codeType;
    
    /**
     * 关联用户ID（可选）
     */
    @Column(name = "user_id")
    private Long userId;
    
    /**
     * IP地址
     */
    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    /**
     * 用户代理
     */
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;
    
    /**
     * 是否已使用
     */
    @Column(name = "is_used", nullable = false)
    private Boolean isUsed = false;
    
    /**
     * 使用时间
     */
    @Column(name = "used_at")
    private LocalDateTime usedAt;
    
    /**
     * 过期时间
     */
    @NotNull(message = "过期时间不能为空")
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 用户关联（多对一）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        REGISTER("注册验证"),
        LOGIN("登录验证"),
        RESET_PASSWORD("重置密码"),
        BIND_PHONE("绑定手机"),
        CHANGE_PHONE("更换手机"),
        SECURITY_VERIFY("安全验证");
        
        private final String description;
        
        CodeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查验证码是否有效
     */
    public boolean isValid() {
        return !isUsed && LocalDateTime.now().isBefore(expiresAt);
    }
    
    /**
     * 检查验证码是否过期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * 使用验证码
     */
    public void use() {
        this.isUsed = true;
        this.usedAt = LocalDateTime.now();
    }
    
    /**
     * 验证码是否可以使用
     */
    public boolean canUse() {
        return !isUsed && !isExpired();
    }
    
    /**
     * 获取剩余有效时间（秒）
     */
    public long getRemainingSeconds() {
        if (isExpired()) {
            return 0;
        }
        return java.time.Duration.between(LocalDateTime.now(), expiresAt).getSeconds();
    }
    
    /**
     * 获取验证码类型描述
     */
    public String getCodeTypeDescription() {
        return codeType.getDescription();
    }
    
    /**
     * 验证验证码
     */
    public boolean verify(String inputCode) {
        return canUse() && code.equals(inputCode);
    }
}
