package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.Instant;
import java.time.LocalTime;

/**
 * 用户通知设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Entity
@Table(name = "user_notification_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationSettings {
    
    @Id
    @Column(name = "user_id")
    private Long userId;
    
    // ==================== 基础通知设置 ====================
    
    /**
     * 消息通知开关
     */
    @Column(name = "message_notifications", nullable = false)
    private Boolean messageNotifications = true;
    
    /**
     * 群组通知开关
     */
    @Column(name = "group_notifications", nullable = false)
    private Boolean groupNotifications = true;
    
    /**
     * 频道通知开关
     */
    @Column(name = "channel_notifications", nullable = false)
    private Boolean channelNotifications = true;
    
    /**
     * 通话通知开关
     */
    @Column(name = "call_notifications", nullable = false)
    private Boolean callNotifications = true;
    
    // ==================== 通知详情设置 ====================
    
    /**
     * 显示消息预览
     */
    @Column(name = "show_preview", nullable = false)
    private Boolean showPreview = true;
    
    /**
     * 显示发送者姓名
     */
    @Column(name = "show_sender", nullable = false)
    private Boolean showSender = true;
    
    /**
     * 通知声音
     */
    @Column(name = "notification_sound", length = 100)
    private String notificationSound = "default";
    
    /**
     * 自定义通知声音URL
     */
    @Column(name = "custom_sound_url", length = 500)
    private String customSoundUrl;
    
    // ==================== 免打扰设置 ====================
    
    /**
     * 免打扰模式开关
     */
    @Column(name = "do_not_disturb", nullable = false)
    private Boolean doNotDisturb = false;
    
    /**
     * 免打扰开始时间
     */
    @Column(name = "quiet_hours_start")
    private LocalTime quietHoursStart;
    
    /**
     * 免打扰结束时间
     */
    @Column(name = "quiet_hours_end")
    private LocalTime quietHoursEnd;
    
    /**
     * 周末免打扰
     */
    @Column(name = "weekend_quiet", nullable = false)
    private Boolean weekendQuiet = false;
    
    // ==================== 振动和LED设置 ====================
    
    /**
     * 振动开关
     */
    @Column(name = "vibration_enabled", nullable = false)
    private Boolean vibrationEnabled = true;
    
    /**
     * 振动模式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "vibration_pattern", length = 20)
    private VibrationPattern vibrationPattern = VibrationPattern.DEFAULT;
    
    /**
     * LED灯颜色
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "led_color", length = 20)
    private LedColor ledColor = LedColor.BLUE;
    
    /**
     * LED灯开关
     */
    @Column(name = "led_enabled", nullable = false)
    private Boolean ledEnabled = true;
    
    // ==================== 高级设置 ====================
    
    /**
     * 重复通知间隔（分钟）
     */
    @Column(name = "repeat_interval")
    private Integer repeatInterval = 0; // 0表示不重复
    
    /**
     * 通知优先级
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "notification_priority", length = 20)
    private NotificationPriority notificationPriority = NotificationPriority.NORMAL;
    
    /**
     * 锁屏显示通知
     */
    @Column(name = "show_on_lock_screen", nullable = false)
    private Boolean showOnLockScreen = true;
    
    /**
     * 通知分组
     */
    @Column(name = "group_notifications_enabled", nullable = false)
    private Boolean groupNotificationsEnabled = true;
    
    // ==================== 时间戳 ====================
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;
    
    // ==================== 关联关系 ====================
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    // ==================== 枚举定义 ====================
    
    /**
     * 振动模式枚举
     */
    public enum VibrationPattern {
        DEFAULT("默认"),
        SHORT("短振动"),
        LONG("长振动"),
        DOUBLE("双振动"),
        CUSTOM("自定义");
        
        private final String description;
        
        VibrationPattern(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * LED颜色枚举
     */
    public enum LedColor {
        RED("红色"),
        GREEN("绿色"),
        BLUE("蓝色"),
        YELLOW("黄色"),
        PURPLE("紫色"),
        WHITE("白色"),
        OFF("关闭");
        
        private final String description;
        
        LedColor(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 通知优先级枚举
     */
    public enum NotificationPriority {
        LOW("低"),
        NORMAL("普通"),
        HIGH("高"),
        URGENT("紧急");
        
        private final String description;
        
        NotificationPriority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    // ==================== 构造函数 ====================
    
    /**
     * 构造函数 - 创建默认通知设置
     */
    public NotificationSettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    // ==================== 业务方法 ====================
    
    /**
     * 检查当前时间是否在免打扰时段
     */
    public boolean isInQuietHours() {
        if (!doNotDisturb || quietHoursStart == null || quietHoursEnd == null) {
            return false;
        }
        
        LocalTime now = LocalTime.now();
        
        // 处理跨天的情况
        if (quietHoursStart.isAfter(quietHoursEnd)) {
            return now.isAfter(quietHoursStart) || now.isBefore(quietHoursEnd);
        } else {
            return now.isAfter(quietHoursStart) && now.isBefore(quietHoursEnd);
        }
    }
    
    /**
     * 检查是否应该显示通知
     */
    public boolean shouldShowNotification(String notificationType) {
        if (isInQuietHours()) {
            return false;
        }
        
        return switch (notificationType.toLowerCase()) {
            case "message" -> messageNotifications;
            case "group" -> groupNotifications;
            case "channel" -> channelNotifications;
            case "call" -> callNotifications;
            default -> true;
        };
    }
    
    /**
     * 获取有效的通知声音
     */
    public String getEffectiveNotificationSound() {
        if (customSoundUrl != null && !customSoundUrl.trim().isEmpty()) {
            return customSoundUrl;
        }
        return notificationSound;
    }
}
