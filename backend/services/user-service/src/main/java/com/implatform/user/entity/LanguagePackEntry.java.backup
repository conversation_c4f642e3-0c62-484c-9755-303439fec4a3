package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;

/**
 * 语言包翻译条目实体类
 * 
 * <p>该实体类用于存储具体的翻译键值对，每个条目属于特定的语言包版本。
 * 支持分类管理、上下文说明、激活状态控制等功能。</p>
 * 
 * <p><strong>主要功能</strong>：
 * <ul>
 *   <li>翻译键值对存储</li>
 *   <li>分类管理支持</li>
 *   <li>上下文说明</li>
 *   <li>激活状态控制</li>
 *   <li>版本关联管理</li>
 * </ul>
 * 
 * <p><strong>数据库映射</strong>：
 * 映射到 language_pack_entries 表，包含版本ID和翻译键的复合唯一索引。
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "language_pack_entries", 
       indexes = {
           @Index(name = "idx_language_pack_entries_version_id", columnList = "language_pack_version_id"),
           @Index(name = "idx_language_pack_entries_translation_key", columnList = "translation_key"),
           @Index(name = "idx_language_pack_entries_category", columnList = "category"),
           @Index(name = "idx_language_pack_entries_is_active", columnList = "is_active")
       },
       uniqueConstraints = {
           @UniqueConstraint(name = "unique_entry", columnNames = {"language_pack_version_id", "translation_key"})
       })
@EntityListeners(AuditingEntityListener.class)
@EqualsAndHashCode(callSuper = false)
public class LanguagePackEntry {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的语言包版本
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "language_pack_version_id", nullable = false,
                foreignKey = @ForeignKey(name = "fk_language_pack_entries_version_id"))
    private LanguagePackVersion languagePackVersion;
    
    /**
     * 翻译键
     * 格式：common.hello、auth.login、chat.send_message 等
     */
    @NotBlank(message = "翻译键不能为空")
    @Size(max = 255, message = "翻译键长度不能超过255个字符")
    @Column(name = "translation_key", nullable = false, length = 255)
    private String translationKey;
    
    /**
     * 翻译值
     */
    @NotBlank(message = "翻译值不能为空")
    @Column(name = "translation_value", nullable = false, columnDefinition = "TEXT")
    private String translationValue;
    
    /**
     * 分类
     * 如：common、auth、chat、settings 等
     */
    @Size(max = 50, message = "分类长度不能超过50个字符")
    @Column(name = "category", length = 50)
    private String category;
    
    /**
     * 描述
     * 用于说明该翻译条目的用途
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    /**
     * 上下文说明
     * 用于提供翻译时的上下文信息
     */
    @Column(name = "context", columnDefinition = "TEXT")
    private String context;
    
    /**
     * 是否激活
     * 只有激活的条目才会被使用
     */
    @Builder.Default
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    
    /**
     * 创建时间
     */
    @CreatedDate
    @Column(nullable = false, updatable = false, name = "created_at")
    private Instant createdAt;
    
    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(nullable = false, name = "updated_at")
    private Instant updatedAt;
    
    /**
     * 激活条目
     */
    public void activate() {
        this.isActive = true;
    }
    
    /**
     * 停用条目
     */
    public void deactivate() {
        this.isActive = false;
    }
    
    /**
     * 检查是否激活
     * 
     * @return true如果条目处于激活状态
     */
    public boolean isActivated() {
        return Boolean.TRUE.equals(isActive);
    }
    
    /**
     * 获取分类显示名称
     * 如果分类为空，返回"未分类"
     * 
     * @return 分类显示名称
     */
    public String getCategoryDisplayName() {
        return category != null && !category.trim().isEmpty() ? category : "未分类";
    }
    
    /**
     * 获取翻译键的层级结构
     * 例如：common.hello -> ["common", "hello"]
     * 
     * @return 翻译键的层级数组
     */
    public String[] getKeyHierarchy() {
        return translationKey != null ? translationKey.split("\\.") : new String[0];
    }
    
    /**
     * 获取翻译键的根分类
     * 例如：common.hello -> "common"
     * 
     * @return 翻译键的根分类
     */
    public String getRootCategory() {
        String[] hierarchy = getKeyHierarchy();
        return hierarchy.length > 0 ? hierarchy[0] : "";
    }
    
    /**
     * 检查翻译值是否包含参数占位符
     * 检查是否包含 {参数名} 格式的占位符
     * 
     * @return true如果包含参数占位符
     */
    public boolean hasParameters() {
        return translationValue != null && translationValue.contains("{") && translationValue.contains("}");
    }
    
    /**
     * 检查翻译值是否为空或只包含空白字符
     * 
     * @return true如果翻译值为空
     */
    public boolean isTranslationEmpty() {
        return translationValue == null || translationValue.trim().isEmpty();
    }
    
    /**
     * 获取翻译值的字符长度
     * 
     * @return 翻译值的字符长度
     */
    public int getTranslationLength() {
        return translationValue != null ? translationValue.length() : 0;
    }
    
    /**
     * 更新翻译值
     * 
     * @param newValue 新的翻译值
     */
    public void updateTranslation(String newValue) {
        if (newValue != null && !newValue.trim().isEmpty()) {
            this.translationValue = newValue.trim();
        }
    }
    
    /**
     * 设置分类（自动从翻译键推断）
     * 如果分类为空，则从翻译键的第一部分推断
     */
    public void inferCategoryFromKey() {
        if ((category == null || category.trim().isEmpty()) && translationKey != null) {
            String rootCategory = getRootCategory();
            if (!rootCategory.isEmpty()) {
                this.category = rootCategory;
            }
        }
    }
    
    /**
     * 创建条目的副本（用于版本复制）
     * 
     * @param targetVersion 目标版本
     * @return 新的条目副本
     */
    public LanguagePackEntry createCopy(LanguagePackVersion targetVersion) {
        return LanguagePackEntry.builder()
            .languagePackVersion(targetVersion)
            .translationKey(this.translationKey)
            .translationValue(this.translationValue)
            .category(this.category)
            .description(this.description)
            .context(this.context)
            .isActive(this.isActive)
            .build();
    }
    
    /**
     * 获取条目的显示信息
     * 格式：翻译键 = 翻译值 (分类)
     * 
     * @return 条目的显示信息
     */
    public String getDisplayInfo() {
        String categoryInfo = category != null ? String.format(" (%s)", category) : "";
        String valuePreview = translationValue != null && translationValue.length() > 50 
            ? translationValue.substring(0, 47) + "..." 
            : translationValue;
        return String.format("%s = %s%s", translationKey, valuePreview, categoryInfo);
    }
}
