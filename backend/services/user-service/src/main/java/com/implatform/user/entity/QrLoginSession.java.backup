package com.implatform.user.entity;

import com.implatform.user.enums.QrLoginStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.TimeToLive;

import java.time.Instant;

/**
 * 二维码登录会话实体
 * 存储在Redis中，用于管理扫码登录的临时状态
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@RedisHash("qr_login_session")
public class QrLoginSession {

    /**
     * 二维码token，作为唯一标识
     */
    @Id
    private String qrToken;

    /**
     * 扫码登录状态
     */
    private QrLoginStatus status;

    /**
     * 扫码用户ID（扫码后设置）
     */
    private Long userId;

    /**
     * 扫码用户名（扫码后设置）
     */
    private String username;

    /**
     * 扫码设备信息
     */
    private String deviceInfo;

    /**
     * 扫码设备IP地址
     */
    private String deviceIp;

    /**
     * 目标登录设备信息（Web端）
     */
    private String targetDeviceInfo;

    /**
     * 目标登录设备IP地址（Web端）
     */
    private String targetDeviceIp;

    /**
     * WebSocket连接ID（用于实时通知）
     */
    private String websocketConnectionId;

    /**
     * 创建时间
     */
    private Instant createdAt;

    /**
     * 扫码时间
     */
    private Instant scannedAt;

    /**
     * 确认时间
     */
    private Instant confirmedAt;

    /**
     * 过期时间
     */
    private Instant expiresAt;

    /**
     * 生存时间（秒）- Redis TTL
     */
    @TimeToLive
    private Long ttl;

    /**
     * 扩展属性（JSON格式）
     */
    private String extraData;

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return Instant.now().isAfter(expiresAt);
    }

    /**
     * 检查是否可以扫码
     */
    public boolean canScan() {
        return status == QrLoginStatus.PENDING && !isExpired();
    }

    /**
     * 检查是否可以确认
     */
    public boolean canConfirm() {
        return status == QrLoginStatus.SCANNED && !isExpired();
    }

    /**
     * 标记为已扫码
     */
    public void markAsScanned(Long userId, String username, String deviceInfo, String deviceIp) {
        this.status = QrLoginStatus.SCANNED;
        this.userId = userId;
        this.username = username;
        this.deviceInfo = deviceInfo;
        this.deviceIp = deviceIp;
        this.scannedAt = Instant.now();
    }

    /**
     * 标记为已确认
     */
    public void markAsConfirmed() {
        this.status = QrLoginStatus.CONFIRMED;
        this.confirmedAt = Instant.now();
    }

    /**
     * 标记为已取消
     */
    public void markAsCancelled() {
        this.status = QrLoginStatus.CANCELLED;
    }

    /**
     * 标记为已过期
     */
    public void markAsExpired() {
        this.status = QrLoginStatus.EXPIRED;
    }

    /**
     * 创建新的二维码登录会话
     */
    public static QrLoginSession create(String qrToken, String targetDeviceInfo, String targetDeviceIp, 
                                       String websocketConnectionId, int ttlSeconds) {
        Instant now = Instant.now();
        return QrLoginSession.builder()
                .qrToken(qrToken)
                .status(QrLoginStatus.PENDING)
                .targetDeviceInfo(targetDeviceInfo)
                .targetDeviceIp(targetDeviceIp)
                .websocketConnectionId(websocketConnectionId)
                .createdAt(now)
                .expiresAt(now.plusSeconds(ttlSeconds))
                .ttl((long) ttlSeconds)
                .build();
    }
}
