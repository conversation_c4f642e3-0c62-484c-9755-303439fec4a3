package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 隐私设置实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("user_privacy_settings")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PrivacySettings {
    
    @Id
    @Column("user_id")
    private Long userId;
    
        @Column("phone_visibility")
    private PrivacyLevel phoneVisibility = PrivacyLevel.NOBODY;
    
        @Column("last_seen_visibility")
    private PrivacyLevel lastSeenVisibility = PrivacyLevel.EVERYBODY;
    
        @Column("profile_photo_visibility")
    private PrivacyLevel profilePhotoVisibility = PrivacyLevel.EVERYBODY;
    
        @Column("message_permission")
    private PrivacyLevel messagePermission = PrivacyLevel.EVERYBODY;
    
        @Column("group_invite_permission")
    private PrivacyLevel groupInvitePermission = PrivacyLevel.CONTACTS;
    
        @Column("phone_discovery")
    private PrivacyLevel phoneDiscovery = PrivacyLevel.CONTACTS;
    
        @Column("username_discovery")
    private PrivacyLevel usernameDiscovery = PrivacyLevel.EVERYBODY;
    
    @Column("allow_forwarding")
    private Boolean allowForwarding = true;
    
    @Column("show_read_receipts")
    private Boolean showReadReceipts = true;
    
    @Column("show_online_status")
    private Boolean showOnlineStatus = true;
    
    @Column("allow_voice_calls")
    private Boolean allowVoiceCalls = true;
    
    @Column("allow_video_calls")
    private Boolean allowVideoCalls = true;
    
    @CreatedDate
    @Column("created_at")
    private LocalDateTime createdAt;
    
    @LastModifiedDate
    @Column("updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 与用户实体的关联
     */
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id")
    @MapsId
    private User user;
    
    /**
     * 隐私级别枚举
     */
    public enum PrivacyLevel {
        EVERYBODY("所有人"),
        CONTACTS("联系人"),
        NOBODY("没有人");
        
        private final String description;
        
        PrivacyLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数 - 创建默认隐私设置
     */
    public PrivacySettings(Long userId) {
        this.userId = userId;
        // 使用默认值
    }
    
    /**
     * 检查是否允许某用户查看手机号
     */
    public boolean canViewPhone(boolean isContact) {
        return switch (phoneVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户查看最后在线时间
     */
    public boolean canViewLastSeen(boolean isContact) {
        return switch (lastSeenVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户查看头像
     */
    public boolean canViewProfilePhoto(boolean isContact) {
        return switch (profilePhotoVisibility) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户发送消息
     */
    public boolean canSendMessage(boolean isContact) {
        return switch (messagePermission) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
    
    /**
     * 检查是否允许某用户邀请加入群组
     */
    public boolean canInviteToGroup(boolean isContact) {
        return switch (groupInvitePermission) {
            case EVERYBODY -> true;
            case CONTACTS -> isContact;
            case NOBODY -> false;
        };
    }
}
