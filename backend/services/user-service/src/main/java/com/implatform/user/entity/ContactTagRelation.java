package com.implatform.user.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.time.Instant;

/**
 * 联系人标签关联实体
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@Table("contact_tag_relations"),
       indexes = {
    @Index(name = "idx_contact_tag_rel_user", columnList = "user_id"),
    @Index(name = "idx_contact_tag_rel_contact", columnList = "contact_id"),
    @Index(name = "idx_contact_tag_rel_tag", columnList = "tag_id"),
    @Index(name = "idx_contact_tag_rel_created", columnList = "created_at")
})
@EqualsAndHashCode(callSuper = false)
public class ContactTagRelation {

    @Id
        private Long id;

    /**
     * 用户ID（标签所有者）
     */
    @Column("user_id")
    private Long userId;

    /**
     * 联系人ID（被标记的用户）
     */
    @Column("contact_id")
    private Long contactId;

    /**
     * 标签ID
     */
    @Column("tag_id")
    private Long tagId;

    /**
     * 关联类型
     */
        @Column("relation_type")
    private RelationType relationType = RelationType.MANUAL;

    /**
     * 关联来源
     */
    @Column("source")
    private String source = "USER_MANUAL";

    /**
     * 关联备注
     */
    @Column("remark")
    private String remark;

    /**
     * 是否启用
     */
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 权重（用于排序）
     */
    @Column("weight")
    private Integer weight = 0;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 关联类型枚举
     */
    public enum RelationType {
        MANUAL("手动添加"),
        AUTO("自动添加"),
        IMPORT("导入添加"),
        SMART("智能推荐");

        private final String description;

        RelationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 默认构造函数
     */
    public ContactTagRelation() {}

    /**
     * 构造函数
     */
    public ContactTagRelation(Long userId, Long contactId, Long tagId) {
        this.userId = userId;
        this.contactId = contactId;
        this.tagId = tagId;
    }

    /**
     * 构造函数（带关联类型）
     */
    public ContactTagRelation(Long userId, Long contactId, Long tagId, RelationType relationType) {
        this.userId = userId;
        this.contactId = contactId;
        this.tagId = tagId;
        this.relationType = relationType;
    }

    /**
     * 检查关联是否有效
     */
    public boolean isValid() {
        return userId != null && 
               contactId != null && 
               tagId != null && 
               !userId.equals(contactId) && // 不能给自己打标签
               isActive;
    }

    /**
     * 激活关联
     */
    public void activate() {
        this.isActive = true;
    }

    /**
     * 停用关联
     */
    public void deactivate() {
        this.isActive = false;
    }

    /**
     * 切换激活状态
     */
    public void toggleActive() {
        this.isActive = !this.isActive;
    }

    /**
     * 设置权重
     */
    public void setWeight(Integer weight) {
        this.weight = weight != null && weight >= 0 ? weight : 0;
    }

    /**
     * 增加权重
     */
    public void increaseWeight(int increment) {
        this.weight += increment;
    }

    /**
     * 减少权重
     */
    public void decreaseWeight(int decrement) {
        this.weight = Math.max(0, this.weight - decrement);
    }

    /**
     * 检查是否为手动添加
     */
    public boolean isManual() {
        return relationType == RelationType.MANUAL;
    }

    /**
     * 检查是否为自动添加
     */
    public boolean isAuto() {
        return relationType == RelationType.AUTO;
    }

    /**
     * 检查是否为智能推荐
     */
    public boolean isSmart() {
        return relationType == RelationType.SMART;
    }

    /**
     * 检查是否为导入添加
     */
    public boolean isImport() {
        return relationType == RelationType.IMPORT;
    }

    /**
     * 获取关联类型描述
     */
    public String getRelationTypeDescription() {
        return relationType.getDescription();
    }

    /**
     * 更新备注
     */
    public void updateRemark(String remark) {
        this.remark = remark != null ? remark.trim() : null;
    }

    /**
     * 更新来源
     */
    public void updateSource(String source) {
        this.source = source != null ? source.trim() : "USER_MANUAL";
    }

    /**
     * 获取关联摘要
     */
    public String getSummary() {
        return String.format("用户[%d]给联系人[%d]添加标签[%d] 类型[%s] 状态[%s]", 
                userId, contactId, tagId, 
                relationType.getDescription(), 
                isActive ? "激活" : "停用");
    }

    /**
     * 检查是否为最近添加（7天内）
     */
    public boolean isRecentlyAdded() {
        return createdAt != null && 
               createdAt.isAfter(Instant.now().minusSeconds(7 * 24 * 3600));
    }

    /**
     * 检查是否为长期关联（30天以上）
     */
    public boolean isLongTerm() {
        return createdAt != null && 
               createdAt.isBefore(Instant.now().minusSeconds(30 * 24 * 3600));
    }

    /**
     * 获取关联天数
     */
    public long getDaysFromCreation() {
        if (createdAt == null) {
            return 0;
        }
        return java.time.Duration.between(createdAt, Instant.now()).toDays();
    }

    /**
     * 检查是否需要确认（智能推荐的关联）
     */
    public boolean needsConfirmation() {
        return relationType == RelationType.SMART && isActive;
    }

    /**
     * 确认智能推荐
     */
    public void confirmSmartRecommendation() {
        if (relationType == RelationType.SMART) {
            this.relationType = RelationType.MANUAL;
            this.source = "SMART_CONFIRMED";
        }
    }

    /**
     * 拒绝智能推荐
     */
    public void rejectSmartRecommendation() {
        if (relationType == RelationType.SMART) {
            this.isActive = false;
            this.source = "SMART_REJECTED";
        }
    }

    /**
     * 复制关联到新标签
     */
    public ContactTagRelation copyToNewTag(Long newTagId) {
        ContactTagRelation copy = new ContactTagRelation();
        copy.setUserId(this.userId);
        copy.setContactId(this.contactId);
        copy.setTagId(newTagId);
        copy.setRelationType(RelationType.MANUAL);
        copy.setSource("COPIED_FROM_TAG_" + this.tagId);
        copy.setRemark(this.remark);
        copy.setIsActive(true);
        copy.setWeight(this.weight);
        return copy;
    }

    /**
     * 检查是否可以删除
     */
    public boolean canDelete() {
        // 手动添加的关联可以删除，系统自动的可能需要特殊权限
        return relationType == RelationType.MANUAL || relationType == RelationType.IMPORT;
    }

    /**
     * 检查是否可以编辑
     */
    public boolean canEdit() {
        return isActive && (relationType == RelationType.MANUAL || relationType == RelationType.IMPORT);
    }

    /**
     * 获取关联强度（基于权重和时间）
     */
    public double getRelationStrength() {
        double strength = weight * 0.7; // 权重占70%
        
        // 时间因素占30%
        long days = getDaysFromCreation();
        if (days > 0) {
            strength += Math.min(days * 0.1, 30) * 0.3; // 最多30分
        }
        
        // 关联类型调整
        switch (relationType) {
            case MANUAL:
                strength *= 1.2; // 手动添加权重更高
                break;
            case SMART:
                strength *= 0.8; // 智能推荐权重较低
                break;
            case AUTO:
                strength *= 0.9; // 自动添加权重中等
                break;
            case IMPORT:
                strength *= 1.0; // 导入添加权重正常
                break;
        }
        
        return Math.min(strength, 100.0); // 最大100分
    }

    /**
     * 检查关联是否过期（针对临时标签）
     */
    public boolean isExpired(int expireDays) {
        return createdAt != null && 
               createdAt.isBefore(Instant.now().minusSeconds(expireDays * 24L * 3600));
    }
}
