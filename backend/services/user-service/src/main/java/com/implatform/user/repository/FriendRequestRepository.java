package com.implatform.user.repository;

import java.time.LocalDateTime;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import com.implatform.user.entity.FriendRequest;

/**
 * 好友请求Repository
 */
@Repository
public interface FriendRequestRepository extends ReactiveCrudRepository<FriendRequest, Long> {

    /**
     * 查找指定用户间的请求
     */
    Mono<FriendRequest> findByFromUserIdAndToUserIdAndStatus(Long fromUserId, Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 查找用户发送的请求
     */
    @Query("SELECT * FROM friend_requests WHERE from_user_id = :fromUserId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<FriendRequest> findByFromUserIdOrderByCreatedAtDesc(Long fromUserId, int limit, long offset);

    /**
     * 查找用户接收的请求
     */
    @Query("SELECT * FROM friend_requests WHERE to_user_id = :toUserId ORDER BY created_at DESC LIMIT :limit OFFSET :offset")
    Flux<FriendRequest> findByToUserIdOrderByCreatedAtDesc(Long toUserId, int limit, long offset);

    /**
     * 查找用户接收的待处理请求
     */
    Flux<FriendRequest> findByToUserIdAndStatus(Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 统计待处理请求数量
     */
    Mono<Long> countByToUserIdAndStatus(Long toUserId, FriendRequest.RequestStatus status);

    /**
     * 查找过期的请求
     */
    @Query("SELECT * FROM friend_requests WHERE status = 'PENDING' AND created_at < :expireTime")
    Flux<FriendRequest> findExpiredRequests(LocalDateTime expireTime);

    /**
     * 检查是否存在待处理的请求
     */
    Mono<Boolean> existsByFromUserIdAndToUserIdAndStatus(Long fromUserId, Long toUserId, FriendRequest.RequestStatus status);
}