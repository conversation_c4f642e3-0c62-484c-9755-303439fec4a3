package com.implatform.user.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 验证码实体
 * 管理短信验证码、邮箱验证码等各种验证码的生命周期
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Table("verification_codes"),
    @Index(name = "idx_verification_codes_code", columnList = "code"),
    @Index(name = "idx_verification_codes_type", columnList = "code_type"),
    @Index(name = "idx_verification_codes_status", columnList = "status"),
    @Index(name = "idx_verification_codes_expires_at", columnList = "expires_at"),
    @Index(name = "idx_verification_codes_created_at", columnList = "created_at")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationCode {

    @Id
        private Long id;

    /**
     * 目标（手机号、邮箱等）
     */
    @Column("target")
    @NotBlank(message = "Target cannot be blank")
    @Size(max = 100, message = "Target cannot exceed 100 characters")
    private String target;

    /**
     * 验证码
     */
    @Column("code")
    @NotBlank(message = "Code cannot be blank")
    @Size(max = 10, message = "Code cannot exceed 10 characters")
    private String code;

    /**
     * 验证码类型
     */
        @Column("code_type")
    @NotNull(message = "Code type cannot be null")
    private CodeType codeType;

    /**
     * 验证码用途
     */
        @Column("purpose")
    @NotNull(message = "Purpose cannot be null")
    private Purpose purpose;

    /**
     * 验证码状态
     */
        @Column("status")
    @NotNull(message = "Status cannot be null")
    private CodeStatus status;

    /**
     * 过期时间
     */
    @Column("expires_at")
    @NotNull(message = "Expires at cannot be null")
    private Instant expiresAt;

    /**
     * 使用时间
     */
    @Column("used_at")
    private Instant usedAt;

    /**
     * 尝试次数
     */
    @Column("attempt_count")
    @Builder.Default
    private Integer attemptCount = 0;

    /**
     * 最大尝试次数
     */
    @Column("max_attempts")
    @Builder.Default
    private Integer maxAttempts = 3;

    /**
     * 客户端IP地址
     */
    @Column("client_ip")
    @Size(max = 45, message = "Client IP cannot exceed 45 characters")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @Column("user_agent")
    @Size(max = 500, message = "User agent cannot exceed 500 characters")
    private String userAgent;

    /**
     * 设备ID
     */
    @Column("device_id")
    @Size(max = 64, message = "Device ID cannot exceed 64 characters")
    private String deviceId;

    /**
     * 关联的用户ID（可选）
     */
    @Column("user_id")
    private Long userId;

    /**
     * 发送渠道
     */
    @Column("send_channel")
    @Size(max = 50, message = "Send channel cannot exceed 50 characters")
    private String sendChannel;

    /**
     * 发送状态
     */
        @Column("send_status")
    private SendStatus sendStatus;

    /**
     * 发送错误信息
     */
    @Column("send_error")
    @Size(max = 200, message = "Send error cannot exceed 200 characters")
    private String sendError;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column("created_at")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column("updated_at")
    private Instant updatedAt;

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        /**
         * 短信验证码
         */
        SMS,
        
        /**
         * 邮箱验证码
         */
        EMAIL,
        
        /**
         * 语音验证码
         */
        VOICE,
        
        /**
         * 图形验证码
         */
        CAPTCHA,
        
        /**
         * TOTP验证码
         */
        TOTP
    }

    /**
     * 验证码用途枚举
     */
    public enum Purpose {
        /**
         * 用户注册
         */
        REGISTRATION,
        
        /**
         * 登录验证
         */
        LOGIN,
        
        /**
         * 密码重置
         */
        PASSWORD_RESET,
        
        /**
         * 手机号绑定
         */
        PHONE_BINDING,
        
        /**
         * 邮箱绑定
         */
        EMAIL_BINDING,
        
        /**
         * 账户验证
         */
        ACCOUNT_VERIFICATION,
        
        /**
         * 敏感操作确认
         */
        SENSITIVE_OPERATION,
        
        /**
         * 二次验证
         */
        TWO_FACTOR_AUTH
    }

    /**
     * 验证码状态枚举
     */
    public enum CodeStatus {
        /**
         * 待使用
         */
        PENDING,
        
        /**
         * 已使用
         */
        USED,
        
        /**
         * 已过期
         */
        EXPIRED,
        
        /**
         * 已失效（超过最大尝试次数）
         */
        INVALID,
        
        /**
         * 已取消
         */
        CANCELLED
    }

    /**
     * 发送状态枚举
     */
    public enum SendStatus {
        /**
         * 发送成功
         */
        SUCCESS,
        
        /**
         * 发送失败
         */
        FAILED,
        
        /**
         * 发送中
         */
        SENDING,
        
        /**
         * 待发送
         */
        PENDING
    }

    /**
     * 检查验证码是否有效
     */
    public boolean isValid() {
        return status == CodeStatus.PENDING && 
               expiresAt.isAfter(Instant.now()) &&
               attemptCount < maxAttempts;
    }

    /**
     * 检查验证码是否过期
     */
    public boolean isExpired() {
        return expiresAt.isBefore(Instant.now());
    }

    /**
     * 增加尝试次数
     */
    public void incrementAttempt() {
        this.attemptCount++;
        if (this.attemptCount >= this.maxAttempts) {
            this.status = CodeStatus.INVALID;
        }
        this.updatedAt = Instant.now();
    }

    /**
     * 标记为已使用
     */
    public void markAsUsed() {
        this.status = CodeStatus.USED;
        this.usedAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    /**
     * 标记为已过期
     */
    public void markAsExpired() {
        this.status = CodeStatus.EXPIRED;
        this.updatedAt = Instant.now();
    }

    /**
     * 标记为已取消
     */
    public void markAsCancelled() {
        this.status = CodeStatus.CANCELLED;
        this.updatedAt = Instant.now();
    }

    /**
     * 验证码是否匹配
     */
    public boolean matches(String inputCode) {
        return this.code.equals(inputCode);
    }
}
