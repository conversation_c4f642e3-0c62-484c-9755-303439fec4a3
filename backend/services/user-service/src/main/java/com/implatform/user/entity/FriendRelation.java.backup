package com.implatform.user.entity;

import java.time.Instant;

import org.springframework.data.annotation.*;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.implatform.common.core.enums.UserErrorCode;

/**
 * 好友关系实体类 - IM平台核心社交关系管理实体
 *
 * <p><strong>业务用途</strong>：
 * 管理IM平台中用户之间的好友关系，提供类似Telegram的联系人系统功能。支持双向好友关系管理、
 * 联系人分组标签、隐私权限控制、互动统计分析等完整的社交关系功能。
 *
 * <p><strong>核心功能特性</strong>：
 * <ul>
 *   <li><strong>双向关系管理</strong>：支持好友添加、删除、拉黑等状态管理</li>
 *   <li><strong>联系人分组</strong>：支持自定义分组和标签系统</li>
 *   <li><strong>隐私权限控制</strong>：细粒度的隐私设置和权限管理</li>
 *   <li><strong>互动统计</strong>：消息、通话等互动数据统计和亲密度计算</li>
 *   <li><strong>同步状态管理</strong>：支持多设备联系人同步</li>
 *   <li><strong>丰富备注信息</strong>：支持备注、笔记、重要日期等个性化信息</li>
 * </ul>
 *
 * <p><strong>使用场景</strong>：
 * <ul>
 *   <li>用户添加好友并建立社交关系</li>
 *   <li>联系人列表展示和管理（分组、排序、搜索）</li>
 *   <li>隐私设置管理（免打扰、屏蔽动态、通话权限等）</li>
 *   <li>社交数据分析（亲密度评分、活跃联系人识别）</li>
 *   <li>通讯录同步和多设备数据一致性</li>
 *   <li>个性化联系人信息管理（备注、标签、重要日期）</li>
 * </ul>
 *
 * <p><strong>业务上下文</strong>：
 * 作为IM平台的核心社交实体，FriendRelation连接了用户管理、消息系统、通话功能、隐私控制等
 * 多个业务域。通过丰富的关系数据，支持个性化的社交体验和智能化的功能推荐。
 *
 * <p><strong>关键关系</strong>：
 * <ul>
 *   <li>多对一关系：FriendRelation → User（用户）</li>
 *   <li>多对一关系：FriendRelation → User（好友）</li>
 *   <li>多对一关系：FriendRelation → ContactGroup（联系人分组）</li>
 *   <li>多对多关系：FriendRelation ↔ ContactTag（联系人标签）</li>
 *   <li>一对多关系：FriendRelation → Message（私聊消息）</li>
 * </ul>
 *
 * <p><strong>实际应用示例</strong>：
 * <pre>
 * // 创建好友关系
 * FriendRelation relation = new FriendRelation();
 * relation.setUserId(currentUserId);
 * relation.setFriendId(friendUserId);
 * relation.setSource(FriendSource.SEARCH);
 * relation.setRemark("工作同事");
 *
 * // 设置隐私权限
 * relation.setMuted(true);
 * relation.setMutedWithExpiry(24); // 免打扰24小时
 * relation.setAllowVideoCall(false);
 *
 * // 更新互动统计
 * relation.incrementMessageCount();
 * relation.incrementCallCount(300); // 通话5分钟
 * relation.updateIntimacyScore();
 * </pre>
 *
 * <p><strong>数据库映射</strong>：
 * 映射到 friend_relations 表，包含用户ID、好友ID的复合唯一约束，以及针对状态、分组、
 * 互动时间等字段的索引，支持高效的好友查询、统计和同步操作。
 *
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Table("friend_relations")
public class FriendRelation {

    @Id
    private Long id;

    // ==================== 基础关系信息 ====================

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 好友ID
     */
    @Column("friend_id")
    private Long friendId;

    /**
     * 好友关系状态
     */
    @Column("status")
    private FriendStatus status = FriendStatus.NORMAL;

    /**
     * 好友来源
     */
    @Column("source")
    private FriendSource source;

    // ==================== 联系人信息 ====================

    /**
     * 好友备注名（支持Telegram式长备注）
     */
    @Column("remark")
    private String remark;

    /**
     * 联系人显示名称（本地自定义）
     */
    @Column("display_name")
    private String displayName;

    /**
     * 联系人电话号码（如果通过电话添加）
     */
    @Column("phone_number")
    private String phoneNumber;

    /**
     * 联系人头像URL（本地缓存）
     */
    @Column("avatar_url")
    private String avatarUrl;

    // ==================== 分组和标签 ====================

    /**
     * 好友分组ID
     */
    @Column("group_id")
    private Long groupId;

    /**
     * 联系人标签（JSON数组格式）
     */
    @Column("tags")
    private String tags;

    /**
     * 自定义标签颜色
     */
    @Column("tag_color")
    private String tagColor;
    
    // ==================== 状态和权限 ====================

    /**
     * 是否置顶
     */
    @Column(nullable = false)
    private Boolean pinned = false;

    /**
     * 是否收藏
     */
    @Column(nullable = false)
    private Boolean favorite = false;

    /**
     * 是否免打扰
     */
    @Column(nullable = false)
    private Boolean muted = false;

    /**
     * 免打扰到期时间
     */
    private Instant mutedUntil;

    /**
     * 是否屏蔽朋友圈/动态
     */
    @Column(nullable = false)
    private Boolean momentBlocked = false;

    /**
     * 是否隐藏在线状态
     */
    @Column(nullable = false)
    private Boolean hideOnlineStatus = false;

    /**
     * 是否允许查看头像
     */
    @Column(nullable = false)
    private Boolean allowViewAvatar = true;

    /**
     * 是否允许语音通话
     */
    @Column(nullable = false)
    private Boolean allowVoiceCall = true;

    /**
     * 是否允许视频通话
     */
    @Column(nullable = false)
    private Boolean allowVideoCall = true;

    /**
     * 是否允许文件传输
     */
    @Column(nullable = false)
    private Boolean allowFileTransfer = true;

    // ==================== 互动统计 ====================

    /**
     * 消息交互次数
     */
    @Column(nullable = false)
    private Long messageCount = 0L;

    /**
     * 通话次数
     */
    @Column(nullable = false)
    private Long callCount = 0L;

    /**
     * 通话总时长（秒）
     */
    @Column(nullable = false)
    private Long callDurationSeconds = 0L;

    /**
     * 最后互动时间
     */
    private Instant lastInteractionAt;

    /**
     * 最后通话时间
     */
    private Instant lastCallAt;

    /**
     * 亲密度评分（0-100）
     */
    @Column(nullable = false)
    private Integer intimacyScore = 0;
    
    // ==================== 同步和扩展信息 ====================

    /**
     * 同步状态
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private SyncStatus syncStatus = SyncStatus.SYNCED;

    /**
     * 联系人来源设备ID
     */
    @Column(length = 100)
    private String sourceDeviceId;

    /**
     * 是否来自通讯录同步
     */
    @Column(nullable = false)
    private Boolean fromAddressBook = false;

    /**
     * 通讯录联系人ID
     */
    @Column(length = 100)
    private String addressBookContactId;

    /**
     * 扩展信息(JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String extra;

    /**
     * 备注信息（私人笔记）
     */
    @Column(columnDefinition = "TEXT")
    private String notes;

    /**
     * 重要日期（生日、纪念日等，JSON格式）
     */
    @Column(columnDefinition = "TEXT")
    private String importantDates;

    // ==================== 时间戳 ====================

    /**
     * 创建时间
     */
    @Column(nullable = false, updatable = false)
    private Instant createdAt = Instant.now();

    /**
     * 更新时间
     */
    @Column(nullable = false)
    private Instant updatedAt = Instant.now();

    /**
     * 最后同步时间
     */
    private Instant lastSyncAt;

    // ==================== 枚举定义 ====================

    /**
     * 好友状态枚举
     */
    public enum FriendStatus {
        NORMAL("正常"),
        BLOCKED("已拉黑"),
        DELETED("已删除");
        
        private final String description;
        
        FriendStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 好友来源枚举
     */
    public enum FriendSource {
        SEARCH("搜索添加"),
        QR_CODE("扫码添加"),
        PHONE("手机号添加"),
        USERNAME("用户名添加"),
        NEARBY("附近的人"),
        GROUP("群组添加"),
        CARD("名片分享"),
        INVITE("邀请链接"),
        RECOMMENDATION("推荐添加"),
        IMPORT("导入添加"),
        MUTUAL_FRIENDS("共同好友"),
        BUSINESS_CARD("商务名片"),
        SOCIAL_MEDIA("社交媒体"),
        EMAIL("邮箱联系人");

        private final String description;

        FriendSource(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SYNCED("已同步"),
        PENDING("待同步"),
        SYNCING("同步中"),
        FAILED("同步失败"),
        CONFLICT("同步冲突"),
        DELETED("已删除");

        private final String description;

        SyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // ==================== 业务方法 ====================

    /**
     * 更新最后互动时间
     */
    public void updateLastInteraction() {
        this.lastInteractionAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        this.messageCount++;
        updateLastInteraction();
    }

    /**
     * 增加通话计数和时长
     */
    public void incrementCallCount(long durationSeconds) {
        this.callCount++;
        this.callDurationSeconds += durationSeconds;
        this.lastCallAt = Instant.now();
        updateLastInteraction();
    }

    /**
     * 更新亲密度评分
     */
    public void updateIntimacyScore() {
        // 基于互动频率、通话时长等计算亲密度
        long daysSinceCreated = java.time.Duration.between(createdAt, Instant.now()).toDays();
        if (daysSinceCreated > 0) {
            double messageFreq = (double) messageCount / daysSinceCreated;
            double callFreq = (double) callCount / daysSinceCreated;

            // 简单的亲密度计算公式
            int score = (int) Math.min(100, (messageFreq * 10 + callFreq * 20));
            this.intimacyScore = Math.max(0, score);
        }
    }

    /**
     * 检查是否为活跃联系人
     */
    public boolean isActiveContact() {
        if (lastInteractionAt == null) return false;

        // 30天内有互动视为活跃
        Instant thirtyDaysAgo = Instant.now().minus(30, java.time.temporal.ChronoUnit.DAYS);
        return lastInteractionAt.isAfter(thirtyDaysAgo);
    }

    /**
     * 检查是否为重要联系人
     */
    public boolean isImportantContact() {
        return favorite || pinned || intimacyScore >= 80;
    }

    /**
     * 设置免打扰（带过期时间）
     */
    public void setMutedWithExpiry(long hours) {
        this.muted = true;
        this.mutedUntil = Instant.now().plus(hours, java.time.temporal.ChronoUnit.HOURS);
        this.updatedAt = Instant.now();
    }

    /**
     * 检查免打扰是否已过期
     */
    public boolean isMuteExpired() {
        if (!muted || mutedUntil == null) return false;
        return Instant.now().isAfter(mutedUntil);
    }

    /**
     * 自动解除过期的免打扰
     */
    public void checkAndClearExpiredMute() {
        if (isMuteExpired()) {
            this.muted = false;
            this.mutedUntil = null;
            this.updatedAt = Instant.now();
        }
    }
}