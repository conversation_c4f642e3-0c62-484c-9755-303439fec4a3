package com.implatform.user.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.List;

/**
 * 云笔记实体类
 * 
 * <AUTHOR> Platform Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "cloud_notes", indexes = {
    @Index(name = "idx_cloud_note_user_id", columnList = "user_id"),
    @Index(name = "idx_cloud_note_category", columnList = "category"),
    @Index(name = "idx_cloud_note_status", columnList = "status"),
    @Index(name = "idx_cloud_note_created_at", columnList = "created_at")
})
@EntityListeners(AuditingEntityListener.class)
public class CloudNote {

    /**
     * 笔记ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 笔记唯一标识
     */
    @Column(name = "note_id", unique = true, nullable = false, length = 32)
    private String noteId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 笔记标题
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 笔记内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * 笔记摘要
     */
    @Column(name = "summary", length = 500)
    private String summary;

    /**
     * 分类
     */
    @Column(name = "category", length = 50)
    private String category;

    /**
     * 标签列表（JSON格式）
     */
    @Column(name = "tags", columnDefinition = "JSON")
    private List<String> tags;

    /**
     * 笔记状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private NoteStatus status = NoteStatus.DRAFT;

    /**
     * 是否公开
     */
    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = false;

    /**
     * 是否收藏
     */
    @Column(name = "is_favorite", nullable = false)
    private Boolean isFavorite = false;

    /**
     * 是否置顶
     */
    @Column(name = "is_pinned", nullable = false)
    private Boolean isPinned = false;

    /**
     * 阅读次数
     */
    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    /**
     * 点赞次数
     */
    @Column(name = "like_count", nullable = false)
    private Long likeCount = 0L;

    /**
     * 分享次数
     */
    @Column(name = "share_count", nullable = false)
    private Long shareCount = 0L;

    /**
     * 字数统计
     */
    @Column(name = "word_count", nullable = false)
    private Integer wordCount = 0;

    /**
     * 预计阅读时间（分钟）
     */
    @Column(name = "reading_time", nullable = false)
    private Integer readingTime = 0;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private Instant createdAt;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 发布时间
     */
    @Column(name = "published_at")
    private Instant publishedAt;

    /**
     * 删除时间（软删除）
     */
    @Column(name = "deleted_at")
    private Instant deletedAt;

    /**
     * 是否激活
     */
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    /**
     * 是否加密
     */
    @Column(name = "is_encrypted", nullable = false)
    private Boolean isEncrypted = false;

    /**
     * 版本号
     */
    @Column(name = "version", nullable = false)
    private Integer version = 1;

    /**
     * 最后查看时间
     */
    @Column(name = "last_viewed_at")
    private Instant lastViewedAt;

    /**
     * 笔记状态枚举
     */
    public enum NoteStatus {
        DRAFT,      // 草稿
        PUBLISHED,  // 已发布
        ARCHIVED,   // 已归档
        DELETED     // 已删除
    }

    /**
     * 检查笔记是否已删除
     */
    public boolean isDeleted() {
        return status == NoteStatus.DELETED || deletedAt != null;
    }

    /**
     * 检查笔记是否已发布
     */
    public boolean isPublished() {
        return status == NoteStatus.PUBLISHED && publishedAt != null;
    }

    /**
     * 软删除笔记
     */
    public void softDelete() {
        this.status = NoteStatus.DELETED;
        this.deletedAt = Instant.now();
    }

    /**
     * 发布笔记
     */
    public void publish() {
        this.status = NoteStatus.PUBLISHED;
        this.publishedAt = Instant.now();
    }

    /**
     * 归档笔记
     */
    public void archive() {
        this.status = NoteStatus.ARCHIVED;
    }

    /**
     * 增加阅读次数
     */
    public void incrementViewCount() {
        this.viewCount++;
    }

    /**
     * 增加点赞次数
     */
    public void incrementLikeCount() {
        this.likeCount++;
    }

    /**
     * 减少点赞次数
     */
    public void decrementLikeCount() {
        if (this.likeCount > 0) {
            this.likeCount--;
        }
    }

    /**
     * 增加分享次数
     */
    public void incrementShareCount() {
        this.shareCount++;
    }

    /**
     * 获取是否激活
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * 设置是否激活
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * 获取是否加密
     */
    public Boolean getIsEncrypted() {
        return isEncrypted;
    }

    /**
     * 设置是否加密
     */
    public void setIsEncrypted(Boolean isEncrypted) {
        this.isEncrypted = isEncrypted;
    }

    /**
     * 获取版本号
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * 设置版本号
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 获取最后查看时间
     */
    public Instant getLastViewedAt() {
        return lastViewedAt;
    }

    /**
     * 设置最后查看时间
     */
    public void setLastViewedAt(Instant lastViewedAt) {
        this.lastViewedAt = lastViewedAt;
    }

    /**
     * 创建Builder
     */
    public static CloudNoteBuilder builder() {
        return new CloudNoteBuilder();
    }

    /**
     * Builder类
     */
    public static class CloudNoteBuilder {
        private CloudNote cloudNote = new CloudNote();

        public CloudNoteBuilder userId(Long userId) {
            cloudNote.userId = userId;
            return this;
        }

        public CloudNoteBuilder title(String title) {
            cloudNote.title = title;
            return this;
        }

        public CloudNoteBuilder content(String content) {
            cloudNote.content = content;
            return this;
        }

        public CloudNoteBuilder category(String category) {
            cloudNote.category = category;
            return this;
        }

        public CloudNoteBuilder tags(java.util.List<String> tags) {
            cloudNote.tags = tags;
            return this;
        }

        public CloudNoteBuilder status(NoteStatus status) {
            cloudNote.status = status;
            return this;
        }

        public CloudNoteBuilder isPublic(Boolean isPublic) {
            cloudNote.isPublic = isPublic;
            return this;
        }

        public CloudNoteBuilder isActive(Boolean isActive) {
            cloudNote.isActive = isActive;
            return this;
        }

        public CloudNote build() {
            return cloudNote;
        }
    }
}
