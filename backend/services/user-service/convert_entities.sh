#!/bin/bash

# 批量转换实体类从 JPA 到 R2DBC 的脚本

ENTITY_DIR="src/main/java/com/implatform/user/entity"

# 需要转换的实体类列表
ENTITIES=(
    "DataSettings.java"
    "ThemeSettings.java"
    "NotificationSettings.java"
    "FriendRequest.java"
    "AuthToken.java"
    "DeviceSession.java"
    "LoginLog.java"
    "PrivacySettings.java"
    "TwoFactorAuth.java"
    "UserBlock.java"
    "UserStatistics.java"
    "VerificationCode.java"
    "SmsVerificationCode.java"
    "QrLoginSession.java"
    "SecurityQuestion.java"
    "UserSecurityAnswer.java"
    "ThirdPartyAccount.java"
    "PhoneContact.java"
    "ContactTag.java"
    "ContactTagRelation.java"
    "UserReport.java"
    "HelpFeedback.java"
    "UserAvatarHistory.java"
    "NicknameChangeHistory.java"
    "UsernameHistory.java"
    "UserExperience.java"
    "UserLevel.java"
    "UserLevelConfig.java"
    "UserLevelReward.java"
    "CloudNote.java"
    "LanguagePack.java"
    "LanguagePackEntry.java"
    "LanguagePackVersion.java"
    "LanguagePackStatistics.java"
    "ThirdPartyConfig.java"
    "AvatarModerationRequest.java"
    "AvatarModerationSettings.java"
    "UsernameSearchIndex.java"
)

echo "开始批量转换实体类..."

for entity in "${ENTITIES[@]}"; do
    file_path="$ENTITY_DIR/$entity"
    
    if [ -f "$file_path" ]; then
        echo "转换 $entity..."
        
        # 备份原文件
        cp "$file_path" "$file_path.backup"
        
        # 执行转换
        sed -i '' '
            # 替换导入语句
            s/import jakarta\.persistence\.\*;//g
            s/import jakarta\.persistence\..*;.*//g
            s/import org\.hibernate\.annotations\.\*;//g
            s/import org\.hibernate\.annotations\..*;.*//g
            s/import org\.springframework\.data\.jpa\.domain\.support\.AuditingEntityListener;//g
            
            # 添加 R2DBC 导入（在 package 行后添加）
            /^package/ a\
\
import org.springframework.data.annotation.*;\
import org.springframework.data.relational.core.mapping.Column;\
import org.springframework.data.relational.core.mapping.Table;
            
            # 替换注解
            s/@Entity//g
            s/@Table(name = "\([^"]*\)".*/@Table("\1")/g
            s/@EntityListeners(AuditingEntityListener\.class)//g
            s/@GeneratedValue(strategy = GenerationType\.IDENTITY)//g
            s/@Enumerated(EnumType\.STRING)//g
            
            # 替换 Column 注解
            s/@Column(name = "\([^"]*\)"[^)]*)/@Column("\1")/g
            s/@Column(nullable = false)/@Column/g
            s/@Column(length = [0-9]*)/@Column/g
            s/@Column(columnDefinition = "[^"]*")/@Column/g
            s/@Column(unique = true)/@Column/g
            s/@Column(updatable = false)/@Column/g
            
            # 替换时间注解
            s/@CreationTimestamp/@CreatedDate/g
            s/@UpdateTimestamp/@LastModifiedDate/g
            
            # 清理空行
            /^[[:space:]]*$/d
        ' "$file_path"
        
        echo "完成转换 $entity"
    else
        echo "文件不存在: $entity"
    fi
done

echo "批量转换完成！"
echo "备份文件已保存为 .backup 后缀"
echo "请检查转换结果并手动调整复杂的注解"
