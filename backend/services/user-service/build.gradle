
dependencies {
    // Common Modules (排除JPA依赖，使用R2DBC)
    implementation project(':common:common-core')
    implementation project(':common:common-webflux')
    implementation project(':common:common-messaging')
    implementation project(':common:common-api')
    implementation project(':common:common-security')
    implementation project(':common:common-protobuf')
    implementation(project(':common:common-data')) {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-jpa'
        exclude group: 'com.zaxxer', module: 'HikariCP'
    }

    // R2DBC Dependencies
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'org.postgresql:r2dbc-postgresql'
    implementation 'org.springframework:spring-r2dbc'

    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'com.xuxueli:xxl-job-core:3.1.0'

    // Rate Limiting
    implementation 'com.github.vladimir-bukhtoyarov:bucket4j-core:7.6.0'
    implementation 'com.github.vladimir-bukhtoyarov:bucket4j-redis:7.6.0'
    
    // Device Detection
    implementation 'eu.bitwalker:UserAgentUtils:1.21'
    
    // IP Geolocation
    implementation 'com.maxmind.geoip2:geoip2:4.0.1'
    
    // Phone number validation
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.26'
    
    // QR Code generation
    implementation 'com.google.zxing:core:3.5.2'
    implementation 'com.google.zxing:javase:3.5.2'

    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'

    // Security and Service Discovery are included via common modules

    // Observability
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'

    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // Servlet API
    implementation 'jakarta.servlet:jakarta.servlet-api'

    // Validation
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'jakarta.validation:jakarta.validation-api'

    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    
}